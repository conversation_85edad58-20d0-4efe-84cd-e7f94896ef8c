import {
  ZoomOutOutlined,
  ZoomInOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { Image, Space, Button } from 'antd';
import { ContentBlock, ContentState } from 'draft-js';
import styled from 'styled-components';

const MediaContainer = styled.div`
  position: relative;
  display: inline-block;
  max-width: 100%;
  margin: 8px 0;
`;

const VideoPlayer = styled.video`
  max-width: 100%;
  height: auto;
  border-radius: 6px;
`;

const DeleteButton = styled(Button)`
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
`;

interface MediaProps {
  block: ContentBlock;
  contentState: ContentState;
  blockProps?: any;
}

export const mediaBlockRendererFn = (block: ContentBlock) => {
  if (block.getType() !== 'atomic') {
    return null;
  }

  const entityKey = block.getEntityAt(0);
  if (!entityKey) {
    return {
      editable: false,
    };
  }

  return {
    component: MediaComponent,
    editable: false,
  };
};

const MediaComponent = ({ block, contentState, blockProps }: MediaProps) => {
  const entityKey = block.getEntityAt(0);
  if (!entityKey) return <></>;

  const entity = contentState.getEntity(entityKey);
  const { src, mediaType } = entity.getData();

  if (!src) return <></>;

  const handleDelete = () => {
    if (blockProps?.onDelete) {
      blockProps.onDelete(block.getKey());
    }
  };

  if (mediaType === 'video' || entity.getType() === 'VIDEO') {
    return (
      <MediaContainer>
        {blockProps?.onDelete && (
          <DeleteButton
            type="primary"
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={handleDelete}
          />
        )}
        <VideoPlayer controls preload="metadata" src={src}>
          Your browser does not support the video tag.
        </VideoPlayer>
      </MediaContainer>
    );
  }

  // Default to image for backward compatibility and IMAGE entities
  return (
    <MediaContainer>
      {blockProps?.onDelete && (
        <DeleteButton
          type="primary"
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={handleDelete}
        />
      )}
      <Image
        src={src}
        preview={{
          toolbarRender: (
            _,
            { transform: { scale }, actions: { onZoomOut, onZoomIn } },
          ) => (
            <Space size={16}>
              <ZoomOutOutlined disabled={scale === 1} onClick={onZoomOut} />
              <ZoomInOutlined disabled={scale === 50} onClick={onZoomIn} />
            </Space>
          ),
        }}
      />
    </MediaContainer>
  );
};

export default MediaComponent;
