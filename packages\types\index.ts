export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  DateTime: any;
  JSONObject: any;
};

export type AIVerification = {
  __typename?: 'AIVerification';
  description?: Maybe<Scalars['String']>;
  reason?: Maybe<Scalars['String']>;
  tokens?: Maybe<TokenUsage>;
  verified?: Maybe<Scalars['Boolean']>;
};

export enum AccountEventVisibility {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC'
}

export enum AccountParticipationStatus {
  INVALID = 'INVALID',
  VALID = 'VALID'
}

export type AirPoolGiveawayData = {
  __typename?: 'AirPoolGiveawayData';
  airPool: BlockchainPool;
  /** Cap */
  amount?: Maybe<Scalars['String']>;
  capped: Scalars['Boolean'];
  guardConfig?: Maybe<TaskGuard>;
  rules?: Maybe<Array<Maybe<GiveawayRule>>>;
  shopConfig?: Maybe<ShopConfig>;
  /** amount per winner */
  winnerAmount?: Maybe<Scalars['String']>;
};

export type AirPoolGiveawayDataInput = {
  /** Cap */
  amount?: InputMaybe<Scalars['String']>;
  capped: Scalars['Boolean'];
  guardConfig?: InputMaybe<TaskGuardInput>;
  rules?: InputMaybe<Array<InputMaybe<GiveawayRuleInput>>>;
  shopConfig?: InputMaybe<ShopConfigInput>;
  /** amount per winner */
  winnerAmount?: InputMaybe<Scalars['String']>;
};

export type AirPoolGiveawayDataUpdateInput = {
  /** Cap */
  amount?: InputMaybe<Scalars['String']>;
  capped?: InputMaybe<Scalars['Boolean']>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  rules?: InputMaybe<Array<GiveawayRuleInput>>;
  shopConfig?: InputMaybe<ShopConfigInput>;
  /** amount per winner */
  winnerAmount?: InputMaybe<Scalars['String']>;
};

export type AirPoolGiveawayInput = {
  airPoolId: Scalars['ID'];
  condition?: GiveawayEventCondition;
  data: AirPoolGiveawayDataInput;
  description?: InputMaybe<Scalars['String']>;
  distributionType: DistributionType;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: Scalars['Boolean'];
  frequency?: InputMaybe<Frequency>;
  giveawayType: GiveawayType;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type AirPoolGiveawayUpdateInput = {
  condition?: InputMaybe<GiveawayEventCondition>;
  data: AirPoolGiveawayDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: InputMaybe<Scalars['Boolean']>;
  frequency?: InputMaybe<Frequency>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<Scalars['String']>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type AirTokenGiveawayData = {
  __typename?: 'AirTokenGiveawayData';
  airToken: BlockchainAsset;
  /** Cap */
  amount?: Maybe<Scalars['String']>;
  capped: Scalars['Boolean'];
  guardConfig?: Maybe<TaskGuard>;
  rules?: Maybe<Array<Maybe<GiveawayRule>>>;
  shopConfig?: Maybe<ShopConfig>;
  /** amount per winner */
  winnerAmount?: Maybe<Scalars['String']>;
};

export type AirTokenGiveawayDataInput = {
  /** Cap */
  amount?: InputMaybe<Scalars['String']>;
  capped: Scalars['Boolean'];
  guardConfig?: InputMaybe<TaskGuardInput>;
  rules?: InputMaybe<Array<InputMaybe<GiveawayRuleInput>>>;
  shopConfig?: InputMaybe<ShopConfigInput>;
  /** amount per winner */
  winnerAmount?: InputMaybe<Scalars['String']>;
};

export type AirTokenGiveawayDataUpdateInput = {
  /** Cap */
  amount?: InputMaybe<Scalars['String']>;
  capped?: InputMaybe<Scalars['Boolean']>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  rules?: InputMaybe<Array<GiveawayRuleInput>>;
  shopConfig?: InputMaybe<ShopConfigInput>;
  /** amount per winner */
  winnerAmount?: InputMaybe<Scalars['String']>;
};

export type AirTokenGiveawayInput = {
  airTokenId: Scalars['ID'];
  condition?: GiveawayEventCondition;
  data: AirTokenGiveawayDataInput;
  description?: InputMaybe<Scalars['String']>;
  distributionType: DistributionType;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: Scalars['Boolean'];
  frequency?: InputMaybe<Frequency>;
  giveawayType: GiveawayType;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type AirTokenGiveawayUpdateInput = {
  condition?: InputMaybe<GiveawayEventCondition>;
  data: AirTokenGiveawayDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: InputMaybe<Scalars['Boolean']>;
  frequency?: InputMaybe<Frequency>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<Scalars['String']>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type AirboostReferralTaskData = {
  __typename?: 'AirboostReferralTaskData';
  createdAt: Scalars['DateTime'];
  max: Scalars['Float'];
  shareBody?: Maybe<Scalars['String']>;
  shareTitle: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type AirboostReferralTaskDataInput = {
  max: Scalars['Float'];
  shareBody?: InputMaybe<Scalars['String']>;
  shareTitle: Scalars['String'];
};

export type AirboostReferralTaskDataUpdateInput = {
  max?: InputMaybe<Scalars['Float']>;
  shareBody?: InputMaybe<Scalars['String']>;
  shareTitle?: InputMaybe<Scalars['String']>;
};

export type AirboostReferralTaskInput = {
  data: AirboostReferralTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type AirboostReferralTaskUpdateInput = {
  data: AirboostReferralTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type AirlyftApp = {
  __typename?: 'AirlyftApp';
  appType: AppType;
  config: AppConfig;
  key?: Maybe<Scalars['String']>;
  tasks: Array<AppTask>;
};

export type AirquestFollowTaskData = {
  __typename?: 'AirquestFollowTaskData';
  createdAt: Scalars['DateTime'];
  projectId: Scalars['String'];
  projectName: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type AirquestFollowTaskDataInput = {
  projectId: Scalars['String'];
  projectName: Scalars['String'];
  url: Scalars['String'];
};

export type AirquestFollowTaskDataUpdateInput = {
  projectId?: InputMaybe<Scalars['String']>;
  projectName?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
};

export type AirquestFollowTaskInput = {
  data: AirquestFollowTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type AirquestFollowTaskUpdateInput = {
  data: AirquestFollowTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type AllProjectsWhereInput = {
  plan?: InputMaybe<PaymentPlan>;
  publicUrl?: InputMaybe<Scalars['String']>;
  supportEnabled?: InputMaybe<Scalars['String']>;
  verified?: InputMaybe<Scalars['String']>;
};

export type ApiKey = {
  __typename?: 'ApiKey';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  key: Scalars['String'];
  projectId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type ApiKeyIntegrationData = {
  __typename?: 'ApiKeyIntegrationData';
  createdAt: Scalars['DateTime'];
  name?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  webhookUrl?: Maybe<Scalars['String']>;
};

export type ApiKeyIntegrationDataInput = {
  /** apiKey used for hard coded integrations like telegram */
  apiKey?: InputMaybe<Scalars['String']>;
  /** arguments used for dynamics API integrations like email subscribtions */
  args?: InputMaybe<Array<Scalars['String']>>;
  name?: InputMaybe<Scalars['String']>;
  webhookUrl?: InputMaybe<Scalars['String']>;
};

export type ApiKeyIntegrationDataUpdateInput = {
  /** apiKey used for hard coded integrations like telegram */
  apiKey?: InputMaybe<Scalars['String']>;
  /** arguments used for dynamics API integrations like email subscribtions */
  args?: InputMaybe<Array<Scalars['String']>>;
  name?: InputMaybe<Scalars['String']>;
  webhookUrl?: InputMaybe<Scalars['String']>;
};

export type ApiKeyIntegrationInput = {
  data: ApiKeyIntegrationDataInput;
  name?: InputMaybe<Scalars['String']>;
  type: IntegrationType;
};

export type ApiKeyIntegrationUpdateInput = {
  data: ApiKeyIntegrationDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type AppConfig = {
  __typename?: 'AppConfig';
  bgColor?: Maybe<Scalars['String']>;
  color: Scalars['String'];
  connectionGuards?: Maybe<Array<Maybe<AuthProvider>>>;
  description: Scalars['String'];
  iconUrl?: Maybe<Scalars['String']>;
  isPremium?: Maybe<Scalars['Boolean']>;
  title: Scalars['String'];
};

export type AppTask = {
  __typename?: 'AppTask';
  config: TaskConfig;
  key?: Maybe<Scalars['String']>;
  renderer: Array<Scalars['String']>;
  taskType: TaskType;
};

export enum AppType {
  AIRBOOST = 'AIRBOOST',
  AIRQUEST = 'AIRQUEST',
  BLOG = 'BLOG',
  CHECKIN = 'CHECKIN',
  DISCORD = 'DISCORD',
  EMAIL = 'EMAIL',
  EVM = 'EVM',
  FAUCET = 'FAUCET',
  FLASHCARD = 'FLASHCARD',
  FORM = 'FORM',
  INSTAGRAM = 'INSTAGRAM',
  KICKSTARTER = 'KICKSTARTER',
  LUCKYDRAW = 'LUCKYDRAW',
  MEDIA = 'MEDIA',
  MOBILE_APP = 'MOBILE_APP',
  PRODUCTHUNT = 'PRODUCTHUNT',
  QUIZ = 'QUIZ',
  REST = 'REST',
  SECRET_CODE = 'SECRET_CODE',
  SUBGRAPH = 'SUBGRAPH',
  SUBQUERY = 'SUBQUERY',
  SUBSOCIAL = 'SUBSOCIAL',
  SUBSQUID = 'SUBSQUID',
  SUBSTRATE = 'SUBSTRATE',
  TELEGRAM = 'TELEGRAM',
  TERMS = 'TERMS',
  TWITTER = 'TWITTER',
  UPLOAD = 'UPLOAD',
  URL = 'URL',
  WALLET = 'WALLET',
  YOUTUBE = 'YOUTUBE'
}

export enum AssetType {
  DOTSAMA_NFT = 'DOTSAMA_NFT',
  DOTSAMA_TOKEN = 'DOTSAMA_TOKEN',
  ERC20 = 'ERC20',
  ERC721 = 'ERC721',
  ERC1155 = 'ERC1155',
  NATIVE = 'NATIVE'
}

export type Auth = {
  __typename?: 'Auth';
  createdAt: Scalars['DateTime'];
  firstName?: Maybe<Scalars['String']>;
  isPrimary: Scalars['Boolean'];
  lastName?: Maybe<Scalars['String']>;
  picture?: Maybe<Scalars['String']>;
  provider: AuthProvider;
  providerId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  userId?: Maybe<Scalars['String']>;
  username?: Maybe<Scalars['String']>;
  verified: Scalars['Boolean'];
};

export enum AuthProvider {
  DISCORD = 'DISCORD',
  DOTSAMA_BLOCKCHAIN = 'DOTSAMA_BLOCKCHAIN',
  EVM_BLOCKCHAIN = 'EVM_BLOCKCHAIN',
  GOOGLE = 'GOOGLE',
  MAGIC_LINK = 'MAGIC_LINK',
  PRODUCTHUNT = 'PRODUCTHUNT',
  TELEGRAM = 'TELEGRAM',
  TWITTER = 'TWITTER'
}

export type AuthSubscriptionResponse = {
  __typename?: 'AuthSubscriptionResponse';
  error?: Maybe<Scalars['String']>;
  token?: Maybe<Scalars['String']>;
};

export type Authorization = {
  __typename?: 'Authorization';
  projectId: Scalars['String'];
  role: Role;
  user: User;
  userId: Scalars['String'];
};

export type Billing = {
  __typename?: 'Billing';
  amount?: Maybe<Scalars['Int']>;
  createdAt: Scalars['DateTime'];
  currency?: Maybe<Scalars['String']>;
  currentPeriodEnd?: Maybe<Scalars['DateTime']>;
  currentPeriodStart?: Maybe<Scalars['DateTime']>;
  gateway: PaymentGateway;
  id: Scalars['ID'];
  isCurrent: Scalars['Boolean'];
  plan?: Maybe<BillingPlan>;
  planId: Scalars['String'];
  startDate?: Maybe<Scalars['DateTime']>;
  status: BillingStatus;
  txInfo?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type BillingDetails = {
  __typename?: 'BillingDetails';
  billing?: Maybe<Billing>;
  entryResetDate?: Maybe<Scalars['DateTime']>;
  maxAiVerificationEntries: Scalars['Float'];
  maxEntries: Scalars['Float'];
  plan: PaymentPlan;
  renewalDate?: Maybe<Scalars['DateTime']>;
};

export type BillingInput = {
  amount: Scalars['Float'];
  currency: Scalars['String'];
  currentPeriodEnd: Scalars['DateTime'];
  currentPeriodStart: Scalars['DateTime'];
  isCurrent: Scalars['Boolean'];
  planId: Scalars['String'];
  projectId: Scalars['String'];
  status: Scalars['String'];
  txInfo?: InputMaybe<Scalars['String']>;
};

export type BillingPlan = {
  __typename?: 'BillingPlan';
  billings?: Maybe<Array<Billing>>;
  createdAt: Scalars['DateTime'];
  currency?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  originalPrice: Scalars['Float'];
  plan: PaymentPlan;
  price: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

/** Billing Payment status */
export enum BillingStatus {
  ACTIVE = 'ACTIVE',
  CANCELED = 'CANCELED',
  INCOMPLETE = 'INCOMPLETE',
  INCOMPLETE_EXPIRED = 'INCOMPLETE_EXPIRED',
  PAST_DUE = 'PAST_DUE',
  PAUSED = 'PAUSED',
  PAYMENT_PROCESSING = 'PAYMENT_PROCESSING',
  TRIALING = 'TRIALING',
  UNPAID = 'UNPAID'
}

export type BillingUpdateInput = {
  amount?: InputMaybe<Scalars['Float']>;
  currency?: InputMaybe<Scalars['String']>;
  currentPeriodEnd?: InputMaybe<Scalars['DateTime']>;
  currentPeriodStart?: InputMaybe<Scalars['DateTime']>;
  isCurrent?: InputMaybe<Scalars['Boolean']>;
  planId?: InputMaybe<Scalars['String']>;
  projectId?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<Scalars['String']>;
  txInfo?: InputMaybe<Scalars['String']>;
};

export type BillingUsingCryptoInput = {
  email: Scalars['String'];
  telegram: Scalars['String'];
  transHash: Scalars['String'];
};

export type Blockchain = {
  __typename?: 'Blockchain';
  apiUrl?: Maybe<Scalars['String']>;
  assets?: Maybe<Array<BlockchainAsset>>;
  blockExplorerUrls: Array<Scalars['String']>;
  chainId: Scalars['Int'];
  color?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  decimals: Scalars['Int'];
  icon: Scalars['String'];
  id: Scalars['ID'];
  name: Scalars['String'];
  nativeCurrency: Scalars['String'];
  order: Scalars['Int'];
  rpcUrls: Array<Scalars['String']>;
  status: BlockchainIntegrationStatus;
  tokenUrls?: Maybe<Array<Maybe<Scalars['String']>>>;
  type: BlockchainType;
  updatedAt: Scalars['DateTime'];
  version?: Maybe<Scalars['Float']>;
  website?: Maybe<Scalars['String']>;
};

export type BlockchainAsset = {
  __typename?: 'BlockchainAsset';
  address: Scalars['String'];
  assetType: AssetType;
  blockchain: Blockchain;
  blockchainId: Scalars['String'];
  color?: Maybe<Scalars['String']>;
  contractAddress?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  createdById: Scalars['String'];
  creatorBlockchainAddress?: Maybe<Scalars['String']>;
  decimals: Scalars['Int'];
  description?: Maybe<Scalars['String']>;
  dotsamaNfts?: Maybe<Array<DotsamaNft>>;
  external?: Maybe<Scalars['Boolean']>;
  icon?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name: Scalars['String'];
  status: BlockchainIntegrationStatus;
  ticker: Scalars['String'];
  tokenId: Scalars['String'];
  txHash?: Maybe<Scalars['String']>;
  txStatus: TransactionStatus;
  updatedAt: Scalars['DateTime'];
  website?: Maybe<Scalars['String']>;
};

export type BlockchainAssetInput = {
  address: Scalars['String'];
  assetType: AssetType;
  blockchainId: Scalars['ID'];
  decimals: Scalars['Int'];
  icon: Scalars['String'];
  name: Scalars['String'];
  status: BlockchainIntegrationStatus;
  ticker: Scalars['String'];
  tokenId: Scalars['String'];
  txStatus: TransactionStatus;
};

export type BlockchainAssetUpdateInput = {
  address?: InputMaybe<Scalars['String']>;
  assetType?: InputMaybe<AssetType>;
  blockchainId?: InputMaybe<Scalars['ID']>;
  decimals?: InputMaybe<Scalars['Int']>;
  icon?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  status?: InputMaybe<BlockchainIntegrationStatus>;
  ticker?: InputMaybe<Scalars['String']>;
  tokenId?: InputMaybe<Scalars['String']>;
  txStatus?: InputMaybe<TransactionStatus>;
};

export type BlockchainAssetWhereInput = {
  assetType?: InputMaybe<Scalars['String']>;
  statusIn?: InputMaybe<Array<InputMaybe<BlockchainIntegrationStatus>>>;
  tokenId?: InputMaybe<Scalars['String']>;
};

export type BlockchainAssetsResponse = {
  __typename?: 'BlockchainAssetsResponse';
  data: Array<BlockchainAsset>;
  total: Scalars['Int'];
};

export type BlockchainInput = {
  blockExplorerUrls: Array<Scalars['String']>;
  chainId: Scalars['Int'];
  decimals: Scalars['Int'];
  icon: Scalars['String'];
  keyPairType?: InputMaybe<KeyPairType>;
  name: Scalars['String'];
  nativeCurrency: Scalars['String'];
  rpcUrls: Array<Scalars['String']>;
  status: BlockchainIntegrationStatus;
  type: BlockchainType;
};

export enum BlockchainIntegrationStatus {
  AIR_TOKEN = 'AIR_TOKEN',
  PENDING = 'PENDING',
  UNVERIFIED = 'UNVERIFIED',
  UPCOMING = 'UPCOMING',
  VERIFIED = 'VERIFIED'
}

export type BlockchainPool = {
  __typename?: 'BlockchainPool';
  amount: Scalars['String'];
  asset: BlockchainAsset;
  contractAddress: Scalars['String'];
  createdAt: Scalars['DateTime'];
  createdById: Scalars['String'];
  creatorBlockchainAddress: Scalars['String'];
  dotsamaNfts?: Maybe<Array<DotsamaNft>>;
  id: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  poolId: Scalars['String'];
  status: TransactionStatus;
  tokenIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  updatedAt: Scalars['DateTime'];
};

export type BlockchainPoolInput = {
  amount: Scalars['String'];
  assetId: Scalars['String'];
  assetType: AssetType;
  blockchainId: Scalars['String'];
  contractAddress: Scalars['String'];
  creatorBlockchainAddress: Scalars['String'];
  name: Scalars['String'];
  tokenIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type BlockchainPoolWhereInput = {
  assetType?: InputMaybe<Scalars['String']>;
  blockchainId?: InputMaybe<Scalars['String']>;
  statusIn?: InputMaybe<Array<InputMaybe<TransactionStatus>>>;
};

export enum BlockchainType {
  DOTSAMA = 'DOTSAMA',
  EVM = 'EVM'
}

export type BlockchainUpdateInput = {
  blockExplorerUrls?: InputMaybe<Array<Scalars['String']>>;
  chainId?: InputMaybe<Scalars['Int']>;
  decimals?: InputMaybe<Scalars['Int']>;
  icon?: InputMaybe<Scalars['String']>;
  keyPairType?: InputMaybe<KeyPairType>;
  name?: InputMaybe<Scalars['String']>;
  nativeCurrency?: InputMaybe<Scalars['String']>;
  rpcUrls?: InputMaybe<Array<Scalars['String']>>;
  status?: InputMaybe<BlockchainIntegrationStatus>;
  type?: InputMaybe<BlockchainType>;
};

export type BlockchainWhereInput = {
  blockchainType?: InputMaybe<BlockchainType>;
  contractType?: InputMaybe<ContractType>;
};

export type BlogCommentParticipationInput = {
  username: Scalars['String'];
};

export type BlogCommentTaskData = {
  __typename?: 'BlogCommentTaskData';
  blogUrl?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
};

export type BlogCommentTaskDataInput = {
  blogUrl?: InputMaybe<Scalars['String']>;
};

export type BlogCommentTaskDataUpdateInput = {
  blogUrl?: InputMaybe<Scalars['String']>;
};

export type BlogCommentTaskInput = {
  data: BlogCommentTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type BlogCommentTaskParticipationData = {
  __typename?: 'BlogCommentTaskParticipationData';
  username: Scalars['String'];
};

export type BlogCommentTaskUpdateInput = {
  data: BlogCommentTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type BlogWriteParticipationInput = {
  blogUrl: Scalars['String'];
};

export type BlogWriteTaskInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type BlogWriteTaskParticipationData = {
  __typename?: 'BlogWriteTaskParticipationData';
  blogUrl: Scalars['String'];
};

export type BlogWriteTaskUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type CertificateResult = {
  __typename?: 'CertificateResult';
  deadline: Scalars['String'];
  nonce: Scalars['String'];
  r: Scalars['String'];
  s: Scalars['String'];
  signature: Scalars['String'];
  type: Scalars['String'];
  v: Scalars['Int'];
};

export type CheckinDailyTaskInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type CheckinDailyTaskUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type Condition = {
  __typename?: 'Condition';
  operator: ConditionOperator;
  value?: Maybe<Scalars['String']>;
  values?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type ConditionInput = {
  operator: ConditionOperator;
  value?: InputMaybe<Scalars['String']>;
  values?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export enum ConditionOperator {
  ARR_LENGTH_EQ = 'ARR_LENGTH_EQ',
  ARR_LENGTH_GT = 'ARR_LENGTH_GT',
  ARR_LENGTH_GTE = 'ARR_LENGTH_GTE',
  ARR_LENGTH_LT = 'ARR_LENGTH_LT',
  ARR_LENGTH_LTE = 'ARR_LENGTH_LTE',
  EQ = 'EQ',
  GT = 'GT',
  GTE = 'GTE',
  IN = 'IN',
  LT = 'LT',
  LTE = 'LTE',
  RANGE = 'RANGE'
}

export type ConflictedUserProfile = {
  __typename?: 'ConflictedUserProfile';
  displayName: Scalars['String'];
  picture?: Maybe<Scalars['String']>;
  providerType: AuthProvider;
};

export type Contract = {
  __typename?: 'Contract';
  address: Scalars['String'];
  blockchain: Blockchain;
  ca: Scalars['String'];
  createdAt: Scalars['DateTime'];
  deployer: Scalars['String'];
  owner: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  user: User;
};

export enum ContractType {
  DOTSAMA_ASSET = 'DOTSAMA_ASSET',
  ERC20_AIRBASE = 'ERC20_AIRBASE',
  ERC20_AIRPOOL = 'ERC20_AIRPOOL',
  ERC721_AIRBASE = 'ERC721_AIRBASE',
  ERC721_AIRPOOL = 'ERC721_AIRPOOL',
  ERC1155_AIRBASE = 'ERC1155_AIRBASE',
  ERC1155_AIRPOOL = 'ERC1155_AIRPOOL'
}

export enum CookieConsent {
  ACCEPT_ALL = 'ACCEPT_ALL',
  NECESSARY = 'NECESSARY'
}

export type CountryStat = {
  __typename?: 'CountryStat';
  count: Scalars['Int'];
  countryCode: Scalars['String'];
  countryName: Scalars['String'];
};

export type CreateAirTokenInput = {
  assetType: AssetType;
  baseURI?: InputMaybe<Scalars['String']>;
  blockchainId: Scalars['String'];
  contractAddress: Scalars['String'];
  creatorBlockchainAddress: Scalars['String'];
  description?: InputMaybe<Scalars['String']>;
  icon: Scalars['String'];
  name: Scalars['String'];
  ticker: Scalars['String'];
  transferable: Scalars['Boolean'];
};

export type CreateAirTokenResult = {
  __typename?: 'CreateAirTokenResult';
  certificate: CertificateResult;
  /** Token Contract address */
  contractAddress: Scalars['String'];
  insertResult: InsertResult;
  /** Token Contract owner */
  owner: Scalars['String'];
  /** Token base uri */
  tokenBaseURI?: Maybe<Scalars['String']>;
};

export type CreateAssetHubAirToken = {
  assetType: AssetType;
  baseURI?: InputMaybe<Scalars['String']>;
  blockchainId: Scalars['String'];
  description?: InputMaybe<Scalars['String']>;
  icon: Scalars['String'];
  name: Scalars['String'];
  ticker: Scalars['String'];
  transferable: Scalars['Boolean'];
};

export type CreateAssetHubAirTokenResult = {
  __typename?: 'CreateAssetHubAirTokenResult';
  assetId: Scalars['String'];
  insertResult: InsertResult;
  txHash: Scalars['String'];
};

export type CreateBlockchainPoolResult = {
  __typename?: 'CreateBlockchainPoolResult';
  certificate: CertificateResult;
  insertResult: InsertResult;
  /** Blockchain pool id */
  poolId: Scalars['String'];
};

export type CreateDotsamaBlockchainPoolResult = {
  __typename?: 'CreateDotsamaBlockchainPoolResult';
  insertResult: InsertResult;
  /** pool id */
  poolId: Scalars['String'];
};

export type CustomTaskData = {
  __typename?: 'CustomTaskData';
  createdAt: Scalars['DateTime'];
  items: Array<FormAnswer>;
  updatedAt: Scalars['DateTime'];
};

export type CustomTaskInput = {
  data: Array<FormAnswerInput>;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type CustomTaskUpdateInput = {
  data: Array<FormAnswerInput>;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type DateInput = {
  end: Scalars['DateTime'];
  start: Scalars['DateTime'];
};

export type DauData = {
  __typename?: 'DauData';
  count: Scalars['Int'];
  date: Scalars['DateTime'];
};

export type DeleteResult = {
  __typename?: 'DeleteResult';
  affected?: Maybe<Scalars['Int']>;
};

export enum DeliveryType {
  APP_EMAIL = 'APP_EMAIL',
  APP_ONLY = 'APP_ONLY'
}

export type DepositBlockchainPoolInput = {
  amount: Scalars['String'];
  assetType: AssetType;
  depositorBlockchainAddress: Scalars['String'];
  id: Scalars['String'];
  tokenIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type DiscordJoinTaskData = {
  __typename?: 'DiscordJoinTaskData';
  createdAt: Scalars['DateTime'];
  guildId: Scalars['String'];
  guildName: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type DiscordJoinTaskDataInput = {
  guildId: Scalars['String'];
  guildName: Scalars['String'];
  url: Scalars['String'];
};

export type DiscordJoinTaskDataUpdateInput = {
  guildId?: InputMaybe<Scalars['String']>;
  guildName?: InputMaybe<Scalars['String']>;
  url?: InputMaybe<Scalars['String']>;
};

export type DiscordJoinTaskInput = {
  data: DiscordJoinTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type DiscordJoinTaskUpdateInput = {
  data: DiscordJoinTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum DisplayType {
  PLAIN_TEXT = 'PLAIN_TEXT',
  QR_CODE = 'QR_CODE'
}

export enum DistributionType {
  FCFS_RANGE = 'FCFS_RANGE',
  FCFS_RANGE_END = 'FCFS_RANGE_END',
  FCFS_TASK_ID = 'FCFS_TASK_ID',
  MANUAL_CONTINUOUS = 'MANUAL_CONTINUOUS',
  MANUAL_END = 'MANUAL_END',
  MANUAL_REC = 'MANUAL_REC',
  RANDOM_END = 'RANDOM_END',
  RANDOM_REC = 'RANDOM_REC',
  RANDOM_SPECIFIC_END = 'RANDOM_SPECIFIC_END',
  RANK_END = 'RANK_END',
  RANK_REC = 'RANK_REC',
  SHOP = 'SHOP'
}

export type DotsamaBlockchainAuthDto = {
  address: Scalars['String'];
  message: Scalars['String'];
  name: Scalars['String'];
  signature: Scalars['String'];
  source: Scalars['String'];
};

export type DotsamaNft = {
  __typename?: 'DotsamaNft';
  blockchainAssetId: Scalars['String'];
  blockchainPoolId: Scalars['String'];
  createdAt: Scalars['DateTime'];
  itemId: Scalars['Int'];
  status: NFTStatus;
  updatedAt: Scalars['DateTime'];
};

export type DotsamaPoolGiveawaySummary = {
  __typename?: 'DotsamaPoolGiveawaySummary';
  amount: Scalars['String'];
  claimedAmount: Scalars['String'];
  eventId: Scalars['String'];
  id: Scalars['String'];
  title: Scalars['String'];
};

export type EVMContractDeployResult = {
  __typename?: 'EVMContractDeployResult';
  address: Scalars['ID'];
  contractType: ContractType;
};

export enum Ecosystem {
  ARBITRUM = 'ARBITRUM',
  ASTAR = 'ASTAR',
  AVAX = 'AVAX',
  BNB = 'BNB',
  CRAB = 'CRAB',
  DOGECHAIN = 'DOGECHAIN',
  ETH = 'ETH',
  EVMOS = 'EVMOS',
  IOTEX = 'IOTEX',
  MOONBEAM = 'MOONBEAM',
  MOONRIVER = 'MOONRIVER',
  POLIMEC = 'POLIMEC',
  POLKADOT = 'POLKADOT',
  POLYGON = 'POLYGON',
  SHARDEUM = 'SHARDEUM',
  SONEIUM = 'SONEIUM',
  TELOS = 'TELOS'
}

export type EmailAddressTaskInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type EmailAddressTaskUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type EmailSubscribeTaskInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type EmailSubscribeTaskUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum EmailVerificationType {
  ANY = 'ANY',
  LIST = 'LIST'
}

export type EmailWhitelistTaskData = {
  __typename?: 'EmailWhitelistTaskData';
  createdAt: Scalars['DateTime'];
  emailList?: Maybe<Array<Scalars['String']>>;
  updatedAt: Scalars['DateTime'];
  verification?: Maybe<EmailVerificationType>;
};

export type EmailWhitelistTaskDataInput = {
  emailList?: InputMaybe<Array<Scalars['String']>>;
  verification?: InputMaybe<EmailVerificationType>;
};

export type EmailWhitelistTaskDataUpdateInput = {
  emailList?: InputMaybe<Array<Scalars['String']>>;
  verification?: InputMaybe<EmailVerificationType>;
};

export type EmailWhitelistTaskInput = {
  data: EmailWhitelistTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type EmailWhitelistTaskUpdateInput = {
  data: EmailWhitelistTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type EventConnection = {
  __typename?: 'EventConnection';
  createdAt: Scalars['DateTime'];
  provider: AuthProvider;
  providerId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type EventConnectionInsertResult = {
  __typename?: 'EventConnectionInsertResult';
  eventId: Scalars['ID'];
  provider: AuthProvider;
  providerId: Scalars['ID'];
  userId: Scalars['ID'];
};

export type EventConnectionUser = {
  __typename?: 'EventConnectionUser';
  auth: Auth;
  avatar?: Maybe<Scalars['String']>;
  cookieConsent?: Maybe<CookieConsent>;
  createdAt: Scalars['DateTime'];
  firstName?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  lastName?: Maybe<Scalars['String']>;
  onboarded?: Maybe<Array<Onboarding>>;
  updatedAt: Scalars['DateTime'];
  username?: Maybe<Scalars['String']>;
};

export type EventParticipantData = {
  __typename?: 'EventParticipantData';
  data?: Maybe<Array<Maybe<ProjectEvent>>>;
  total: Scalars['Float'];
};

export type EventReward = {
  __typename?: 'EventReward';
  createdAt: Scalars['DateTime'];
  giveawayId: Scalars['String'];
  hashedIp?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  info: GiveawayInfoUnion;
  ruleId: Scalars['String'];
  status: RewardStatus;
  txHash?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  wallet?: Maybe<Scalars['String']>;
};

export type EventRewardData = {
  __typename?: 'EventRewardData';
  giveaway: Giveaway;
  giveawayId: Scalars['String'];
  status: RewardStatus;
};

export type EventRewardsResponse = {
  __typename?: 'EventRewardsResponse';
  data: Array<EventRewardData>;
  total: Scalars['Int'];
};

export enum EventState {
  COMMITTED = 'COMMITTED',
  DRAFT = 'DRAFT',
  ONGOING = 'ONGOING',
  READY_TO_SETTLE = 'READY_TO_SETTLE',
  SCHEDULED = 'SCHEDULED',
  SETTLED = 'SETTLED'
}

export type EventTemplate = {
  __typename?: 'EventTemplate';
  bannerUrl?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  displayType: TemplateView;
  ecosystem?: Maybe<Ecosystem>;
  eventId?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  name?: Maybe<Scalars['String']>;
  onboarding?: Maybe<Array<ProjectTemplateOnboardings>>;
  order?: Maybe<Scalars['Int']>;
  tags?: Maybe<Array<TemplateTags>>;
  updatedAt: Scalars['DateTime'];
};

export enum EventType {
  CAMPAIGN = 'CAMPAIGN',
  DEFAULT_QUEST = 'DEFAULT_QUEST',
  E_LEARNING = 'E_LEARNING'
}

export enum EventVisibility {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC',
  PUBLIC_EXPLORABLE = 'PUBLIC_EXPLORABLE'
}

export type EvmBlockchainAuthDto = {
  address: Scalars['String'];
  message: Scalars['String'];
  signature: Scalars['String'];
};

export type EvmContractInteractTaskData = {
  __typename?: 'EvmContractInteractTaskData';
  blockchainId: Scalars['String'];
  condition: Array<FunctionCondition>;
  contractAddress: Scalars['String'];
  createdAt: Scalars['DateTime'];
  function: SmartContractFunction;
  inputParams: Array<Maybe<InputParams>>;
  updatedAt: Scalars['DateTime'];
  verifiedWallet?: Maybe<Scalars['Boolean']>;
};

export type EvmContractInteractTaskDataInput = {
  blockchainId: Scalars['String'];
  condition: Array<FunctionConditionInput>;
  contractAddress: Scalars['String'];
  function: SmartContractFunctionInput;
  inputParams: Array<InputMaybe<FunctionInputParamsInput>>;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type EvmContractInteractTaskDataUpdateInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  condition?: InputMaybe<Array<FunctionConditionInput>>;
  contractAddress?: InputMaybe<Scalars['String']>;
  function?: InputMaybe<SmartContractFunctionInput>;
  inputParams?: InputMaybe<Array<FunctionInputParamsInput>>;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type EvmContractInteractTaskInput = {
  data: EvmContractInteractTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type EvmContractInteractTaskParticipationInput = {
  formResponses?: InputMaybe<Array<FormResponseInput>>;
};

export type EvmContractInteractTaskUpdateInput = {
  data: EvmContractInteractTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type FaucetRawTaskData = {
  __typename?: 'FaucetRawTaskData';
  allocationCSVExists?: Maybe<Scalars['Boolean']>;
  amountPerUser?: Maybe<Scalars['String']>;
  blockchainId: Scalars['String'];
  createdAt: Scalars['DateTime'];
  maxUser?: Maybe<Scalars['Int']>;
  tokenAddress?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type FaucetRawTaskDataInput = {
  allocationCSVExists?: InputMaybe<Scalars['Boolean']>;
  amountPerUser?: InputMaybe<Scalars['String']>;
  blockchainId: Scalars['String'];
  maxUser?: InputMaybe<Scalars['Int']>;
  tokenAddress?: InputMaybe<Scalars['String']>;
};

export type FaucetRawTaskDataUpdateInput = {
  allocationCSVExists?: InputMaybe<Scalars['Boolean']>;
  amountPerUser?: InputMaybe<Scalars['String']>;
  blockchainId?: InputMaybe<Scalars['String']>;
  maxUser?: InputMaybe<Scalars['Int']>;
  tokenAddress?: InputMaybe<Scalars['String']>;
};

export type FaucetRawTaskInput = {
  allocationCSV?: InputMaybe<Array<KeyValueGroupInput>>;
  data: FaucetRawTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  taskType: TaskType;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type FaucetRawTaskUpdateInput = {
  allocationCSV?: InputMaybe<Array<KeyValueGroupInput>>;
  data: FaucetRawTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type FaucetTaskParticipationData = {
  __typename?: 'FaucetTaskParticipationData';
  address: Scalars['String'];
  amount: Scalars['String'];
  hash?: Maybe<Scalars['String']>;
};

export type FlashcardSection = {
  __typename?: 'FlashcardSection';
  content?: Maybe<Scalars['String']>;
  id: Scalars['String'];
  order: Scalars['Int'];
};

export type FlashcardSectionInput = {
  content?: InputMaybe<Scalars['String']>;
  id: Scalars['String'];
  order: Scalars['Int'];
};

export type FlashcardTaskData = {
  __typename?: 'FlashcardTaskData';
  createdAt: Scalars['DateTime'];
  sections?: Maybe<Array<FlashcardSection>>;
  updatedAt: Scalars['DateTime'];
};

export type FlashcardTaskDataInput = {
  sections?: InputMaybe<Array<FlashcardSectionInput>>;
};

export type FlashcardTaskDataUpdateInput = {
  sections?: InputMaybe<Array<FlashcardSectionInput>>;
};

export type FlashcardTaskInput = {
  data?: InputMaybe<FlashcardTaskDataInput>;
  description?: InputMaybe<Scalars['String']>;
  eventId: Scalars['String'];
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  projectId: Scalars['String'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type FlashcardTaskUpdateInput = {
  data?: InputMaybe<FlashcardTaskDataUpdateInput>;
  description?: InputMaybe<Scalars['String']>;
  eventId: Scalars['String'];
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  id: Scalars['String'];
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  projectId: Scalars['String'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type FollowedProject = {
  __typename?: 'FollowedProject';
  project: Project;
  totalPoints: Scalars['Int'];
  totalXp: Scalars['Int'];
};

export type FollowedProjectData = {
  __typename?: 'FollowedProjectData';
  data?: Maybe<Array<Maybe<FollowedProject>>>;
  total: Scalars['Float'];
};

export type FormAnswer = {
  __typename?: 'FormAnswer';
  id: Scalars['String'];
  value?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export type FormAnswerInput = {
  id: Scalars['String'];
  value?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type FormAnswerTaskData = {
  __typename?: 'FormAnswerTaskData';
  hidden?: Maybe<Scalars['Boolean']>;
  id: Scalars['String'];
  order: Scalars['Int'];
  required: Scalars['Boolean'];
  title: Scalars['String'];
  values?: Maybe<Array<FormDataValue>>;
  widget: FormWidgetType;
};

export type FormAnswerTaskDataInput = {
  hidden?: InputMaybe<Scalars['Boolean']>;
  id: Scalars['String'];
  order: Scalars['Int'];
  required: Scalars['Boolean'];
  title: Scalars['String'];
  values?: InputMaybe<Array<FormDataValueInput>>;
  widget: FormWidgetType;
};

export type FormAnswerTaskDataItems = {
  __typename?: 'FormAnswerTaskDataItems';
  createdAt: Scalars['DateTime'];
  items: Array<FormAnswerTaskData>;
  updatedAt: Scalars['DateTime'];
};

export type FormAnswerTaskDataUpdateInput = {
  hidden?: InputMaybe<Scalars['Boolean']>;
  id?: InputMaybe<Scalars['String']>;
  order?: InputMaybe<Scalars['Int']>;
  required?: InputMaybe<Scalars['Boolean']>;
  title?: InputMaybe<Scalars['String']>;
  values?: InputMaybe<Array<FormDataValueInput>>;
  widget?: InputMaybe<FormWidgetType>;
};

export type FormAnswerTaskInput = {
  data: Array<FormAnswerTaskDataInput>;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type FormAnswerTaskParticipationData = {
  __typename?: 'FormAnswerTaskParticipationData';
  answers: Array<FormAnswer>;
};

export type FormAnswerTaskParticipationInput = {
  answers: Array<FormAnswerInput>;
};

export type FormAnswerTaskUpdateInput = {
  data: Array<FormAnswerTaskDataUpdateInput>;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type FormDataValue = {
  __typename?: 'FormDataValue';
  value: Scalars['String'];
};

export type FormDataValueInput = {
  value: Scalars['String'];
};

export type FormResponse = {
  __typename?: 'FormResponse';
  id: Scalars['String'];
  value?: Maybe<Scalars['String']>;
};

export type FormResponseInput = {
  id: Scalars['String'];
  value?: InputMaybe<Scalars['String']>;
};

export enum FormWidgetType {
  AXELAR_ASSET_SELECT = 'AXELAR_ASSET_SELECT',
  AXELAR_CHAIN_SELECT = 'AXELAR_CHAIN_SELECT',
  BIG_NUMBER = 'BIG_NUMBER',
  BLOCKCHAIN_SELECT = 'BLOCKCHAIN_SELECT',
  CHECKBOX = 'CHECKBOX',
  DATE_INPUT = 'DATE_INPUT',
  DOTSAMA_ADDRESS = 'DOTSAMA_ADDRESS',
  EMAIL = 'EMAIL',
  EVM_CONTRACT_ADDRESS = 'EVM_CONTRACT_ADDRESS',
  INPUT = 'INPUT',
  NAME = 'NAME',
  NFT_AMOUNT = 'NFT_AMOUNT',
  NULL = 'NULL',
  NUMBER = 'NUMBER',
  RADIO = 'RADIO',
  SELECT = 'SELECT',
  TEXT = 'TEXT',
  TOKEN_AMOUNT = 'TOKEN_AMOUNT',
  TOKEN_SELECT = 'TOKEN_SELECT',
  WEBSITE = 'WEBSITE',
  WORMHOLE_CHAIN_SELECT = 'WORMHOLE_CHAIN_SELECT'
}

export enum Frequency {
  DAY = 'DAY',
  MONTH = 'MONTH',
  NONE = 'NONE',
  WEEK = 'WEEK',
  YEAR = 'YEAR'
}

export type FunctionCondition = {
  __typename?: 'FunctionCondition';
  condition: Condition;
  t: Scalars['String'];
};

export type FunctionConditionInput = {
  condition: ConditionInput;
  t: Scalars['String'];
};

export type FunctionIO = {
  __typename?: 'FunctionIO';
  components?: Maybe<Array<Maybe<FunctionIO>>>;
  indexed?: Maybe<Scalars['Boolean']>;
  internalType?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  type: Scalars['String'];
};

export type FunctionIOInput = {
  components?: InputMaybe<Array<InputMaybe<FunctionIOInput>>>;
  indexed?: InputMaybe<Scalars['Boolean']>;
  internalType?: InputMaybe<Scalars['String']>;
  name: Scalars['String'];
  type: Scalars['String'];
};

export type FunctionInputParamsInput = {
  id: Scalars['String'];
  order: Scalars['Int'];
  title: Scalars['String'];
  value: Scalars['String'];
  widget: Scalars['String'];
};

export type Giveaway = {
  __typename?: 'Giveaway';
  approved?: Maybe<Scalars['Boolean']>;
  createdAt: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  distributionType: DistributionType;
  endTime?: Maybe<Scalars['DateTime']>;
  eventRelativeWindow: Scalars['Boolean'];
  frequency?: Maybe<Frequency>;
  gasless?: Maybe<Scalars['Boolean']>;
  giveawayType: GiveawayType;
  hasWhitelist: Scalars['Boolean'];
  hidden?: Maybe<Scalars['Boolean']>;
  icon?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  info: GiveawayInfoUnion;
  lastSettledTime?: Maybe<Scalars['DateTime']>;
  /** Indicates the latest review status for manual distribution type giveaways. */
  latestReviewStatus: GiveawayReviewStatus;
  mode: Scalars['Int'];
  startTime?: Maybe<Scalars['DateTime']>;
  title?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  whitelistWallets?: Maybe<Array<Maybe<Scalars['String']>>>;
  winnerCount: Scalars['Int'];
  winningMessage?: Maybe<Scalars['String']>;
};

export enum GiveawayEventCondition {
  AND = 'AND',
  NA = 'NA',
  OR = 'OR'
}

export type GiveawayInfoUnion = AirPoolGiveawayData | AirTokenGiveawayData | MerchandiseGiveawayData | SecretGiveawayData | WhitelistGiveawayData;

export enum GiveawayReviewStatus {
  IN_REVIEW = 'IN_REVIEW',
  NONE = 'NONE',
  REVIEWED = 'REVIEWED'
}

export type GiveawayRule = {
  __typename?: 'GiveawayRule';
  amount: Scalars['String'];
  condition?: Maybe<GiveawayRuleCondition>;
  id: Scalars['String'];
  ids?: Maybe<Array<Maybe<Scalars['String']>>>;
  max?: Maybe<Scalars['Int']>;
  min?: Maybe<Scalars['Int']>;
  ruleType?: Maybe<GiveawayRuleType>;
};

export enum GiveawayRuleCondition {
  AND = 'AND',
  OR = 'OR'
}

export type GiveawayRuleInput = {
  amount: Scalars['String'];
  condition?: InputMaybe<GiveawayRuleCondition>;
  ids?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  max?: InputMaybe<Scalars['Int']>;
  min?: InputMaybe<Scalars['Int']>;
  ruleType?: InputMaybe<GiveawayRuleType>;
};

export enum GiveawayRuleType {
  POINTS = 'POINTS',
  TASK_COUNT = 'TASK_COUNT',
  TASK_ID = 'TASK_ID'
}

export type GiveawaySortInput = {
  direction: SortDirection;
  sortKey: GiveawaySortKey;
};

/** Valid sort keys for the query */
export enum GiveawaySortKey {
  CREATED_AT = 'CREATED_AT',
  FUEL = 'FUEL'
}

export type GiveawaySummary = {
  __typename?: 'GiveawaySummary';
  blockchainId?: Maybe<Scalars['String']>;
  giveawayType: GiveawayType;
  icon?: Maybe<Scalars['String']>;
  title?: Maybe<Scalars['String']>;
};

export enum GiveawaySwapRuleType {
  BURN_NFT = 'BURN_NFT',
  BURN_POINTS = 'BURN_POINTS'
}

export enum GiveawayType {
  AUTO_GENERATED_SECRET = 'AUTO_GENERATED_SECRET',
  COUPON = 'COUPON',
  DOTSAMA_NFT_AIR_POOL = 'DOTSAMA_NFT_AIR_POOL',
  DOTSAMA_NFT_AIR_TOKEN = 'DOTSAMA_NFT_AIR_TOKEN',
  DOTSAMA_TOKEN_AIR_POOL = 'DOTSAMA_TOKEN_AIR_POOL',
  DOTSAMA_TOKEN_AIR_TOKEN = 'DOTSAMA_TOKEN_AIR_TOKEN',
  ERC20_AIR_POOL = 'ERC20_AIR_POOL',
  ERC20_AIR_TOKEN = 'ERC20_AIR_TOKEN',
  ERC721_AIR_POOL = 'ERC721_AIR_POOL',
  ERC721_AIR_TOKEN = 'ERC721_AIR_TOKEN',
  ERC1155_AIR_POOL = 'ERC1155_AIR_POOL',
  ERC1155_AIR_TOKEN = 'ERC1155_AIR_TOKEN',
  MERCHANDISE = 'MERCHANDISE',
  NFT_WHITELIST = 'NFT_WHITELIST',
  PRE_GENERATED_SECRET = 'PRE_GENERATED_SECRET',
  TOKEN_WHITELIST = 'TOKEN_WHITELIST',
  WHITELIST = 'WHITELIST'
}

export type GiveawaysData = {
  __typename?: 'GiveawaysData';
  data?: Maybe<Array<Maybe<Giveaway>>>;
  total: Scalars['Float'];
};

export type GlobalLeaderboardData = {
  __typename?: 'GlobalLeaderboardData';
  data?: Maybe<Array<Maybe<GlobalLeaderboardItem>>>;
  hasMore: Scalars['Boolean'];
  total: Scalars['Float'];
};

export type GlobalLeaderboardItem = {
  __typename?: 'GlobalLeaderboardItem';
  avatar?: Maybe<Scalars['String']>;
  firstName?: Maybe<Scalars['String']>;
  longestStreak: Scalars['Float'];
  offchainPoints: Scalars['Float'];
  offchainQuests: Scalars['Float'];
  offchainXp: Scalars['Float'];
  onchainPoints: Scalars['Float'];
  onchainQuests: Scalars['Float'];
  onchainXp: Scalars['Float'];
  rank: Scalars['Float'];
  referrals: Scalars['Float'];
  totalActivity: Scalars['Float'];
  totalPoints: Scalars['Float'];
  totalProjects: Scalars['Float'];
  totalQuests: Scalars['Float'];
  totalXp: Scalars['Float'];
  userId: Scalars['String'];
  userSince: Scalars['DateTime'];
  username?: Maybe<Scalars['String']>;
};

export type GlobalLeaderboardSortInput = {
  direction: SortDirection;
  sortKey: GlobalLeaderboardSortKey;
};

/** Valid sort keys for the query */
export enum GlobalLeaderboardSortKey {
  LONGEST_STREAK = 'LONGEST_STREAK',
  OFFCHAIN_POINTS = 'OFFCHAIN_POINTS',
  OFFCHAIN_QUESTS = 'OFFCHAIN_QUESTS',
  OFFCHAIN_XP = 'OFFCHAIN_XP',
  ONCHAIN_POINTS = 'ONCHAIN_POINTS',
  ONCHAIN_QUESTS = 'ONCHAIN_QUESTS',
  ONCHAIN_XP = 'ONCHAIN_XP',
  REFERRALS = 'REFERRALS',
  TOTAL_ACTIVITY = 'TOTAL_ACTIVITY',
  TOTAL_POINTS = 'TOTAL_POINTS',
  TOTAL_PROJECTS = 'TOTAL_PROJECTS',
  TOTAL_QUESTS = 'TOTAL_QUESTS',
  TOTAL_XP = 'TOTAL_XP'
}

export type GlobalLeaderboardWhereInput = {
  date?: InputMaybe<DateInput>;
  interval?: InputMaybe<LeaderboardInterval>;
  projectId?: InputMaybe<Scalars['ID']>;
};

export type HSLColor = {
  __typename?: 'HSLColor';
  h: Scalars['Float'];
  l: Scalars['Float'];
  s: Scalars['Float'];
};

export type HSLColorInput = {
  h: Scalars['Float'];
  l: Scalars['Float'];
  s: Scalars['Float'];
};

export type InputParams = {
  __typename?: 'InputParams';
  id: Scalars['String'];
  order: Scalars['Int'];
  title: Scalars['String'];
  value: Scalars['String'];
  widget: Scalars['String'];
};

export type InsertResult = {
  __typename?: 'InsertResult';
  identifiers?: Maybe<Array<Maybe<ObjectLiteral>>>;
};

export type Integration = {
  __typename?: 'Integration';
  auth: Auth;
  createdAt: Scalars['DateTime'];
  /** displayName is a user-friendly name of the integration */
  displayName?: Maybe<Scalars['String']>;
  events?: Maybe<Array<IntegrationEvent>>;
  id: Scalars['ID'];
  info: IntegrationInfoUnion;
  /** Integration Platform Name (enum) */
  name?: Maybe<Scalars['String']>;
  provider?: Maybe<AuthProvider>;
  providerId?: Maybe<Scalars['String']>;
  tasks?: Maybe<Array<IntegrationTask>>;
  type: IntegrationType;
  updatedAt: Scalars['DateTime'];
};

export type IntegrationEvent = {
  __typename?: 'IntegrationEvent';
  createdAt: Scalars['DateTime'];
  eventId: Scalars['String'];
  info?: Maybe<IntegrationInfoUnion>;
  integration: Integration;
  integrationId: Scalars['String'];
  projectId: Scalars['String'];
  routeId?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type IntegrationEventInput = {
  /** Route ID required for zapier integration */
  routeId?: InputMaybe<Scalars['ID']>;
};

export type IntegrationEventUpdateInput = {
  /** New integration ID required for webhook integration */
  newIntegrationId?: InputMaybe<Scalars['ID']>;
  /** Route ID required for zapier integration */
  routeId?: InputMaybe<Scalars['ID']>;
};

export type IntegrationInfoUnion = ApiKeyIntegrationData | NullableTaskData | ZapierIntegrationData;

export enum IntegrationPlatform {
  CONVERTKIT = 'CONVERTKIT',
  CUSTOMERIO = 'CUSTOMERIO',
  KLAVIYO = 'KLAVIYO',
  MAILCHIMP = 'MAILCHIMP',
  MAILERLITE = 'MAILERLITE',
  MAILJET = 'MAILJET',
  MARKETO = 'MARKETO',
  SENDGRID = 'SENDGRID',
  SENDINBLUE = 'SENDINBLUE',
  SHOPIFY = 'SHOPIFY',
  TELEGRAM_BOT = 'TELEGRAM_BOT',
  WEBHOOKS = 'WEBHOOKS',
  ZAPIER = 'ZAPIER'
}

export type IntegrationPlatformConfig = {
  __typename?: 'IntegrationPlatformConfig';
  bgColor: Scalars['String'];
  color: Scalars['String'];
  description: Scalars['String'];
  fields?: Maybe<Array<PlatformField>>;
  name: Scalars['String'];
  platformType: IntegrationPlatform;
};

export type IntegrationTask = {
  __typename?: 'IntegrationTask';
  createdAt: Scalars['DateTime'];
  info?: Maybe<IntegrationInfoUnion>;
  integration: Integration;
  integrationId: Scalars['String'];
  routeId?: Maybe<Scalars['String']>;
  task: Task;
  taskId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type IntegrationTaskInput = {
  integrationId: Scalars['String'];
  routeId?: InputMaybe<Scalars['String']>;
};

export enum IntegrationType {
  API_KEY_INTEGRATION = 'API_KEY_INTEGRATION',
  WEBHOOKS_INTEGRATION = 'WEBHOOKS_INTEGRATION',
  ZAPIER_INTEGRATION = 'ZAPIER_INTEGRATION'
}

export type Invitation = {
  __typename?: 'Invitation';
  createdAt: Scalars['DateTime'];
  createdById?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  invitationCode: Scalars['String'];
  isUsed: Scalars['Boolean'];
  project: Project;
  projectId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export enum KeyPairType {
  ECDSA = 'ECDSA',
  ED25519 = 'ED25519',
  ETHEREUM = 'ETHEREUM',
  SR25519 = 'SR25519'
}

export type KeyValueGroup = {
  __typename?: 'KeyValueGroup';
  createdAt: Scalars['DateTime'];
  groupId: Scalars['String'];
  key: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  value: Scalars['String'];
};

export type KeyValueGroupInput = {
  groupId?: InputMaybe<Scalars['String']>;
  key: Scalars['String'];
  value?: InputMaybe<Scalars['String']>;
};

export type KickstarterTaskData = {
  __typename?: 'KickstarterTaskData';
  createdAt: Scalars['DateTime'];
  projectUrl: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type KickstarterTaskDataInput = {
  projectUrl: Scalars['String'];
};

export type KickstarterTaskDataUpdateInput = {
  projectUrl?: InputMaybe<Scalars['String']>;
};

export type KickstarterTaskInput = {
  data: KickstarterTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type KickstarterTaskUpdateInput = {
  data: KickstarterTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type LeaderBoardWhereInput = {
  eventId?: InputMaybe<Scalars['ID']>;
  interval?: InputMaybe<LeaderboardInterval>;
  seasonId?: InputMaybe<Scalars['ID']>;
};

export type LeaderboardData = {
  __typename?: 'LeaderboardData';
  data?: Maybe<Array<Maybe<LeaderboardItem>>>;
  hasMore: Scalars['Boolean'];
  total: Scalars['Float'];
};

export enum LeaderboardInterval {
  ALL_TIME = 'ALL_TIME',
  MONTH = 'MONTH',
  WEEK = 'WEEK'
}

export type LeaderboardItem = {
  __typename?: 'LeaderboardItem';
  rank: Scalars['Int'];
  user: User;
  xp: Scalars['Int'];
};

export enum LeaderboardType {
  EVENT = 'EVENT',
  NONE = 'NONE',
  SEASON = 'SEASON'
}

export type LinkTaskData = {
  __typename?: 'LinkTaskData';
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type LinkTaskDataInput = {
  url: Scalars['String'];
};

export type LinkTaskDataUpdateInput = {
  url?: InputMaybe<Scalars['String']>;
};

export type LinkTaskInput = {
  appType: AppType;
  data: LinkTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  taskType: TaskType;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type LinkTaskUpdateInput = {
  data: LinkTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type LoginToken = {
  __typename?: 'LoginToken';
  token: Scalars['String'];
};

export type LuckydrawReward = {
  __typename?: 'LuckydrawReward';
  amount: Scalars['Int'];
  probability: Scalars['Int'];
};

export type LuckydrawRewardInput = {
  amount: Scalars['Int'];
  probability: Scalars['Int'];
};

export type LuckydrawTaskData = {
  __typename?: 'LuckydrawTaskData';
  createdAt: Scalars['DateTime'];
  luckydrawType: LuckydrawType;
  rewardType: RewardType;
  rewards: Array<LuckydrawReward>;
  slotMachineIcons?: Maybe<Array<Scalars['String']>>;
  updatedAt: Scalars['DateTime'];
};

export type LuckydrawTaskDataInput = {
  luckydrawType: LuckydrawType;
  rewardType: RewardType;
  rewards: Array<LuckydrawRewardInput>;
  slotMachineIcons?: InputMaybe<Array<Scalars['String']>>;
};

export type LuckydrawTaskDataUpdateInput = {
  luckydrawType?: InputMaybe<LuckydrawType>;
  rewardType?: InputMaybe<RewardType>;
  rewards?: InputMaybe<Array<LuckydrawRewardInput>>;
  slotMachineIcons?: InputMaybe<Array<Scalars['String']>>;
};

export type LuckydrawTaskInput = {
  data: LuckydrawTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  taskType: TaskType;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type LuckydrawTaskParticipationData = {
  __typename?: 'LuckydrawTaskParticipationData';
  luckydrawType: LuckydrawType;
  resultIndex: Scalars['Int'];
  rewardType: RewardType;
};

export type LuckydrawTaskParticipationResult = {
  __typename?: 'LuckydrawTaskParticipationResult';
  insertResult: InsertResult;
  points: Scalars['Int'];
  resultIndex: Scalars['Int'];
  xp: Scalars['Int'];
};

export type LuckydrawTaskUpdateInput = {
  data: LuckydrawTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum LuckydrawType {
  MYSTERYBOX = 'MYSTERYBOX',
  SLOTMACHINE = 'SLOTMACHINE',
  SPINWHEEL = 'SPINWHEEL'
}

export type MauData = {
  __typename?: 'MauData';
  count: Scalars['Int'];
  date: Scalars['DateTime'];
};

export type MerchandiseGiveawayData = {
  __typename?: 'MerchandiseGiveawayData';
  claimed?: Maybe<Scalars['Float']>;
  description?: Maybe<Scalars['String']>;
  formItems: Array<FormAnswerTaskData>;
  guardConfig?: Maybe<TaskGuard>;
  reward: Scalars['String'];
  rewardAmount: Scalars['Float'];
  shopConfig?: Maybe<ShopConfig>;
};

export type MerchandiseGiveawayDataInput = {
  description?: InputMaybe<Scalars['String']>;
  formItems: Array<FormAnswerTaskDataInput>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  reward: Scalars['String'];
  rewardAmount: Scalars['Float'];
  shopConfig?: InputMaybe<ShopConfigInput>;
};

export type MerchandiseGiveawayDataUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  formItems?: InputMaybe<Array<FormAnswerTaskDataInput>>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  reward?: InputMaybe<Scalars['String']>;
  rewardAmount?: InputMaybe<Scalars['Float']>;
  shopConfig?: InputMaybe<ShopConfigInput>;
};

export type MerchandiseGiveawayInput = {
  condition?: GiveawayEventCondition;
  data: MerchandiseGiveawayDataInput;
  description?: InputMaybe<Scalars['String']>;
  distributionType: DistributionType;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: Scalars['Boolean'];
  frequency?: InputMaybe<Frequency>;
  giveawayType: GiveawayType;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type MerchandiseGiveawayUpdateInput = {
  condition?: InputMaybe<GiveawayEventCondition>;
  data: MerchandiseGiveawayDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: InputMaybe<Scalars['Boolean']>;
  frequency?: InputMaybe<Frequency>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<Scalars['String']>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type MobileAppParticipationInput = {
  url: Scalars['String'];
};

export type MobileAppTaskData = {
  __typename?: 'MobileAppTaskData';
  appName?: Maybe<Scalars['String']>;
  appStoreUrl?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  playStoreUrl?: Maybe<Scalars['String']>;
  prompt?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type MobileAppTaskDataInput = {
  appName?: InputMaybe<Scalars['String']>;
  appStoreUrl?: InputMaybe<Scalars['String']>;
  playStoreUrl?: InputMaybe<Scalars['String']>;
  prompt?: InputMaybe<Scalars['String']>;
};

export type MobileAppTaskDataUpdateInput = {
  appName?: InputMaybe<Scalars['String']>;
  appStoreUrl?: InputMaybe<Scalars['String']>;
  playStoreUrl?: InputMaybe<Scalars['String']>;
  prompt?: InputMaybe<Scalars['String']>;
};

export type MobileAppTaskInput = {
  data: MobileAppTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type MobileAppTaskParticipationData = {
  __typename?: 'MobileAppTaskParticipationData';
  aiVerification?: Maybe<AIVerification>;
  url: Scalars['String'];
};

export type MobileAppTaskUpdateInput = {
  data: MobileAppTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  /** Internal Use Only - Whitelist AirToken */
  addAirToken: Scalars['String'];
  /** Internal Use Only - Add a new blockchain on the platform */
  addBlockchain: Blockchain;
  /** Internal Use Only - Add a new blockchain asset on the platform */
  addBlockchainAsset: BlockchainAsset;
  addEventPromotion: PromotedEvent;
  addProjectPromotion: PromotedProject;
  /** Replace all winners by input list */
  addWinners: InsertResult;
  billingUsingCrypto: UpdateResult;
  cancelBilling?: Maybe<UpdateResult>;
  claimAirPoolGiveaway: RewardCertificate;
  claimAirTokenGiveaway: RewardCertificate;
  claimDotsamaAirPoolGiveaway: RewardClaimResult;
  claimDotsamaAirTokenGiveaway: RewardCertificate;
  claimMerchandiseGiveaway: Array<EventReward>;
  /** Clear all notifications of the logged in user */
  clearAllNotifications: DeleteResult;
  /** Remove all winner items */
  clearWinners: DeleteResult;
  /** Clone an event */
  cloneEvent: InsertResult;
  /** Clone a task */
  cloneTask: Scalars['ID'];
  completeFlashcardViewTask: Scalars['Boolean'];
  /** Convert event to a reusable template */
  convertToTemplate?: Maybe<UpdateResult>;
  createAirPoolGiveaway: InsertResult;
  /** Create AirToken */
  createAirToken: CreateAirTokenResult;
  /** Mark AirToken creation failure */
  createAirTokenFailed: TransactionStatus;
  createAirTokenGiveaway: InsertResult;
  createAirboostReferralTask: Scalars['ID'];
  createAirquestFollowTask: Scalars['ID'];
  /** Create an API Key integration */
  createApiKeyIntegration: InsertResult;
  /** Create AssetHub AirToken */
  createAssetHubAirToken: CreateAssetHubAirTokenResult;
  createBilling: Billing;
  /** Create a blockchain pool for the user */
  createBlockchainPool: CreateBlockchainPoolResult;
  createBlogCommentTask: Scalars['ID'];
  createBlogWriteTask: Scalars['ID'];
  createCheckinTask: Scalars['ID'];
  createCheckoutSession: Scalars['String'];
  createCustomTask: Scalars['ID'];
  createDiscordJoinTask: Scalars['ID'];
  /** Create a dotsama blockchain pool for the user */
  createDotsamaBlockchainPool: CreateDotsamaBlockchainPoolResult;
  createEmailAddressTask: Scalars['ID'];
  createEmailSubscribeTask: Scalars['ID'];
  createEmailWhitelistTask: Scalars['ID'];
  createEventConnection: EventConnectionInsertResult;
  createEvmContractInteractTask: Scalars['ID'];
  createFaucetRawTask: Scalars['ID'];
  createFlashcardTask: Scalars['ID'];
  createFormAnswerTask: Scalars['ID'];
  /** Create event from template */
  createFromTemplate?: Maybe<InsertResult>;
  /** Create a global referral code */
  createGlobalReferralCode: ReferralCode;
  /** Create integration for the given event */
  createIntegrationEvent: IntegrationEvent;
  /** Create an invitations for your team members to join your project */
  createInvitation: Invitation;
  createKickstarterTask: Scalars['ID'];
  createLinkTask: Scalars['ID'];
  createLuckydrawTask: Scalars['ID'];
  createMerchandiseGiveaway: InsertResult;
  createMobileAppTask: Scalars['ID'];
  /** Get the questboard event for project */
  createOrReturnDefaultEvent: ProjectEvent;
  createProducthuntFollowTask: Scalars['ID'];
  createProducthuntUpvoteTask: Scalars['ID'];
  /** Create new Project */
  createProject: InsertResult;
  /** Create a project event. Events are always created in a DRAFT state */
  createProjectEvent: InsertResult;
  /** Create new Project Url */
  createProjectUrl: InsertResult;
  /** Create Project Webhook secret */
  createProjectwebhookSecret: Scalars['String'];
  createQuizPlayTask: Scalars['ID'];
  createRestRawTask: Scalars['ID'];
  /** Create Season */
  createSeason: Season;
  createSecretCodeTask: Scalars['ID'];
  createSecretGiveaway: InsertResult;
  createSignTermsTask: Scalars['ID'];
  createSubgraphRawTask: Scalars['ID'];
  createSubsocialCommentTask: Scalars['ID'];
  createSubsocialFollowTask: Scalars['ID'];
  createSubsocialPostTask: Scalars['ID'];
  createSubsocialProfileTask: Scalars['ID'];
  createSubsocialShareTask: Scalars['ID'];
  createSubsocialSpaceTask: Scalars['ID'];
  createSubsocialUpvoteTask: Scalars['ID'];
  createSubstrateQueryTask: Scalars['ID'];
  createTelegramJoinTask: Scalars['ID'];
  createTwitterFollowTask: Scalars['ID'];
  createTwitterLikeRetweetTask: Scalars['ID'];
  createTwitterLikeTask: Scalars['ID'];
  createTwitterPostTask: Scalars['ID'];
  createTwitterRetweetTask: Scalars['ID'];
  createTwitterUgcTask: Scalars['ID'];
  createTwitterWhitelistTask: Scalars['ID'];
  createUploadTask: Scalars['ID'];
  createWalletAddressTask: Scalars['ID'];
  createWhitelistGiveaway: InsertResult;
  /** Create Widget */
  createWidget: InsertResult;
  /** Create webhook */
  createZapierWebhook: ZapierWebhookData;
  /** Delete AirToken */
  deleteAirToken: DeleteResult;
  /** Internal Use Only - Delete a blockchain from the platform */
  deleteBlockchain: DeleteResult;
  /** Internal Use Only - Delete a blockchain asset from the platform */
  deleteBlockchainAsset: DeleteResult;
  /** Delete a failed Blockchain Pool */
  deleteBlockchainPool: DeleteResult;
  deleteEventPromotion: DeleteResult;
  /** Delete a giveaway */
  deleteGiveaway: DeleteResult;
  /** Delete a global referral code */
  deleteGlobalReferralCode: DeleteResult;
  /** Delete an integration */
  deleteIntegration: DeleteResult;
  /** Remove integration for the given eventId */
  deleteIntegrationEvent: DeleteResult;
  /** Remove integration for the given taskId */
  deleteIntegrationTask: DeleteResult;
  /** Delete logged-in user account */
  deleteMe: Scalars['ID'];
  /** Delete a project created by logged in user */
  deleteProject: Scalars['ID'];
  /** Delete an event */
  deleteProjectEvent: DeleteResult;
  /** Add or update a project member role */
  deleteProjectMember?: Maybe<DeleteResult>;
  deleteProjectPromotion: DeleteResult;
  /** Delete a project url created by logged in user */
  deleteProjectUrl: DeleteResult;
  /** Delete a task */
  deleteTask: DeleteResult;
  /** Delete a specific task participation entry */
  deleteTaskParticipation: DeleteResult;
  /** Delete Widget */
  deleteWidget: DeleteResult;
  /** Remove single winner item */
  deleteWinner: DeleteResult;
  /** Delete webhook */
  deleteZapierWebhook: Scalars['Boolean'];
  /** Internal Use Only - Deploy AirLyft EVM contracts to Blockchain */
  deployContracts: Array<EVMContractDeployResult>;
  /** Deposit fund */
  depositBlockchainPool: CertificateResult;
  /** Login using EVM wallet signature */
  evmLogin: LoginToken;
  /** Create project following by the logged in user */
  follow: InsertResult;
  /** Internal Use Only - Generate API key for project */
  generateApiKey: ApiKey;
  /** Generate Zapier API key */
  generateZapierApiKey: InsertResult;
  /** Update hidden flag in giveaway */
  hideGiveaway: UpdateResult;
  /** Hide task */
  hideTask: UpdateResult;
  /** Mark notification as read */
  markReadNotification: UpdateResult;
  notifyTelegramConnection?: Maybe<Scalars['Boolean']>;
  participateAirquestFollowTask: InsertResult;
  participateBlogCommentTask: InsertResult;
  participateBlogWriteTask: InsertResult;
  participateCheckinTask: InsertResult;
  participateDiscordJoinTask: InsertResult;
  participateDotsamaFaucetRawTask: InsertResult;
  participateDotsamaRestRawTask: InsertResult;
  participateDotsamaSignTermsTask: InsertResult;
  participateDotsamaSubgraphRawTask: InsertResult;
  participateDotsamaWalletAddressTask: InsertResult;
  participateEVMSignTermsTask: InsertResult;
  participateEmailAddressTask: InsertResult;
  participateEmailSubscribeTask: InsertResult;
  participateEmailWhitelistTask: InsertResult;
  participateEvmContractInteractTask: InsertResult;
  participateEvmFaucetRawTask: InsertResult;
  participateEvmRestRawTask: InsertResult;
  participateFormAnswerTask: InsertResult;
  participateKickstarterTask: InsertResult;
  participateLinkTask: InsertResult;
  participateLuckydrawTask: LuckydrawTaskParticipationResult;
  participateMobileAppTask: InsertResult;
  participateProducthuntFollowTask: InsertResult;
  participateProducthuntUpvoteTask: InsertResult;
  participateQuizPlayTask: QuizTaskParticipationResult;
  participateRestRawTask: InsertResult;
  participateSecretCodeTask: InsertResult;
  participateSubgraphRawTask: InsertResult;
  participateSubsocialCommentTask: InsertResult;
  participateSubsocialFollowTask: InsertResult;
  participateSubsocialPostTask: InsertResult;
  participateSubsocialProfileTask: InsertResult;
  participateSubsocialShareTask: InsertResult;
  participateSubsocialSpaceTask: InsertResult;
  participateSubsocialUpvoteTask: InsertResult;
  participateSubstrateQueryTask: InsertResult;
  participateTelegramJoinTask: InsertResult;
  participateTextSignTermsTask: InsertResult;
  participateTwitterFollowTask: InsertResult;
  participateTwitterLikeRetweetTask: InsertResult;
  participateTwitterLikeTask: InsertResult;
  participateTwitterPostTask: InsertResult;
  participateTwitterRetweetTask: InsertResult;
  participateTwitterUgcTask: InsertResult;
  participateTwitterWhitelistTask: InsertResult;
  participateUploadTask: InsertResult;
  participateWalletAddressTask: InsertResult;
  /** Login using polkadot wallet signature */
  polkadotLogin: LoginToken;
  /** Add or update a project member role */
  projectMember?: Maybe<Scalars['Boolean']>;
  /** Publish event */
  publishProjectEvent?: Maybe<UpdateResult>;
  /** Create or get a referral code linked to a particular task */
  referralCode: Scalars['String'];
  refreshWalletDotsamaNftItems: Array<DotsamaNft>;
  /** Change task ordering */
  reorderTask: UpdateResult;
  /** Revoke project access */
  revokeProjectAccess: UpdateResult;
  /** Submit final winners after review */
  settleGiveaway: Scalars['Boolean'];
  /** Submit global referral code */
  submitGlobalReferralCode: Scalars['Boolean'];
  superAdminProjectAuthorization: Scalars['Boolean'];
  /** Sync state */
  syncAirToken: TransactionStatus;
  /** Sync state */
  syncBlockchainPool: SyncBlockchainPoolResult;
  /** Sync claim */
  syncClaimAirPoolGiveaway: TransactionStatus;
  /** Sync claim */
  syncClaimAirTokenGiveaway: TransactionStatus;
  /** Unfollow project by the logged in user */
  unfollow: DeleteResult;
  updateAirPoolGiveaway: UpdateResult;
  updateAirTokenGiveaway: UpdateResult;
  updateAirboostReferralTask: Scalars['Boolean'];
  updateAirquestFollowTask: Scalars['Boolean'];
  /** Update API key integration */
  updateApiKeyIntegration: Scalars['Boolean'];
  updateBilling: UpdateResult;
  /** Internal Use Only - Update an existing blockchain on the platform */
  updateBlockchain: UpdateResult;
  /** Internal Use Only - Update an existing blockchain asset on the platform */
  updateBlockchainAsset: UpdateResult;
  updateBlogCommentTask: Scalars['Boolean'];
  updateBlogWriteTask: Scalars['Boolean'];
  updateCheckinTask: Scalars['Boolean'];
  updateCustomTask: Scalars['Boolean'];
  updateDiscordJoinTask: Scalars['Boolean'];
  updateEmailAddressTask: Scalars['Boolean'];
  updateEmailSubscribeTask: Scalars['Boolean'];
  updateEmailWhitelistTask: Scalars['Boolean'];
  updateEventPromotion: UpdateResult;
  updateEvmContractInteractTask: Scalars['Boolean'];
  updateFaucetRawTask: Scalars['Boolean'];
  updateFlashcardTask: Scalars['Boolean'];
  updateFormAnswerTask: Scalars['Boolean'];
  /** Update a global referral code */
  updateGlobalReferralCode: UpdateResult;
  /** Update integration for the given event */
  updateIntegrationEvent: IntegrationEvent;
  updateKickstarterTask: Scalars['Boolean'];
  updateLinkTask: Scalars['Boolean'];
  updateLuckydrawTask: Scalars['Boolean'];
  /** Update logged-in user details */
  updateMe: UpdateResult;
  updateMerchandiseGiveaway: UpdateResult;
  updateMobileAppTask: Scalars['Boolean'];
  updateProducthuntFollowTask: Scalars['Boolean'];
  updateProducthuntUpvoteTask: Scalars['Boolean'];
  /** Update a project created by logged in user */
  updateProject: UpdateResult;
  /** Update a project event */
  updateProjectEvent: UpdateResult;
  updateProjectPromotion: UpdateResult;
  /** Mark completion status of an onboarding template by the project */
  updateProjectTemplateOnboardingStatus: Scalars['Boolean'];
  /** Update a project url created by logged in user */
  updateProjectUrl: UpdateResult;
  /** Internal Use Only - Update a project visibility and verified status */
  updateProjectVisibility: UpdateResult;
  updateQuizPlayTask: Scalars['Boolean'];
  /** Update rejection status for a user referral */
  updateRejectedStatus: TaskParticipation;
  updateRestRawTask: Scalars['Boolean'];
  updateSecretCodeTask: Scalars['Boolean'];
  updateSecretGiveaway: UpdateResult;
  updateSignTermsTask: Scalars['Boolean'];
  updateSubgraphRawTask: Scalars['Boolean'];
  updateSubsocialCommentTask: Scalars['Boolean'];
  updateSubsocialFollowTask: Scalars['Boolean'];
  updateSubsocialPostTask: Scalars['Boolean'];
  updateSubsocialProfileTask: Scalars['Boolean'];
  updateSubsocialShareTask: Scalars['Boolean'];
  updateSubsocialSpaceTask: Scalars['Boolean'];
  updateSubsocialUpvoteTask: Scalars['Boolean'];
  updateSubstrateQueryTask: Scalars['Boolean'];
  /** Modify task participation status */
  updateTaskParticipationStatus: UpdateResult;
  updateTelegramJoinTask: Scalars['Boolean'];
  updateTwitterFollowTask: Scalars['Boolean'];
  updateTwitterLikeRetweetTask: Scalars['Boolean'];
  updateTwitterLikeTask: Scalars['Boolean'];
  updateTwitterPostTask: Scalars['Boolean'];
  updateTwitterRetweetTask: Scalars['Boolean'];
  updateTwitterUgcTask: Scalars['Boolean'];
  updateTwitterWhitelistTask: Scalars['Boolean'];
  updateUploadTask: Scalars['Boolean'];
  /** Update User Cookie Consent */
  updateUserCookieConsent?: Maybe<UpdateResult>;
  /** Update User Onboarding Apps */
  updateUserOnboardingApp?: Maybe<UpdateResult>;
  updateWalletAddressTask: Scalars['Boolean'];
  updateWhitelistGiveaway: UpdateResult;
  /** Update Widget */
  updateWidget: UpdateResult;
  /** Update webhook */
  updateZapierWebhook: Scalars['Boolean'];
  /** Verify invite code */
  verifyInvitation?: Maybe<Invitation>;
  /** Withdraw fund */
  withdrawBlockchainPool: CertificateResult;
  /** Withdraw fund */
  withdrawDotsamaBlockchainPool: WithdrawBlockchainPoolOutput;
};


export type Mutation_addAirTokenArgs = {
  blockchainId: Scalars['ID'];
  contractAddress: Scalars['String'];
  creator: Scalars['String'];
  isExternal: Scalars['Boolean'];
  owner: Scalars['String'];
  tokenAddress: Scalars['String'];
};


export type Mutation_addBlockchainArgs = {
  data: BlockchainInput;
};


export type Mutation_addBlockchainAssetArgs = {
  data: BlockchainAssetInput;
};


export type Mutation_addEventPromotionArgs = {
  promotionType: Scalars['String'];
  url: Scalars['String'];
};


export type Mutation_addProjectPromotionArgs = {
  promotionType: Scalars['String'];
  url: Scalars['String'];
};


export type Mutation_addWinnersArgs = {
  data: Array<WinnerInput>;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_billingUsingCryptoArgs = {
  data: BillingUsingCryptoInput;
  planId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_cancelBillingArgs = {
  billingId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_claimAirPoolGiveawayArgs = {
  captcha?: InputMaybe<Scalars['String']>;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
  userAddress: Scalars['String'];
};


export type Mutation_claimAirTokenGiveawayArgs = {
  captcha?: InputMaybe<Scalars['String']>;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
  userAddress: Scalars['String'];
};


export type Mutation_claimDotsamaAirPoolGiveawayArgs = {
  captcha?: InputMaybe<Scalars['String']>;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
  userAddress: Scalars['String'];
};


export type Mutation_claimDotsamaAirTokenGiveawayArgs = {
  captcha?: InputMaybe<Scalars['String']>;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
  userAddress: Scalars['String'];
};


export type Mutation_claimMerchandiseGiveawayArgs = {
  data: FormAnswerTaskParticipationInput;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_clearWinnersArgs = {
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_cloneEventArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_cloneTaskArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_completeFlashcardViewTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_convertToTemplateArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createAirPoolGiveawayArgs = {
  data: AirPoolGiveawayInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createAirTokenArgs = {
  data: CreateAirTokenInput;
  projectId: Scalars['ID'];
};


export type Mutation_createAirTokenFailedArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createAirTokenGiveawayArgs = {
  data: AirTokenGiveawayInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createAirboostReferralTaskArgs = {
  data: AirboostReferralTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createAirquestFollowTaskArgs = {
  data: AirquestFollowTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createApiKeyIntegrationArgs = {
  data: ApiKeyIntegrationInput;
  displayName?: InputMaybe<Scalars['String']>;
  integrationName: Scalars['String'];
  projectId: Scalars['ID'];
};


export type Mutation_createAssetHubAirTokenArgs = {
  data: CreateAssetHubAirToken;
  projectId: Scalars['ID'];
};


export type Mutation_createBillingArgs = {
  data: BillingInput;
};


export type Mutation_createBlockchainPoolArgs = {
  data: BlockchainPoolInput;
  projectId: Scalars['ID'];
};


export type Mutation_createBlogCommentTaskArgs = {
  data: BlogCommentTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createBlogWriteTaskArgs = {
  data: BlogWriteTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createCheckinTaskArgs = {
  data: CheckinDailyTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createCheckoutSessionArgs = {
  planId: Scalars['ID'];
  projectId: Scalars['ID'];
  redirectTo: Scalars['ID'];
};


export type Mutation_createCustomTaskArgs = {
  appKey: Scalars['ID'];
  data: CustomTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskKey: Scalars['ID'];
};


export type Mutation_createDiscordJoinTaskArgs = {
  data: DiscordJoinTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createDotsamaBlockchainPoolArgs = {
  data: BlockchainPoolInput;
  projectId: Scalars['ID'];
};


export type Mutation_createEmailAddressTaskArgs = {
  data: EmailAddressTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createEmailSubscribeTaskArgs = {
  data: EmailSubscribeTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createEmailWhitelistTaskArgs = {
  data: EmailWhitelistTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createEventConnectionArgs = {
  eventId: Scalars['ID'];
  provider: AuthProvider;
  providerId: Scalars['ID'];
};


export type Mutation_createEvmContractInteractTaskArgs = {
  data: EvmContractInteractTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createFaucetRawTaskArgs = {
  data: FaucetRawTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createFlashcardTaskArgs = {
  data: FlashcardTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createFormAnswerTaskArgs = {
  data: FormAnswerTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createFromTemplateArgs = {
  projectId: Scalars['ID'];
  templateId: Scalars['ID'];
};


export type Mutation_createGlobalReferralCodeArgs = {
  code: Scalars['String'];
  expiresAt: Scalars['DateTime'];
};


export type Mutation_createIntegrationEventArgs = {
  data?: InputMaybe<IntegrationEventInput>;
  eventId: Scalars['ID'];
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createInvitationArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_createKickstarterTaskArgs = {
  data: KickstarterTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createLinkTaskArgs = {
  data: LinkTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createLuckydrawTaskArgs = {
  data: LuckydrawTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createMerchandiseGiveawayArgs = {
  data: MerchandiseGiveawayInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createMobileAppTaskArgs = {
  data: MobileAppTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createOrReturnDefaultEventArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_createProducthuntFollowTaskArgs = {
  data: ProducthuntFollowTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createProducthuntUpvoteTaskArgs = {
  data: ProducthuntUpvoteTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createProjectArgs = {
  data: ProjectInput;
};


export type Mutation_createProjectEventArgs = {
  data: ProjectEventInput;
  projectId: Scalars['ID'];
};


export type Mutation_createProjectUrlArgs = {
  data: ProjectUrlInput;
  projectId: Scalars['ID'];
};


export type Mutation_createProjectwebhookSecretArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_createQuizPlayTaskArgs = {
  data: QuizTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createRestRawTaskArgs = {
  data: RestRawTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSeasonArgs = {
  name: Scalars['String'];
  projectId: Scalars['ID'];
};


export type Mutation_createSecretCodeTaskArgs = {
  data: SecretCodeTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSecretGiveawayArgs = {
  data: SecretGiveawayInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSignTermsTaskArgs = {
  data: SignTermsTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubgraphRawTaskArgs = {
  data: SubgraphRawTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialCommentTaskArgs = {
  data: SubsocialCommentTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialFollowTaskArgs = {
  data: SubsocialFollowTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialPostTaskArgs = {
  data: SubsocialPostTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialProfileTaskArgs = {
  data: SubsocialProfileTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialShareTaskArgs = {
  data: SubsocialShareTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialSpaceTaskArgs = {
  data: SubsocialSpaceTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubsocialUpvoteTaskArgs = {
  data: SubsocialUpvoteTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createSubstrateQueryTaskArgs = {
  data: SubstrateQueryTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTelegramJoinTaskArgs = {
  data: TelegramJoinTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterFollowTaskArgs = {
  data: TwitterFollowTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterLikeRetweetTaskArgs = {
  data: TwitterLikeRetweetTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterLikeTaskArgs = {
  data: TwitterLikeTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterPostTaskArgs = {
  data: TwitterPostTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterRetweetTaskArgs = {
  data: TwitterRetweetTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterUgcTaskArgs = {
  data: TwitterUgcTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createTwitterWhitelistTaskArgs = {
  data: TwitterWhitelistTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createUploadTaskArgs = {
  data: UploadTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createWalletAddressTaskArgs = {
  data: WalletAddressTaskInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createWhitelistGiveawayArgs = {
  data: WhitelistGiveawayInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_createWidgetArgs = {
  data: WidgetDataInput;
  projectId: Scalars['ID'];
};


export type Mutation_createZapierWebhookArgs = {
  integrationId: Scalars['ID'];
  name: Scalars['String'];
  projectId: Scalars['ID'];
  webhookUrl: Scalars['String'];
};


export type Mutation_deleteAirTokenArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteBlockchainArgs = {
  id: Scalars['ID'];
};


export type Mutation_deleteBlockchainAssetArgs = {
  id: Scalars['ID'];
};


export type Mutation_deleteBlockchainPoolArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteEventPromotionArgs = {
  id: Scalars['ID'];
};


export type Mutation_deleteGiveawayArgs = {
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteGlobalReferralCodeArgs = {
  id: Scalars['ID'];
};


export type Mutation_deleteIntegrationArgs = {
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteIntegrationEventArgs = {
  eventId: Scalars['ID'];
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteIntegrationTaskArgs = {
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_deleteProjectArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_deleteProjectEventArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteProjectMemberArgs = {
  projectId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type Mutation_deleteProjectPromotionArgs = {
  id: Scalars['ID'];
};


export type Mutation_deleteProjectUrlArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteTaskArgs = {
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_deleteTaskParticipationArgs = {
  eventId: Scalars['ID'];
  participationId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_deleteWidgetArgs = {
  projectId: Scalars['ID'];
  widgetId: Scalars['ID'];
};


export type Mutation_deleteWinnerArgs = {
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
  rewardId: Scalars['ID'];
};


export type Mutation_deleteZapierWebhookArgs = {
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
  webhookId: Scalars['ID'];
};


export type Mutation_deployContractsArgs = {
  blockchainId: Scalars['ID'];
  ca: Scalars['ID'];
  contracts: Array<ContractType>;
  owner: Scalars['ID'];
};


export type Mutation_depositBlockchainPoolArgs = {
  data: DepositBlockchainPoolInput;
  projectId: Scalars['ID'];
};


export type Mutation_evmLoginArgs = {
  authDto: EvmBlockchainAuthDto;
  projectId: Scalars['ID'];
};


export type Mutation_followArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_generateApiKeyArgs = {
  expiresAt: Scalars['DateTime'];
  projectId: Scalars['ID'];
};


export type Mutation_generateZapierApiKeyArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_hideGiveawayArgs = {
  giveawayId: Scalars['ID'];
  hidden: Scalars['Boolean'];
  projectId: Scalars['ID'];
};


export type Mutation_hideTaskArgs = {
  hidden: Scalars['Boolean'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_markReadNotificationArgs = {
  notificationId: Scalars['ID'];
};


export type Mutation_notifyTelegramConnectionArgs = {
  payload: Scalars['ID'];
  sessionId: Scalars['ID'];
};


export type Mutation_participateAirquestFollowTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateBlogCommentTaskArgs = {
  data: BlogCommentParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateBlogWriteTaskArgs = {
  data: BlogWriteParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateCheckinTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateDiscordJoinTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateDotsamaFaucetRawTaskArgs = {
  captcha?: InputMaybe<Scalars['String']>;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateDotsamaRestRawTaskArgs = {
  data: RestTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateDotsamaSignTermsTaskArgs = {
  data: SignTermsTaskParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateDotsamaSubgraphRawTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateDotsamaWalletAddressTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEVMSignTermsTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEmailAddressTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEmailSubscribeTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEmailWhitelistTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEvmContractInteractTaskArgs = {
  data: EvmContractInteractTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEvmFaucetRawTaskArgs = {
  captcha?: InputMaybe<Scalars['String']>;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateEvmRestRawTaskArgs = {
  data: RestTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateFormAnswerTaskArgs = {
  data: FormAnswerTaskParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateKickstarterTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateLinkTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateLuckydrawTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateMobileAppTaskArgs = {
  data: MobileAppParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateProducthuntFollowTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateProducthuntUpvoteTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateQuizPlayTaskArgs = {
  data: QuizTaskParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateRestRawTaskArgs = {
  data: RestTaskParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSecretCodeTaskArgs = {
  data: SecretCodeTaskParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubgraphRawTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialCommentTaskArgs = {
  data: SubsocialCommentTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialFollowTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialPostTaskArgs = {
  data: SubsocialPostTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialProfileTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialShareTaskArgs = {
  data: SubsocialShareTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialSpaceTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubsocialUpvoteTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateSubstrateQueryTaskArgs = {
  data: SubstrateQueryTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTelegramJoinTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTextSignTermsTaskArgs = {
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterFollowTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterLikeRetweetTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterLikeTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterPostTaskArgs = {
  data: TwitterPostTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterRetweetTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterUgcTaskArgs = {
  data: TwitterUgcTaskParticipationInput;
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateTwitterWhitelistTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateUploadTaskArgs = {
  data: UploadTaskParticipationInput;
  eventId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_participateWalletAddressTaskArgs = {
  eventId: Scalars['ID'];
  providerId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_polkadotLoginArgs = {
  authDto: DotsamaBlockchainAuthDto;
  projectId: Scalars['ID'];
};


export type Mutation_projectMemberArgs = {
  projectId: Scalars['ID'];
  role: Role;
  userId: Scalars['ID'];
};


export type Mutation_publishProjectEventArgs = {
  announce?: InputMaybe<Scalars['Boolean']>;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_referralCodeArgs = {
  eventId: Scalars['ID'];
};


export type Mutation_refreshWalletDotsamaNftItemsArgs = {
  assetId: Scalars['ID'];
  blockchainId: Scalars['ID'];
  poolId: Scalars['ID'];
  projectId: Scalars['ID'];
  walletAddress: Scalars['String'];
};


export type Mutation_reorderTaskArgs = {
  order: Scalars['Int'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_revokeProjectAccessArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_settleGiveawayArgs = {
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_submitGlobalReferralCodeArgs = {
  code: Scalars['String'];
};


export type Mutation_superAdminProjectAuthorizationArgs = {
  projectId: Scalars['ID'];
  status: Scalars['Boolean'];
};


export type Mutation_syncAirTokenArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_syncBlockchainPoolArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_syncClaimAirPoolGiveawayArgs = {
  giveawayId: Scalars['ID'];
  ids: Array<Scalars['ID']>;
};


export type Mutation_syncClaimAirTokenGiveawayArgs = {
  giveawayId: Scalars['ID'];
  ids: Array<Scalars['ID']>;
};


export type Mutation_unfollowArgs = {
  projectId: Scalars['ID'];
};


export type Mutation_updateAirPoolGiveawayArgs = {
  data: AirPoolGiveawayUpdateInput;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateAirTokenGiveawayArgs = {
  data: AirTokenGiveawayUpdateInput;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateAirboostReferralTaskArgs = {
  data: AirboostReferralTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateAirquestFollowTaskArgs = {
  data: AirquestFollowTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateApiKeyIntegrationArgs = {
  data: ApiKeyIntegrationUpdateInput;
  displayName?: InputMaybe<Scalars['String']>;
  integrationId: Scalars['ID'];
  integrationName: Scalars['String'];
  projectId: Scalars['ID'];
};


export type Mutation_updateBillingArgs = {
  billingId: Scalars['ID'];
  data: BillingUpdateInput;
};


export type Mutation_updateBlockchainArgs = {
  data: BlockchainUpdateInput;
  id: Scalars['ID'];
};


export type Mutation_updateBlockchainAssetArgs = {
  data: BlockchainAssetUpdateInput;
  id: Scalars['ID'];
};


export type Mutation_updateBlogCommentTaskArgs = {
  data: BlogCommentTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateBlogWriteTaskArgs = {
  data: BlogWriteTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateCheckinTaskArgs = {
  data: CheckinDailyTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateCustomTaskArgs = {
  data: CustomTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateDiscordJoinTaskArgs = {
  data: DiscordJoinTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateEmailAddressTaskArgs = {
  data: EmailAddressTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateEmailSubscribeTaskArgs = {
  data: EmailSubscribeTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateEmailWhitelistTaskArgs = {
  data: EmailWhitelistTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateEventPromotionArgs = {
  eventId: Scalars['ID'];
  updatedAt?: InputMaybe<Scalars['DateTime']>;
  url?: InputMaybe<Scalars['String']>;
};


export type Mutation_updateEvmContractInteractTaskArgs = {
  data: EvmContractInteractTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateFaucetRawTaskArgs = {
  data: FaucetRawTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateFlashcardTaskArgs = {
  data: FlashcardTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateFormAnswerTaskArgs = {
  data: FormAnswerTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateGlobalReferralCodeArgs = {
  code: Scalars['String'];
  expiresAt: Scalars['DateTime'];
  id: Scalars['ID'];
};


export type Mutation_updateIntegrationEventArgs = {
  data: IntegrationEventUpdateInput;
  eventId: Scalars['ID'];
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateKickstarterTaskArgs = {
  data: KickstarterTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateLinkTaskArgs = {
  data: LinkTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateLuckydrawTaskArgs = {
  data: LuckydrawTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateMeArgs = {
  data: UserInput;
};


export type Mutation_updateMerchandiseGiveawayArgs = {
  data: MerchandiseGiveawayUpdateInput;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateMobileAppTaskArgs = {
  data: MobileAppTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateProducthuntFollowTaskArgs = {
  data: ProducthuntFollowTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateProducthuntUpvoteTaskArgs = {
  data: ProducthuntUpvoteTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateProjectArgs = {
  data: ProjectUpdateInput;
  projectId: Scalars['ID'];
};


export type Mutation_updateProjectEventArgs = {
  data: ProjectEventUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateProjectPromotionArgs = {
  projectId: Scalars['ID'];
  updatedAt?: InputMaybe<Scalars['DateTime']>;
  url?: InputMaybe<Scalars['String']>;
};


export type Mutation_updateProjectTemplateOnboardingStatusArgs = {
  projectId: Scalars['ID'];
  templateId: Scalars['ID'];
};


export type Mutation_updateProjectUrlArgs = {
  data: ProjectUrlUpdateInput;
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateProjectVisibilityArgs = {
  data: ProjectVisibilityUpdateInput;
  projectId: Scalars['ID'];
};


export type Mutation_updateQuizPlayTaskArgs = {
  data: QuizTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateRejectedStatusArgs = {
  eventId: Scalars['ID'];
  id: Scalars['ID'];
  projectId: Scalars['ID'];
  rejected: Scalars['Boolean'];
  taskId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type Mutation_updateRestRawTaskArgs = {
  data: RestRawTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSecretCodeTaskArgs = {
  data: SecretCodeTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSecretGiveawayArgs = {
  data: SecretGiveawayUpdateInput;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateSignTermsTaskArgs = {
  data: SignTermsTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubgraphRawTaskArgs = {
  data: SubgraphRawTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialCommentTaskArgs = {
  data: SubsocialCommentTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialFollowTaskArgs = {
  data: SubsocialFollowTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialPostTaskArgs = {
  data: SubsocialPostTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialProfileTaskArgs = {
  data: SubsocialProfileTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialShareTaskArgs = {
  data: SubsocialShareTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialSpaceTaskArgs = {
  data: SubsocialSpaceTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubsocialUpvoteTaskArgs = {
  data: SubsocialUpvoteTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateSubstrateQueryTaskArgs = {
  data: SubstrateQueryTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTaskParticipationStatusArgs = {
  eventId: Scalars['ID'];
  participationId: Scalars['ID'];
  projectId: Scalars['ID'];
  status: AccountParticipationStatus;
};


export type Mutation_updateTelegramJoinTaskArgs = {
  data: TelegramJoinTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterFollowTaskArgs = {
  data: TwitterFollowTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterLikeRetweetTaskArgs = {
  data: TwitterLikeRetweetTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterLikeTaskArgs = {
  data: TwitterLikeTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterPostTaskArgs = {
  data: TwitterPostTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterRetweetTaskArgs = {
  data: TwitterRetweetTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterUgcTaskArgs = {
  data: TwitterUgcTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateTwitterWhitelistTaskArgs = {
  data: TwitterWhitelistTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateUploadTaskArgs = {
  data: UploadTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateUserCookieConsentArgs = {
  cookieConsent: CookieConsent;
};


export type Mutation_updateUserOnboardingAppArgs = {
  onboardingApp: Onboarding;
};


export type Mutation_updateWalletAddressTaskArgs = {
  data: WalletAddressTaskUpdateInput;
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Mutation_updateWhitelistGiveawayArgs = {
  data: WhitelistGiveawayUpdateInput;
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Mutation_updateWidgetArgs = {
  data: WidgetDataUpdateInput;
  projectId: Scalars['ID'];
  widgetId: Scalars['ID'];
};


export type Mutation_updateZapierWebhookArgs = {
  integrationId: Scalars['ID'];
  name: Scalars['String'];
  projectId: Scalars['ID'];
  webhookId: Scalars['ID'];
  webhookUrl: Scalars['String'];
};


export type Mutation_verifyInvitationArgs = {
  id: Scalars['String'];
  invitationCode: Scalars['String'];
};


export type Mutation_withdrawBlockchainPoolArgs = {
  data: WithdrawBlockchainPoolInput;
  projectId: Scalars['ID'];
};


export type Mutation_withdrawDotsamaBlockchainPoolArgs = {
  data: WithdrawBlockchainPoolInput;
  projectId: Scalars['ID'];
};

export enum NFTStatus {
  AVAILABLE = 'AVAILABLE',
  DISTRIBUTED = 'DISTRIBUTED',
  LOCKED = 'LOCKED'
}

export type NftSwap = {
  __typename?: 'NftSwap';
  address: Scalars['String'];
  blockchainId: Scalars['String'];
};

export type NftSwapInput = {
  address: Scalars['String'];
  blockchainId: Scalars['String'];
};

export type NullableTaskData = {
  __typename?: 'NullableTaskData';
  createdAt: Scalars['DateTime'];
  isNull?: Maybe<Scalars['Boolean']>;
  updatedAt: Scalars['DateTime'];
};

export type ObjectLiteral = {
  __typename?: 'ObjectLiteral';
  id: Scalars['String'];
};

export enum Onboarding {
  AIRPOOLS = 'AIRPOOLS',
  AIRTOKENS = 'AIRTOKENS',
  CAMPAIGNS = 'CAMPAIGNS',
  NOTIFICATION_EMAIL = 'NOTIFICATION_EMAIL',
  PARTICIPANT_TERMS = 'PARTICIPANT_TERMS'
}

export type PaginationInput = {
  skip: Scalars['Int'];
  take: Scalars['Int'];
};

export type Participant = {
  __typename?: 'Participant';
  displayName: Scalars['String'];
  isChosen: Scalars['Boolean'];
  primaryAuth?: Maybe<Auth>;
  rank?: Maybe<Scalars['Int']>;
  totalPoints: Scalars['Int'];
  totalTask: Scalars['Int'];
  totalXp: Scalars['Int'];
  userId: Scalars['String'];
};

export type ParticipantWhereInput = {
  date?: InputMaybe<DateInput>;
  eventId: Scalars['ID'];
  taskIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
};

export type ParticipantsData = {
  __typename?: 'ParticipantsData';
  data: Array<Participant>;
  total: Scalars['Float'];
};

export enum ParticipationStatus {
  INVALID = 'INVALID',
  IN_AI_VERIFICATION = 'IN_AI_VERIFICATION',
  IN_REVIEW = 'IN_REVIEW',
  VALID = 'VALID'
}

export enum PaymentGateway {
  AIRLYFT_CRYPTO = 'AIRLYFT_CRYPTO',
  AIRLYFT_MANUAL = 'AIRLYFT_MANUAL',
  STRIPE = 'STRIPE'
}

export enum PaymentPlan {
  ENTERPRISE = 'ENTERPRISE',
  ENTERPRISE_TRIAL = 'ENTERPRISE_TRIAL',
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  PROFESSIONAL_BIANNUAL = 'PROFESSIONAL_BIANNUAL',
  STARTUP = 'STARTUP',
  STARTUP_BIANNUAL = 'STARTUP_BIANNUAL',
  TRIAL = 'TRIAL'
}

export type PlatformField = {
  __typename?: 'PlatformField';
  label: Scalars['String'];
  name: Scalars['String'];
  placeholder?: Maybe<Scalars['String']>;
  regex?: Maybe<Scalars['String']>;
};

export type PoolGiveawaySummary = {
  __typename?: 'PoolGiveawaySummary';
  amount: Scalars['String'];
  eventId: Scalars['String'];
  id: Scalars['String'];
  title: Scalars['String'];
};

export type ProducthuntFollowTaskData = {
  __typename?: 'ProducthuntFollowTaskData';
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  userUrl: Scalars['String'];
};

export type ProducthuntFollowTaskDataInput = {
  userUrl: Scalars['String'];
};

export type ProducthuntFollowTaskDataUpdateInput = {
  userUrl?: InputMaybe<Scalars['String']>;
};

export type ProducthuntFollowTaskInput = {
  data: ProducthuntFollowTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type ProducthuntFollowTaskUpdateInput = {
  data: ProducthuntFollowTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type ProducthuntUpvoteTaskData = {
  __typename?: 'ProducthuntUpvoteTaskData';
  createdAt: Scalars['DateTime'];
  postTitle: Scalars['String'];
  postUrl: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type ProducthuntUpvoteTaskDataInput = {
  postTitle: Scalars['String'];
  postUrl: Scalars['String'];
};

export type ProducthuntUpvoteTaskDataUpdateInput = {
  postTitle?: InputMaybe<Scalars['String']>;
  postUrl: Scalars['String'];
};

export type ProducthuntUpvoteTaskInput = {
  data: ProducthuntUpvoteTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type ProducthuntUpvoteTaskUpdateInput = {
  data: ProducthuntUpvoteTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type Project = {
  __typename?: 'Project';
  authorization?: Maybe<Array<Authorization>>;
  bannerUrl?: Maybe<Scalars['String']>;
  billings?: Maybe<Array<Billing>>;
  bio?: Maybe<Scalars['String']>;
  contactEmail?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  ecosystems?: Maybe<Array<Ecosystem>>;
  followerCount: Scalars['Float'];
  id: Scalars['ID'];
  integrations?: Maybe<Array<Integration>>;
  isHideQbRecentEvents?: Maybe<Scalars['Boolean']>;
  logo?: Maybe<Scalars['String']>;
  name: Scalars['String'];
  plan: PaymentPlan;
  projectEvents?: Maybe<Array<ProjectEvent>>;
  projectUrls?: Maybe<Array<ProjectUrl>>;
  publicLink: Scalars['String'];
  referralId?: Maybe<Scalars['String']>;
  referredEcosystem?: Maybe<Ecosystem>;
  seasons?: Maybe<Array<Season>>;
  sectors?: Maybe<Array<Sector>>;
  updatedAt: Scalars['DateTime'];
  verified?: Maybe<Scalars['Boolean']>;
  visibility?: Maybe<ProjectVisibility>;
  webhookSecret?: Maybe<Scalars['String']>;
};

export type ProjectData = {
  __typename?: 'ProjectData';
  data?: Maybe<Array<Maybe<Project>>>;
  total: Scalars['Float'];
};

export type ProjectEvent = {
  __typename?: 'ProjectEvent';
  bannerUrl?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  endTime?: Maybe<Scalars['DateTime']>;
  eventType: EventType;
  giveawaySummary?: Maybe<Array<Maybe<GiveawaySummary>>>;
  id: Scalars['ID'];
  ipProtect?: Maybe<Scalars['Boolean']>;
  leaderboard: LeaderboardType;
  mode?: Maybe<Scalars['Int']>;
  project: Project;
  publicLink: Scalars['String'];
  rewardSubtitle?: Maybe<Scalars['String']>;
  rewardTitle?: Maybe<Scalars['String']>;
  seasonId?: Maybe<Scalars['String']>;
  settledAt?: Maybe<Scalars['DateTime']>;
  settlementFiles?: Maybe<Array<Maybe<Scalars['String']>>>;
  startTime?: Maybe<Scalars['DateTime']>;
  state: EventState;
  summary?: Maybe<ProjectEventSummary>;
  tags?: Maybe<Array<Maybe<Scalars['String']>>>;
  title: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  visibility: EventVisibility;
};

export type ProjectEventData = {
  __typename?: 'ProjectEventData';
  data?: Maybe<Array<Maybe<ProjectEvent>>>;
  total: Scalars['Float'];
};

export type ProjectEventInput = {
  bannerUrl?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  ipProtect?: InputMaybe<Scalars['Boolean']>;
  leaderboard?: InputMaybe<LeaderboardType>;
  publicLink: Scalars['String'];
  seasonId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title: Scalars['String'];
  visibility?: InputMaybe<AccountEventVisibility>;
  webhook?: InputMaybe<Scalars['String']>;
  zapierWebhook?: InputMaybe<Scalars['String']>;
};

export type ProjectEventSortInput = {
  direction: SortDirection;
  sortKey: ProjectEventSortKey;
};

/** Valid sort keys for the query */
export enum ProjectEventSortKey {
  CREATED_AT = 'CREATED_AT',
  TRENDING = 'TRENDING'
}

export type ProjectEventSummary = {
  __typename?: 'ProjectEventSummary';
  totalParticipants?: Maybe<Scalars['Int']>;
  totalPoints?: Maybe<Scalars['Int']>;
  totalPointsEarned?: Maybe<Scalars['Int']>;
  totalTaskParticipation?: Maybe<Scalars['Int']>;
  totalTasks?: Maybe<Scalars['Int']>;
  totalXP?: Maybe<Scalars['Int']>;
};

export type ProjectEventUpdateInput = {
  bannerUrl?: InputMaybe<Scalars['String']>;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  ipProtect?: InputMaybe<Scalars['Boolean']>;
  leaderboard?: InputMaybe<LeaderboardType>;
  publicLink?: InputMaybe<Scalars['String']>;
  seasonId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  visibility?: InputMaybe<AccountEventVisibility>;
  webhook?: InputMaybe<Scalars['String']>;
  zapierWebhook?: InputMaybe<Scalars['String']>;
};

export type ProjectEventWhereInput = {
  ecosystems?: InputMaybe<Array<Ecosystem>>;
  projectId: Scalars['ID'];
  state: Array<EventState>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  visibility?: InputMaybe<Array<InputMaybe<AccountEventVisibility>>>;
};

export type ProjectFuelInfo = {
  __typename?: 'ProjectFuelInfo';
  avg?: Maybe<Scalars['Float']>;
  count?: Maybe<Scalars['Int']>;
  max?: Maybe<Scalars['Int']>;
  min?: Maybe<Scalars['Int']>;
};

export type ProjectInput = {
  bannerUrl?: InputMaybe<Scalars['String']>;
  bio?: InputMaybe<Scalars['String']>;
  contactEmail?: InputMaybe<Scalars['String']>;
  ecosystems?: InputMaybe<Array<Ecosystem>>;
  isHideQbRecentEvents?: InputMaybe<Scalars['Boolean']>;
  logo?: InputMaybe<Scalars['String']>;
  name: Scalars['String'];
  publicLink: Scalars['String'];
  referralId?: InputMaybe<Scalars['String']>;
  referredEcosystem?: InputMaybe<Ecosystem>;
  sectors?: InputMaybe<Array<Sector>>;
  urls?: InputMaybe<Array<InputMaybe<ProjectUrlInput>>>;
};

export type ProjectSortInput = {
  direction: SortDirection;
  sortKey: ProjectSortKey;
};

/** Valid sort keys for the query */
export enum ProjectSortKey {
  CREATED_AT = 'CREATED_AT',
  TRENDING = 'TRENDING'
}

export type ProjectTemplateOnboardings = {
  __typename?: 'ProjectTemplateOnboardings';
  createdAt: Scalars['DateTime'];
  eventTemplateId: Scalars['String'];
  projectId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type ProjectUpdateInput = {
  bannerUrl?: InputMaybe<Scalars['String']>;
  bio?: InputMaybe<Scalars['String']>;
  contactEmail?: InputMaybe<Scalars['String']>;
  ecosystems?: InputMaybe<Array<Ecosystem>>;
  isHideQbRecentEvents?: InputMaybe<Scalars['Boolean']>;
  logo?: InputMaybe<Scalars['String']>;
  name?: InputMaybe<Scalars['String']>;
  publicLink?: InputMaybe<Scalars['String']>;
  referralId?: InputMaybe<Scalars['String']>;
  referredEcosystem?: InputMaybe<Ecosystem>;
  sectors?: InputMaybe<Array<Sector>>;
  urls?: InputMaybe<Array<ProjectUrlInput>>;
};

export type ProjectUrl = {
  __typename?: 'ProjectUrl';
  createdAt: Scalars['DateTime'];
  data?: Maybe<Scalars['JSONObject']>;
  id: Scalars['ID'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
  urlType: ProjectUrlType;
};

export type ProjectUrlInput = {
  data?: InputMaybe<Scalars['JSONObject']>;
  url: Scalars['String'];
  urlType: ProjectUrlType;
};

export enum ProjectUrlType {
  DISCORD = 'DISCORD',
  FACEBOOK = 'FACEBOOK',
  GITHUB = 'GITHUB',
  INSTAGRAM = 'INSTAGRAM',
  REDDIT = 'REDDIT',
  TELEGRAM = 'TELEGRAM',
  TOKENOMICS = 'TOKENOMICS',
  TWITTER = 'TWITTER',
  WEBSITE = 'WEBSITE',
  WHITEPAPER = 'WHITEPAPER',
  YOUTUBE = 'YOUTUBE'
}

export type ProjectUrlUpdateInput = {
  data?: InputMaybe<Scalars['JSONObject']>;
  url?: InputMaybe<Scalars['String']>;
  urlType?: InputMaybe<ProjectUrlType>;
};

export type ProjectUserInfo = {
  __typename?: 'ProjectUserInfo';
  currentLevel?: Maybe<Scalars['Int']>;
  follows?: Maybe<Scalars['Boolean']>;
  fuel?: Maybe<Scalars['Int']>;
  levelLimit?: Maybe<Scalars['Int']>;
  rank?: Maybe<Scalars['Int']>;
  userId?: Maybe<Scalars['ID']>;
  xp?: Maybe<Scalars['Int']>;
};

export type ProjectUserStats = {
  __typename?: 'ProjectUserStats';
  projectId: Scalars['ID'];
  totalCompleted: Scalars['Float'];
  totalTasks: Scalars['Float'];
};

export enum ProjectVisibility {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC',
  PUBLIC_EXPLORABLE = 'PUBLIC_EXPLORABLE'
}

export type ProjectVisibilityUpdateInput = {
  verified?: InputMaybe<Scalars['Boolean']>;
  visibility?: InputMaybe<ProjectVisibility>;
};

export type ProjectWhereInput = {
  ecosystems?: InputMaybe<Array<Ecosystem>>;
  sectors?: InputMaybe<Array<Sector>>;
};

export type PromotedEvent = {
  __typename?: 'PromotedEvent';
  createdAt: Scalars['DateTime'];
  event: ProjectEvent;
  eventId: Scalars['String'];
  promotionType: PromotionType;
  updatedAt: Scalars['DateTime'];
};

export type PromotedProject = {
  __typename?: 'PromotedProject';
  createdAt: Scalars['DateTime'];
  project: Project;
  projectId: Scalars['String'];
  promotionType: PromotionType;
  updatedAt: Scalars['DateTime'];
};

export enum PromotionType {
  EXPLORE_FEATURE = 'EXPLORE_FEATURE'
}

export enum PublicEventState {
  ONGOING = 'ONGOING',
  READY_TO_SETTLE = 'READY_TO_SETTLE',
  SCHEDULED = 'SCHEDULED',
  SETTLED = 'SETTLED'
}

export enum PublicEventVisibility {
  PUBLIC = 'PUBLIC',
  PUBLIC_EXPLORABLE = 'PUBLIC_EXPLORABLE'
}

export type PublicProjectEventWhereInput = {
  ecosystems?: InputMaybe<Array<Ecosystem>>;
  eventType?: InputMaybe<EventType>;
  projectId?: InputMaybe<Scalars['ID']>;
  state: Array<PublicEventState>;
  tags?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  visibility: Array<PublicEventVisibility>;
};

export type PublicUser = {
  __typename?: 'PublicUser';
  avatar?: Maybe<Scalars['String']>;
  cookieConsent?: Maybe<CookieConsent>;
  createdAt: Scalars['DateTime'];
  firstName?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  lastName?: Maybe<Scalars['String']>;
  onboarded?: Maybe<Array<Onboarding>>;
  rep: Scalars['Int'];
  updatedAt: Scalars['DateTime'];
  username?: Maybe<Scalars['String']>;
};

export type Query = {
  __typename?: 'Query';
  /** AirToken details */
  airToken: BlockchainAsset;
  /** Get all AirTokens created by the project */
  airTokens: Array<BlockchainAsset>;
  /** Internal Use Only - Get all whitelisted assets on the platform */
  airpoolAssetWhitelist?: Maybe<BlockchainAssetsResponse>;
  /** Count of published events by all projects */
  allEventsCount: Scalars['Int'];
  /** Get all integrations */
  allIntegrationsByPlatformName: Array<Integration>;
  /** Internal Use Only - Get all projects with pagination */
  allProjects: ProjectData;
  billingDetails?: Maybe<BillingDetails>;
  billingPlans?: Maybe<Array<BillingPlan>>;
  blockchain: Blockchain;
  blockchainAssets?: Maybe<Array<BlockchainAsset>>;
  blockchainPool: BlockchainPool;
  blockchainPools: Array<BlockchainPool>;
  blockchains?: Maybe<Array<Blockchain>>;
  conflictedUserProfile?: Maybe<ConflictedUserProfile>;
  connectionToken: Scalars['String'];
  contract?: Maybe<Contract>;
  /** Get questboard event for project */
  defaultProjectEvent: ProjectEvent;
  dotsamaPoolGiveawaysSummary: Array<DotsamaPoolGiveawaySummary>;
  dotsamaWalletAddress: Scalars['String'];
  ecosystemTemplates: Array<EventTemplate>;
  enterpriseTrailEligible: Scalars['Boolean'];
  eventConnectionUser: EventConnectionUser;
  eventConnections: Array<EventConnection>;
  eventParticipants: ParticipantsData;
  /** Get task submission data */
  eventSubmissions: TaskSubmissionsData;
  /** Count of published events by projectId */
  eventsCount: Scalars['Int'];
  evmWalletAddress: Scalars['String'];
  /** Search public events */
  exploreEvents: ProjectEventData;
  /** Search projects */
  exploreProjects: ProjectData;
  /** If user is shortlisted for an event with a giveaway */
  findUserRewardByStatus: Array<EventReward>;
  /** Get list of projects followed by a given user */
  followedProjects: FollowedProjectData;
  getBillings: Array<Billing>;
  getCustomApps: Array<AirlyftApp>;
  getDau?: Maybe<Array<DauData>>;
  getDotsamaPoolAssetNftItems?: Maybe<Array<DotsamaNft>>;
  getIntegrationPlatformConfig: Array<IntegrationPlatformConfig>;
  /** Get all invite codes */
  getInvitationCodes: Array<Invitation>;
  getMau?: Maybe<Array<MauData>>;
  /** Get invite code issuing project */
  getProjectFromInviteId: Invitation;
  getTopCountries?: Maybe<Array<CountryStat>>;
  getTopNQuest?: Maybe<Array<TaskDateParticipationsData>>;
  getUserCount: Array<UserCountPoint>;
  getWalletAddressDate?: Maybe<Array<WalletAddressDateData>>;
  giveaway: Giveaway;
  giveaways: Array<Giveaway>;
  /** Get Paginated list of users and their leaderboard position */
  globalLeaderboard?: Maybe<GlobalLeaderboardData>;
  /** Get Paginated list of users and their leaderboard position */
  globalLeaderboardInternal?: Maybe<GlobalLeaderboardData>;
  globalReferralCodes?: Maybe<Array<Maybe<ReferralCode>>>;
  globalTemplates: Array<EventTemplate>;
  /** Get Integration details */
  integration: Integration;
  /** Get integrations by eventId */
  integrationEvent: Array<IntegrationEvent>;
  /** Get integrations by taskId */
  integrationTask: Array<IntegrationTask>;
  /** Check if a public url is available */
  isEventUrlAvailable: Scalars['Boolean'];
  /** Check if public URL is available */
  isProjectUrlAvailable: Scalars['Boolean'];
  /** Check if code is available */
  isReferralCodeAvailable: Scalars['Boolean'];
  isUsernameAvailable: Scalars['Boolean'];
  /** Get Records by group id */
  keyValueGroups?: Maybe<Array<KeyValueGroup>>;
  /** Get Paginated list of users and their XP for a given project or event. If interval is provided then this always returns the project leaderboard, else if season is provided then season leaderboard, else the event leaderboard */
  leaderboard: LeaderboardData;
  /** Get Logged in User details */
  me: User;
  pGiveaways: Array<Giveaway>;
  pShopGiveaways: GiveawaysData;
  /** Get task by id */
  pTask: Task;
  /** Get Tasks by eventId */
  pTasks: Array<Task>;
  /** Get all sub tasks by parent taskId */
  pTasksByParentId: Array<Task>;
  /** Get task participation data */
  participation: TaskParticipation;
  poolGiveaways: Array<Giveaway>;
  poolGiveawaysSummary: Array<PoolGiveawaySummary>;
  /** Get all projects that the logged in user has some authorization to */
  project?: Maybe<Project>;
  projectAiVerifiedParticipationCount?: Maybe<Scalars['Float']>;
  /** Get project details by link */
  projectByLink: Project;
  /** Get questboard event by project public link */
  projectDefaultEventByLink?: Maybe<ProjectEvent>;
  /** Get project event by id */
  projectEvent: ProjectEvent;
  /** Get project event by public link */
  projectEventByLink?: Maybe<ProjectEvent>;
  /** Search events */
  projectEvents: ProjectEventData;
  /** Get fuel stats for a projectId */
  projectFuelInfo?: Maybe<ProjectFuelInfo>;
  projectParticipationCount?: Maybe<Scalars['Float']>;
  /** Get a user's XP and fuel for a projectId */
  projectUserInfo?: Maybe<ProjectUserInfo>;
  /** Get a users XP, cFuel and number of tasks completed for a give provider and providerId */
  projectUserInfoByProvider?: Maybe<ProjectUserInfo>;
  /** Get all users in a project with user details */
  projectUsersWithUserDetails?: Maybe<Array<Authorization>>;
  /** Get User's projects with pagination */
  projects: Array<Project>;
  /** Get Project Progress */
  projectsProgress: Array<ProjectUserStats>;
  /** Get a list of promoted events */
  promotedEvents?: Maybe<Array<PromotedEvent>>;
  /** Get a list of promoted projects */
  promotedProjects?: Maybe<Array<PromotedProject>>;
  referredUsers: Array<StrippedReferredUser>;
  referredUsersForUserId: Array<StrippedReferredUser>;
  /** Get Season details by id */
  season: Season;
  /** Get Seasons list */
  seasons?: Maybe<Array<Maybe<Season>>>;
  /** Get message to be singed by the user's wallet. */
  signingMessage: Scalars['String'];
  /** Get Task by id */
  task: Task;
  /** Get Tasks by eventId */
  tasks: Array<Task>;
  /** Get all sub tasks by parent taskId */
  tasksByParentId: Array<Task>;
  /** Get a temporary userId */
  tempUserId?: Maybe<Scalars['ID']>;
  /** Get total participations */
  totalParticipations?: Maybe<Scalars['Int']>;
  /** User details */
  user: PublicUser;
  /** Get user stats in your project using user's providerId */
  userByProvider: ProjectUserInfo;
  userEventParticipated: EventParticipantData;
  userEventRewards: Array<EventReward>;
  /** Get global leaderboard position for the logged in user in a specific interval. */
  userGlobalRanking?: Maybe<GlobalLeaderboardItem>;
  /** Get all notification for the logged in user */
  userNotifications: UserNotificationData;
  /** Get notifications by IDs (max 10) */
  userNotificationsByIds: UserNotificationData;
  /** Get leaderboard position for the logged in user in a given project in a specific interval. */
  userProjectRanking?: Maybe<LeaderboardItem>;
  userRewards: EventRewardsResponse;
  /** Get user role in a project */
  userRoleInProject?: Maybe<Role>;
  /** Get logged in user's participations */
  userTaskParticipation: Array<TaskParticipation>;
  /** Get record value */
  valueByKeyGroup?: Maybe<Scalars['String']>;
  verifyImage: VerificationResult;
  /** Get widget details. */
  widget: Widget;
  /** Get all widgets created for an event. */
  widgets: Array<Widget>;
  /** Get winners list */
  winners: WinnersData;
};


export type Query_airTokenArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_airTokensArgs = {
  assetType?: InputMaybe<AssetType>;
  blockchainId?: InputMaybe<Scalars['ID']>;
  projectId: Scalars['ID'];
};


export type Query_airpoolAssetWhitelistArgs = {
  pagination: PaginationInput;
};


export type Query_allIntegrationsByPlatformNameArgs = {
  platformName: Scalars['String'];
  projectId: Scalars['ID'];
};


export type Query_allProjectsArgs = {
  pagination: PaginationInput;
  where: AllProjectsWhereInput;
};


export type Query_billingDetailsArgs = {
  projectId: Scalars['ID'];
};


export type Query_blockchainArgs = {
  id: Scalars['ID'];
};


export type Query_blockchainAssetsArgs = {
  blockchainId: Scalars['ID'];
  where?: InputMaybe<BlockchainAssetWhereInput>;
};


export type Query_blockchainPoolArgs = {
  id: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_blockchainPoolsArgs = {
  projectId: Scalars['ID'];
  where?: InputMaybe<BlockchainPoolWhereInput>;
};


export type Query_blockchainsArgs = {
  where: BlockchainWhereInput;
};


export type Query_conflictedUserProfileArgs = {
  provider: AuthProvider;
  providerId: Scalars['ID'];
};


export type Query_connectionTokenArgs = {
  provider: AuthProvider;
};


export type Query_contractArgs = {
  blockchainId: Scalars['ID'];
  contractType: ContractType;
};


export type Query_defaultProjectEventArgs = {
  projectId: Scalars['ID'];
};


export type Query_dotsamaPoolGiveawaysSummaryArgs = {
  id: Scalars['ID'];
};


export type Query_dotsamaWalletAddressArgs = {
  blockchainId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_ecosystemTemplatesArgs = {
  ecosystem: Ecosystem;
  pagination?: InputMaybe<PaginationInput>;
  projectId: Scalars['ID'];
};


export type Query_enterpriseTrailEligibleArgs = {
  projectId: Scalars['ID'];
};


export type Query_eventConnectionUserArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type Query_eventConnectionsArgs = {
  eventId: Scalars['ID'];
};


export type Query_eventParticipantsArgs = {
  pagination: PaginationInput;
  projectId: Scalars['ID'];
  sorter: SortInput;
  where: ParticipantWhereInput;
};


export type Query_eventSubmissionsArgs = {
  pagination: PaginationInput;
  projectId: Scalars['ID'];
  where: SubmissionWhereInput;
};


export type Query_eventsCountArgs = {
  projectId: Scalars['ID'];
};


export type Query_evmWalletAddressArgs = {
  projectId: Scalars['ID'];
};


export type Query_exploreEventsArgs = {
  pagination: PaginationInput;
  sorter?: InputMaybe<ProjectEventSortInput>;
  where?: InputMaybe<PublicProjectEventWhereInput>;
};


export type Query_exploreProjectsArgs = {
  pagination: PaginationInput;
  sorter?: InputMaybe<ProjectSortInput>;
  where?: InputMaybe<ProjectWhereInput>;
};


export type Query_findUserRewardByStatusArgs = {
  eventId: Scalars['ID'];
  giveawayId: Scalars['ID'];
};


export type Query_followedProjectsArgs = {
  pagination: PaginationInput;
  userId: Scalars['ID'];
};


export type Query_getBillingsArgs = {
  projectId: Scalars['ID'];
};


export type Query_getDauArgs = {
  noOfDays: Scalars['Int'];
};


export type Query_getDotsamaPoolAssetNftItemsArgs = {
  blockchainAssetId: Scalars['ID'];
  blockchainPoolId: Scalars['ID'];
};


export type Query_getIntegrationPlatformConfigArgs = {
  projectId: Scalars['ID'];
};


export type Query_getInvitationCodesArgs = {
  projectId: Scalars['ID'];
};


export type Query_getMauArgs = {
  noOfMonths: Scalars['Int'];
};


export type Query_getProjectFromInviteIdArgs = {
  inviteId: Scalars['ID'];
};


export type Query_getTopNQuestArgs = {
  endDate?: InputMaybe<Scalars['DateTime']>;
  startDate?: InputMaybe<Scalars['DateTime']>;
  top?: InputMaybe<Scalars['Int']>;
};


export type Query_getUserCountArgs = {
  type: UserCountType;
};


export type Query_getWalletAddressDateArgs = {
  noOfDays: Scalars['Int'];
};


export type Query_giveawayArgs = {
  giveawayId: Scalars['ID'];
};


export type Query_giveawaysArgs = {
  eventId: Scalars['ID'];
};


export type Query_globalLeaderboardArgs = {
  pagination: PaginationInput;
  sorter: GlobalLeaderboardSortInput;
  where: GlobalLeaderboardWhereInput;
};


export type Query_globalLeaderboardInternalArgs = {
  pagination: PaginationInput;
  sorter: GlobalLeaderboardSortInput;
  where: GlobalLeaderboardWhereInput;
};


export type Query_globalTemplatesArgs = {
  pagination?: InputMaybe<PaginationInput>;
};


export type Query_integrationArgs = {
  integrationId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_integrationEventArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_integrationTaskArgs = {
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Query_isEventUrlAvailableArgs = {
  eventId?: InputMaybe<Scalars['ID']>;
  projectId: Scalars['ID'];
  publicLink: Scalars['ID'];
};


export type Query_isProjectUrlAvailableArgs = {
  projectId?: InputMaybe<Scalars['ID']>;
  url: Scalars['String'];
};


export type Query_isReferralCodeAvailableArgs = {
  code: Scalars['String'];
  referralCodeId?: InputMaybe<Scalars['ID']>;
};


export type Query_isUsernameAvailableArgs = {
  data: UsernameInput;
};


export type Query_keyValueGroupsArgs = {
  groupId: Scalars['ID'];
};


export type Query_leaderboardArgs = {
  pagination: PaginationInput;
  projectId: Scalars['ID'];
  sorter: SortInput;
  where: LeaderBoardWhereInput;
};


export type Query_meArgs = {
  projectId?: InputMaybe<Scalars['ID']>;
};


export type Query_pGiveawaysArgs = {
  eventId: Scalars['ID'];
};


export type Query_pShopGiveawaysArgs = {
  pagination: PaginationInput;
  sorter: GiveawaySortInput;
};


export type Query_pTaskArgs = {
  taskId: Scalars['ID'];
};


export type Query_pTasksArgs = {
  eventId: Scalars['ID'];
};


export type Query_pTasksByParentIdArgs = {
  parentId: Scalars['ID'];
};


export type Query_participationArgs = {
  participationId: Scalars['String'];
};


export type Query_poolGiveawaysArgs = {
  poolId: Scalars['ID'];
};


export type Query_poolGiveawaysSummaryArgs = {
  poolId: Scalars['ID'];
};


export type Query_projectArgs = {
  projectId: Scalars['ID'];
};


export type Query_projectAiVerifiedParticipationCountArgs = {
  projectId: Scalars['ID'];
};


export type Query_projectByLinkArgs = {
  publicLink: Scalars['String'];
};


export type Query_projectDefaultEventByLinkArgs = {
  projectPublicLink: Scalars['String'];
};


export type Query_projectEventArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_projectEventByLinkArgs = {
  projectPublicLink: Scalars['String'];
  publicLink: Scalars['String'];
};


export type Query_projectEventsArgs = {
  pagination: PaginationInput;
  where: ProjectEventWhereInput;
};


export type Query_projectFuelInfoArgs = {
  minFuel: Scalars['Float'];
  projectId: Scalars['ID'];
};


export type Query_projectParticipationCountArgs = {
  projectId: Scalars['ID'];
};


export type Query_projectUserInfoArgs = {
  projectId: Scalars['ID'];
};


export type Query_projectUserInfoByProviderArgs = {
  projectId: Scalars['ID'];
  provider: AuthProvider;
  providerId: Scalars['ID'];
};


export type Query_projectUsersWithUserDetailsArgs = {
  projectId: Scalars['ID'];
};


export type Query_projectsProgressArgs = {
  projectIds: Array<Scalars['ID']>;
};


export type Query_promotedEventsArgs = {
  ecosystems?: InputMaybe<Array<InputMaybe<Ecosystem>>>;
  promotionType: PromotionType;
};


export type Query_promotedProjectsArgs = {
  ecosystems?: InputMaybe<Array<InputMaybe<Ecosystem>>>;
  promotionType: PromotionType;
};


export type Query_referredUsersArgs = {
  eventId: Scalars['ID'];
};


export type Query_referredUsersForUserIdArgs = {
  eventId: Scalars['ID'];
  userId: Scalars['ID'];
};


export type Query_seasonArgs = {
  seasonId: Scalars['ID'];
};


export type Query_seasonsArgs = {
  projectId: Scalars['ID'];
};


export type Query_signingMessageArgs = {
  projectId: Scalars['ID'];
  walletAddress: Scalars['ID'];
};


export type Query_taskArgs = {
  projectId: Scalars['ID'];
  taskId: Scalars['ID'];
};


export type Query_tasksArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_tasksByParentIdArgs = {
  parentId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_userArgs = {
  userId: Scalars['ID'];
};


export type Query_userByProviderArgs = {
  projectId: Scalars['ID'];
  provider: AuthProvider;
  providerId: Scalars['String'];
};


export type Query_userEventParticipatedArgs = {
  pagination: PaginationInput;
  userId: Scalars['ID'];
};


export type Query_userEventRewardsArgs = {
  eventId: Scalars['ID'];
};


export type Query_userGlobalRankingArgs = {
  sorter: GlobalLeaderboardSortInput;
  where: GlobalLeaderboardWhereInput;
};


export type Query_userNotificationsArgs = {
  pagination: PaginationInput;
};


export type Query_userNotificationsByIdsArgs = {
  ids: Array<Scalars['ID']>;
};


export type Query_userProjectRankingArgs = {
  interval: LeaderboardInterval;
  projectId: Scalars['String'];
};


export type Query_userRewardsArgs = {
  pagination: PaginationInput;
  userId: Scalars['ID'];
};


export type Query_userRoleInProjectArgs = {
  projectId: Scalars['ID'];
};


export type Query_userTaskParticipationArgs = {
  eventId: Scalars['ID'];
};


export type Query_valueByKeyGroupArgs = {
  groupId: Scalars['ID'];
  key: Scalars['String'];
};


export type Query_verifyImageArgs = {
  imageUrl: Scalars['String'];
  prompt: Scalars['String'];
};


export type Query_widgetArgs = {
  id: Scalars['ID'];
};


export type Query_widgetsArgs = {
  eventId: Scalars['ID'];
  projectId: Scalars['ID'];
};


export type Query_winnersArgs = {
  pagination: PaginationInput;
  projectId: Scalars['ID'];
  where: WinnerWhereInput;
};

export type QuizQuestionOption = {
  __typename?: 'QuizQuestionOption';
  id: Scalars['String'];
  isCorrect?: Maybe<Scalars['Boolean']>;
  order: Scalars['Int'];
  text: Scalars['String'];
};

export enum QuizQuestionType {
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  SINGLE_CHOICE = 'SINGLE_CHOICE'
}

export type QuizTaskData = {
  __typename?: 'QuizTaskData';
  createdAt: Scalars['DateTime'];
  options?: Maybe<Array<QuizQuestionOption>>;
  questionType?: Maybe<QuizQuestionType>;
  updatedAt: Scalars['DateTime'];
};

export type QuizTaskDataInput = {
  options?: InputMaybe<Array<QuizTaskDataOptionInput>>;
  questionType?: InputMaybe<QuizQuestionType>;
};

export type QuizTaskDataOptionInput = {
  id: Scalars['String'];
  isCorrect?: InputMaybe<Scalars['Boolean']>;
  order: Scalars['Int'];
  text: Scalars['String'];
};

export type QuizTaskDataUpdateInput = {
  options?: InputMaybe<Array<QuizTaskDataOptionInput>>;
  questionType?: InputMaybe<QuizQuestionType>;
};

export type QuizTaskInput = {
  data?: InputMaybe<QuizTaskDataInput>;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type QuizTaskParticipationData = {
  __typename?: 'QuizTaskParticipationData';
  answers: Array<Scalars['String']>;
  correctAnswers?: Maybe<Array<Scalars['String']>>;
};

export type QuizTaskParticipationInput = {
  answers: Array<Scalars['String']>;
};

export type QuizTaskParticipationResult = {
  __typename?: 'QuizTaskParticipationResult';
  correctAnswers: Array<Scalars['String']>;
  insertResult: InsertResult;
  points: Scalars['Int'];
  xp: Scalars['Int'];
};

export type QuizTaskUpdateInput = {
  data?: InputMaybe<QuizTaskDataUpdateInput>;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type Referral = {
  __typename?: 'Referral';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  updatedAt: Scalars['DateTime'];
};

export type ReferralCode = {
  __typename?: 'ReferralCode';
  code: Scalars['String'];
  createdAt: Scalars['DateTime'];
  expiresAt?: Maybe<Scalars['DateTime']>;
  id: Scalars['ID'];
  referrals?: Maybe<Scalars['Int']>;
  updatedAt: Scalars['DateTime'];
};

export type RestParam = {
  __typename?: 'RestParam';
  key: Scalars['String'];
  paramType: RestParamType;
  value: Scalars['String'];
};

export type RestParamInput = {
  key: Scalars['String'];
  paramType: RestParamType;
  value: Scalars['String'];
};

export type RestParamParticipate = {
  key: Scalars['String'];
  value: Scalars['String'];
};

export enum RestParamType {
  ADDRESS_32_BYTE = 'ADDRESS_32_BYTE',
  ARR_STRING = 'ARR_STRING',
  BOOL = 'BOOL',
  INT = 'INT',
  JSON = 'JSON',
  STRING = 'STRING'
}

export type RestRawTaskData = {
  __typename?: 'RestRawTaskData';
  blockchainId?: Maybe<Scalars['String']>;
  bodyParams?: Maybe<Array<Maybe<RestParam>>>;
  createdAt: Scalars['DateTime'];
  headers?: Maybe<Array<Maybe<RestParam>>>;
  queryParams?: Maybe<Array<Maybe<RestParam>>>;
  requestType: RestRequestType;
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
  validation: Scalars['String'];
  verifiedWallet?: Maybe<Scalars['Boolean']>;
};

export type RestRawTaskDataInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  bodyParams?: InputMaybe<Array<InputMaybe<RestParamInput>>>;
  headers?: InputMaybe<Array<InputMaybe<RestParamInput>>>;
  queryParams?: InputMaybe<Array<InputMaybe<RestParamInput>>>;
  requestType: RestRequestType;
  url: Scalars['String'];
  validation: Scalars['String'];
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type RestRawTaskDataUpdateInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  bodyParams?: InputMaybe<Array<RestParamInput>>;
  headers?: InputMaybe<Array<RestParamInput>>;
  queryParams?: InputMaybe<Array<RestParamInput>>;
  requestType?: InputMaybe<RestRequestType>;
  url?: InputMaybe<Scalars['String']>;
  validation?: InputMaybe<Scalars['String']>;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type RestRawTaskInput = {
  data: RestRawTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  taskType: TaskType;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type RestRawTaskUpdateInput = {
  data: RestRawTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum RestRequestType {
  GET = 'GET',
  POST = 'POST'
}

export type RestTaskParticipationInput = {
  bodyParams?: InputMaybe<Array<InputMaybe<RestParamParticipate>>>;
  headers?: InputMaybe<Array<InputMaybe<RestParamParticipate>>>;
  queryParams?: InputMaybe<Array<InputMaybe<RestParamParticipate>>>;
};

export type RewardCertificate = {
  __typename?: 'RewardCertificate';
  amount: Scalars['String'];
  certificate: CertificateResult;
  claimIds: Array<Scalars['String']>;
  raw: Array<EventReward>;
  /** Only for gasless tx */
  txHash?: Maybe<Scalars['String']>;
  window: Scalars['String'];
  windowLimit: Scalars['String'];
};

export type RewardClaimResult = {
  __typename?: 'RewardClaimResult';
  raw: Array<EventReward>;
};

export enum RewardStatus {
  DRAFT = 'DRAFT',
  FAILED = 'FAILED',
  ISSUED = 'ISSUED',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS'
}

export enum RewardType {
  POINTS = 'POINTS',
  XP = 'XP'
}

export enum Role {
  ADMIN = 'ADMIN',
  OWNER = 'OWNER',
  REVIEWER = 'REVIEWER'
}

export enum RuleOperator {
  EQ = 'EQ',
  GT = 'GT',
  GTE = 'GTE',
  LT = 'LT',
  LTE = 'LTE',
  NE = 'NE'
}

export type Season = {
  __typename?: 'Season';
  createdAt: Scalars['DateTime'];
  id: Scalars['ID'];
  name: Scalars['String'];
  projectId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SecretCodeTaskData = {
  __typename?: 'SecretCodeTaskData';
  createdAt: Scalars['DateTime'];
  secretCodes: Array<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type SecretCodeTaskDataInput = {
  secretCodes: Array<Scalars['String']>;
};

export type SecretCodeTaskDataUpdateInput = {
  secretCodes?: InputMaybe<Array<Scalars['String']>>;
};

export type SecretCodeTaskInput = {
  data: SecretCodeTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SecretCodeTaskParticipationData = {
  __typename?: 'SecretCodeTaskParticipationData';
  code: Scalars['String'];
};

export type SecretCodeTaskParticipationInput = {
  code: Scalars['String'];
};

export type SecretCodeTaskUpdateInput = {
  data: SecretCodeTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SecretGiveawayData = {
  __typename?: 'SecretGiveawayData';
  deliveryType: DeliveryType;
  displayType: DisplayType;
  distributionMsg?: Maybe<Scalars['String']>;
  title: Scalars['String'];
  winnerCount?: Maybe<Scalars['Float']>;
};

export type SecretGiveawayDataInput = {
  deliveryType: DeliveryType;
  displayType: DisplayType;
  distributionMsg?: InputMaybe<Scalars['String']>;
  title: Scalars['String'];
  winnerCount?: InputMaybe<Scalars['Float']>;
};

export type SecretGiveawayDataUpdateInput = {
  deliveryType?: InputMaybe<DeliveryType>;
  displayType?: InputMaybe<DisplayType>;
  distributionMsg?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
  winnerCount?: InputMaybe<Scalars['Float']>;
};

export type SecretGiveawayInput = {
  condition?: GiveawayEventCondition;
  data: SecretGiveawayDataInput;
  description?: InputMaybe<Scalars['String']>;
  distributionType: DistributionType;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: Scalars['Boolean'];
  frequency?: InputMaybe<Frequency>;
  giveawayType: GiveawayType;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type SecretGiveawayUpdateInput = {
  condition?: InputMaybe<GiveawayEventCondition>;
  data: SecretGiveawayDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: InputMaybe<Scalars['Boolean']>;
  frequency?: InputMaybe<Frequency>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<Scalars['String']>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export enum Sector {
  BRIDGE = 'BRIDGE',
  CEFI = 'CEFI',
  CEX = 'CEX',
  COLLECTIBLES = 'COLLECTIBLES',
  DAO = 'DAO',
  DEFI = 'DEFI',
  DEX = 'DEX',
  DID = 'DID',
  GAMEFI = 'GAMEFI',
  INFRASTRUCTURE = 'INFRASTRUCTURE',
  LAUNCHPAD = 'LAUNCHPAD',
  METVERSE = 'METVERSE',
  NFT = 'NFT',
  PRIVACY = 'PRIVACY',
  SOCIAL = 'SOCIAL',
  STAKING = 'STAKING',
  STORAGE = 'STORAGE',
  WALLET = 'WALLET',
  WEB3 = 'WEB3'
}

export type ShopConfig = {
  __typename?: 'ShopConfig';
  amount: Scalars['String'];
  decimals?: Maybe<Scalars['Int']>;
  nfts?: Maybe<Array<Maybe<NftSwap>>>;
  points?: Maybe<Scalars['Int']>;
  ruleType: GiveawaySwapRuleType;
};

export type ShopConfigInput = {
  amount: Scalars['String'];
  decimals?: InputMaybe<Scalars['Int']>;
  nfts?: InputMaybe<Array<InputMaybe<NftSwapInput>>>;
  points?: InputMaybe<Scalars['Int']>;
  ruleType: GiveawaySwapRuleType;
};

export type SignTermsTaskData = {
  __typename?: 'SignTermsTaskData';
  blockchainId?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  terms: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SignTermsTaskDataInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  terms: Scalars['String'];
};

export type SignTermsTaskDataUpdateInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  terms?: InputMaybe<Scalars['String']>;
};

export type SignTermsTaskInput = {
  data: SignTermsTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  taskType: TaskType;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SignTermsTaskParticipationData = {
  __typename?: 'SignTermsTaskParticipationData';
  address?: Maybe<Scalars['String']>;
  signature?: Maybe<Scalars['String']>;
};

export type SignTermsTaskParticipationInput = {
  address: Scalars['String'];
  signature: Scalars['String'];
};

export type SignTermsTaskUpdateInput = {
  data: SignTermsTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SmartContractFunction = {
  __typename?: 'SmartContractFunction';
  anonymous?: Maybe<Scalars['Boolean']>;
  constant?: Maybe<Scalars['Boolean']>;
  inputs?: Maybe<Array<Maybe<FunctionIO>>>;
  name: Scalars['String'];
  outputs?: Maybe<Array<Maybe<FunctionIO>>>;
  payable?: Maybe<Scalars['Boolean']>;
  stateMutability: Scalars['String'];
  type: Scalars['String'];
};

export type SmartContractFunctionInput = {
  anonymous?: InputMaybe<Scalars['Boolean']>;
  constant?: InputMaybe<Scalars['Boolean']>;
  inputs?: InputMaybe<Array<InputMaybe<FunctionIOInput>>>;
  name: Scalars['String'];
  outputs?: InputMaybe<Array<InputMaybe<FunctionIOInput>>>;
  payable?: InputMaybe<Scalars['Boolean']>;
  stateMutability: Scalars['String'];
  type: Scalars['String'];
};

export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

export type SortInput = {
  direction: SortDirection;
  sortKey: Scalars['String'];
};

export type StrippedReferredUser = {
  __typename?: 'StrippedReferredUser';
  createdAt: Scalars['DateTime'];
  id: Scalars['String'];
  referredHandle: Scalars['String'];
  referredUserId: Scalars['String'];
  rejected: Scalars['Boolean'];
  updatedAt: Scalars['DateTime'];
};

export type SubTaskStats = {
  __typename?: 'SubTaskStats';
  count?: Maybe<Scalars['Int']>;
  totalPoints?: Maybe<Scalars['Int']>;
  totalXp?: Maybe<Scalars['Int']>;
};

export type SubgraphRawTaskData = {
  __typename?: 'SubgraphRawTaskData';
  blockchainId: Scalars['String'];
  blockchainType: BlockchainType;
  createdAt: Scalars['DateTime'];
  query: Scalars['String'];
  ss58Prefix?: Maybe<Scalars['Int']>;
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
  validation: Scalars['String'];
  verifiedWallet?: Maybe<Scalars['Boolean']>;
};

export type SubgraphRawTaskDataInput = {
  blockchainId: Scalars['String'];
  blockchainType: BlockchainType;
  query: Scalars['String'];
  ss58Prefix?: InputMaybe<Scalars['Int']>;
  url: Scalars['String'];
  validation: Scalars['String'];
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type SubgraphRawTaskDataUpdateInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  blockchainType?: InputMaybe<BlockchainType>;
  query?: InputMaybe<Scalars['String']>;
  ss58Prefix?: InputMaybe<Scalars['Int']>;
  url?: InputMaybe<Scalars['String']>;
  validation?: InputMaybe<Scalars['String']>;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type SubgraphRawTaskInput = {
  data: SubgraphRawTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubgraphRawTaskUpdateInput = {
  data: SubgraphRawTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubmissionWhereInput = {
  date?: InputMaybe<DateInput>;
  eventId: Scalars['ID'];
  status?: InputMaybe<Array<ParticipationStatus>>;
  taskIds?: InputMaybe<Array<InputMaybe<Scalars['ID']>>>;
  userId?: InputMaybe<Scalars['String']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  onOAuthComplete?: Maybe<AuthSubscriptionResponse>;
  onUserNotification: Array<Scalars['ID']>;
};


export type Subscription_onOAuthCompleteArgs = {
  sessionId: Scalars['ID'];
};

export type SubsocialCommentTaskData = {
  __typename?: 'SubsocialCommentTaskData';
  createdAt: Scalars['DateTime'];
  postId: Scalars['String'];
  postUrl: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SubsocialCommentTaskDataInput = {
  postId: Scalars['String'];
  postUrl: Scalars['String'];
};

export type SubsocialCommentTaskDataUpdateInput = {
  postId?: InputMaybe<Scalars['String']>;
  postUrl?: InputMaybe<Scalars['String']>;
};

export type SubsocialCommentTaskInput = {
  data: SubsocialCommentTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialCommentTaskParticipationInput = {
  postUrl: Scalars['String'];
};

export type SubsocialCommentTaskUpdateInput = {
  data: SubsocialCommentTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubsocialFollowTaskData = {
  __typename?: 'SubsocialFollowTaskData';
  createdAt: Scalars['DateTime'];
  handle: Scalars['String'];
  spaceId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SubsocialFollowTaskDataInput = {
  handle: Scalars['String'];
  spaceId: Scalars['String'];
};

export type SubsocialFollowTaskDataUpdateInput = {
  handle?: InputMaybe<Scalars['String']>;
  spaceId?: InputMaybe<Scalars['String']>;
};

export type SubsocialFollowTaskInput = {
  data: SubsocialFollowTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialFollowTaskUpdateInput = {
  data: SubsocialFollowTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubsocialPostTaskData = {
  __typename?: 'SubsocialPostTaskData';
  createdAt: Scalars['DateTime'];
  handle: Scalars['String'];
  spaceId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SubsocialPostTaskDataInput = {
  handle: Scalars['String'];
  spaceId: Scalars['String'];
};

export type SubsocialPostTaskDataUpdateInput = {
  handle?: InputMaybe<Scalars['String']>;
  spaceId?: InputMaybe<Scalars['String']>;
};

export type SubsocialPostTaskInput = {
  data: SubsocialPostTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialPostTaskParticipationInput = {
  postUrl: Scalars['String'];
};

export type SubsocialPostTaskUpdateInput = {
  data: SubsocialPostTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubsocialProfileTaskInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialProfileTaskUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubsocialShareTaskData = {
  __typename?: 'SubsocialShareTaskData';
  createdAt: Scalars['DateTime'];
  postId: Scalars['String'];
  postUrl: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SubsocialShareTaskDataInput = {
  postId: Scalars['String'];
  postUrl: Scalars['String'];
};

export type SubsocialShareTaskDataUpdateInput = {
  postId?: InputMaybe<Scalars['String']>;
  postUrl?: InputMaybe<Scalars['String']>;
};

export type SubsocialShareTaskInput = {
  data: SubsocialShareTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialShareTaskParticipationInput = {
  postUrl: Scalars['String'];
};

export type SubsocialShareTaskUpdateInput = {
  data: SubsocialShareTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubsocialSpaceTaskInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialSpaceTaskUpdateInput = {
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubsocialUpvoteTaskData = {
  __typename?: 'SubsocialUpvoteTaskData';
  createdAt: Scalars['DateTime'];
  postId: Scalars['String'];
  postUrl: Scalars['String'];
  updatedAt: Scalars['DateTime'];
};

export type SubsocialUpvoteTaskDataInput = {
  postId: Scalars['String'];
  postUrl: Scalars['String'];
};

export type SubsocialUpvoteTaskDataUpdateInput = {
  postId?: InputMaybe<Scalars['String']>;
  postUrl?: InputMaybe<Scalars['String']>;
};

export type SubsocialUpvoteTaskInput = {
  data: SubsocialUpvoteTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubsocialUpvoteTaskUpdateInput = {
  data: SubsocialUpvoteTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type SubstrateQueryTaskData = {
  __typename?: 'SubstrateQueryTaskData';
  blockchainId: Scalars['String'];
  createdAt: Scalars['DateTime'];
  extra?: Maybe<Array<Maybe<FormAnswer>>>;
  inputParams?: Maybe<Array<Maybe<FormAnswerTaskData>>>;
  method: Scalars['String'];
  section: Scalars['String'];
  taskType?: Maybe<SubstrateTaskType>;
  updatedAt: Scalars['DateTime'];
  validation: Scalars['String'];
  verifiedWallet?: Maybe<Scalars['Boolean']>;
};

export type SubstrateQueryTaskDataInput = {
  blockchainId: Scalars['String'];
  extra?: InputMaybe<Array<InputMaybe<FormAnswerInput>>>;
  inputParams?: InputMaybe<Array<InputMaybe<FormAnswerTaskDataInput>>>;
  method: Scalars['String'];
  section: Scalars['String'];
  taskType?: InputMaybe<SubstrateTaskType>;
  validation: Scalars['String'];
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type SubstrateQueryTaskDataUpdateInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  extra?: InputMaybe<Array<FormAnswerInput>>;
  inputParams?: InputMaybe<Array<FormAnswerTaskDataInput>>;
  method?: InputMaybe<Scalars['String']>;
  section?: InputMaybe<Scalars['String']>;
  taskType?: InputMaybe<SubstrateTaskType>;
  validation?: InputMaybe<Scalars['String']>;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
};

export type SubstrateQueryTaskInput = {
  data: SubstrateQueryTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type SubstrateQueryTaskParticipationInput = {
  answers?: InputMaybe<Array<FormAnswerInput>>;
};

export type SubstrateQueryTaskUpdateInput = {
  data: SubstrateQueryTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum SubstrateTaskType {
  SUBSTRATE_ASSET_BALANCE = 'SUBSTRATE_ASSET_BALANCE',
  SUBSTRATE_BALANCE = 'SUBSTRATE_BALANCE',
  SUBSTRATE_BOND_POOL = 'SUBSTRATE_BOND_POOL',
  SUBSTRATE_HOLD_NFT = 'SUBSTRATE_HOLD_NFT',
  SUBSTRATE_NOMINATE = 'SUBSTRATE_NOMINATE',
  SUBSTRATE_QUERY = 'SUBSTRATE_QUERY',
  SUBSTRATE_STAKE = 'SUBSTRATE_STAKE'
}

export type SyncBlockchainPoolResult = {
  __typename?: 'SyncBlockchainPoolResult';
  amount: Scalars['String'];
  status: TransactionStatus;
};

export type Task = {
  __typename?: 'Task';
  /** Only applicable for custom apps */
  appKey?: Maybe<Scalars['String']>;
  appType: AppType;
  createdAt: Scalars['DateTime'];
  description?: Maybe<Scalars['String']>;
  frequency: Frequency;
  guardConfig?: Maybe<TaskGuard>;
  hidden?: Maybe<Scalars['Boolean']>;
  iconUrl?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  info: TaskInfoUnion;
  order: Scalars['Int'];
  parentId?: Maybe<Scalars['String']>;
  participantCount?: Maybe<Scalars['Int']>;
  points: Scalars['Int'];
  subTaskStats: SubTaskStats;
  /** Only applicable for custom apps and custom tasks */
  taskKey?: Maybe<Scalars['String']>;
  taskType: TaskType;
  title?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  verify: VerifyType;
  xp: Scalars['Int'];
};

export type TaskConfig = {
  __typename?: 'TaskConfig';
  comingSoon?: Maybe<Scalars['Boolean']>;
  connectionGuards?: Maybe<Array<Maybe<AuthProvider>>>;
  customVerification?: Maybe<Scalars['Boolean']>;
  description: Scalars['String'];
  hidden?: Maybe<Scalars['Boolean']>;
  iconUrl?: Maybe<Scalars['String']>;
  isBeta?: Maybe<Scalars['Boolean']>;
  isPremium?: Maybe<Scalars['Boolean']>;
  publicUi?: Maybe<Array<Maybe<WidgetTaskData>>>;
  title: Scalars['String'];
  ui?: Maybe<Array<Maybe<WidgetTaskData>>>;
};

export type TaskDateParticipationsData = {
  __typename?: 'TaskDateParticipationsData';
  date: Scalars['DateTime'];
  name: Scalars['String'];
  taskId: Scalars['String'];
  title: Scalars['String'];
  userCount: Scalars['Int'];
};

export type TaskGuard = {
  __typename?: 'TaskGuard';
  condition?: Maybe<GiveawayRuleCondition>;
  rules?: Maybe<Array<Maybe<TaskRule>>>;
};

export type TaskGuardInput = {
  condition?: InputMaybe<GiveawayRuleCondition>;
  rules?: InputMaybe<Array<InputMaybe<TaskRuleInput>>>;
};

export type TaskInfoUnion = AirboostReferralTaskData | AirquestFollowTaskData | BlogCommentTaskData | CustomTaskData | DiscordJoinTaskData | EmailWhitelistTaskData | EvmContractInteractTaskData | FaucetRawTaskData | FlashcardTaskData | FormAnswerTaskDataItems | KickstarterTaskData | LinkTaskData | LuckydrawTaskData | MobileAppTaskData | NullableTaskData | ProducthuntFollowTaskData | ProducthuntUpvoteTaskData | QuizTaskData | RestRawTaskData | SecretCodeTaskData | SignTermsTaskData | SubgraphRawTaskData | SubsocialCommentTaskData | SubsocialFollowTaskData | SubsocialPostTaskData | SubsocialShareTaskData | SubsocialUpvoteTaskData | SubstrateQueryTaskData | TelegramJoinTaskData | TwitterFollowTaskData | TwitterLikeRetweetTaskData | TwitterLikeTaskData | TwitterPostTaskData | TwitterRetweetTaskData | TwitterUgcTaskData | TwitterWhitelistTaskData | UploadTaskData | WalletAddressTaskData;

export type TaskParticipation = {
  __typename?: 'TaskParticipation';
  auth?: Maybe<Auth>;
  createdAt: Scalars['DateTime'];
  hashedIp?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  info: TaskParticipationInfoUnion;
  points: Scalars['Int'];
  primaryAuth?: Maybe<Auth>;
  provider?: Maybe<AuthProvider>;
  providerId?: Maybe<Scalars['String']>;
  status: ParticipationStatus;
  task?: Maybe<Task>;
  taskId: Scalars['String'];
  updatedAt: Scalars['DateTime'];
  userId: Scalars['String'];
  xp: Scalars['Int'];
};

export type TaskParticipationInfoUnion = BlogCommentTaskParticipationData | BlogWriteTaskParticipationData | FaucetTaskParticipationData | FormAnswerTaskParticipationData | LuckydrawTaskParticipationData | MobileAppTaskParticipationData | NullableTaskData | QuizTaskParticipationData | SecretCodeTaskParticipationData | SignTermsTaskParticipationData | TwitterPostTaskParticipationData | TwitterUgcTaskParticipationData | UploadTaskParticipationData | WalletAddressTaskParticipationData;

export type TaskRule = {
  __typename?: 'TaskRule';
  dateValue?: Maybe<Scalars['DateTime']>;
  intValue?: Maybe<Scalars['Int']>;
  operator: RuleOperator;
  ruleType: TaskRuleType;
  stringValue?: Maybe<Scalars['String']>;
};

export type TaskRuleInput = {
  dateValue?: InputMaybe<Scalars['DateTime']>;
  intValue?: InputMaybe<Scalars['Int']>;
  operator: RuleOperator;
  ruleType: TaskRuleType;
  stringValue?: InputMaybe<Scalars['String']>;
};

export enum TaskRuleType {
  DATE = 'DATE',
  DISCORD_ROLE = 'DISCORD_ROLE',
  MAX_PARTICIPANTS = 'MAX_PARTICIPANTS',
  TASK_ID = 'TASK_ID',
  XP = 'XP'
}

export type TaskSubmissionsData = {
  __typename?: 'TaskSubmissionsData';
  data: Array<TaskParticipation>;
  total: Scalars['Float'];
};

export enum TaskType {
  AIRBOOST_REFERRAL = 'AIRBOOST_REFERRAL',
  AIRQUEST_FOLLOW = 'AIRQUEST_FOLLOW',
  BLOG_COMMENT = 'BLOG_COMMENT',
  BLOG_WRITE = 'BLOG_WRITE',
  CHECKIN_DAILY = 'CHECKIN_DAILY',
  DISCORD_JOIN = 'DISCORD_JOIN',
  EMAIL_ADDRESS = 'EMAIL_ADDRESS',
  EMAIL_SUBSCRIBE = 'EMAIL_SUBSCRIBE',
  EMAIL_WHITELIST = 'EMAIL_WHITELIST',
  EVM_CONTRACT = 'EVM_CONTRACT',
  FAUCET_DOTSAMA = 'FAUCET_DOTSAMA',
  FAUCET_EVM = 'FAUCET_EVM',
  FLASHCARD_VIEW = 'FLASHCARD_VIEW',
  FORM_ANSWER = 'FORM_ANSWER',
  INSTAGRAM_SHARE = 'INSTAGRAM_SHARE',
  INSTAGRAM_VIEW = 'INSTAGRAM_VIEW',
  INSTAGRAM_VISIT = 'INSTAGRAM_VISIT',
  KICKSTARTER_SUPPORT = 'KICKSTARTER_SUPPORT',
  LUCKYDRAW_BOX = 'LUCKYDRAW_BOX',
  LUCKYDRAW_PLAY = 'LUCKYDRAW_PLAY',
  LUCKYDRAW_SLOT = 'LUCKYDRAW_SLOT',
  MOBILE_APP_INSTALL = 'MOBILE_APP_INSTALL',
  PRODUCTHUNT_FOLLOW = 'PRODUCTHUNT_FOLLOW',
  PRODUCTHUNT_UPVOTE = 'PRODUCTHUNT_UPVOTE',
  QUIZ_PLAY = 'QUIZ_PLAY',
  REST_DOTSAMA = 'REST_DOTSAMA',
  REST_EVM = 'REST_EVM',
  REST_RAW = 'REST_RAW',
  SECRET_CODE_VALIDATE = 'SECRET_CODE_VALIDATE',
  SUBGRAPH_RAW = 'SUBGRAPH_RAW',
  SUBQUERY_RAW = 'SUBQUERY_RAW',
  SUBSOCIAL_COMMENT = 'SUBSOCIAL_COMMENT',
  SUBSOCIAL_FOLLOW = 'SUBSOCIAL_FOLLOW',
  SUBSOCIAL_POST = 'SUBSOCIAL_POST',
  SUBSOCIAL_PROFILE = 'SUBSOCIAL_PROFILE',
  SUBSOCIAL_SHARE = 'SUBSOCIAL_SHARE',
  SUBSOCIAL_SPACE = 'SUBSOCIAL_SPACE',
  SUBSOCIAL_UPVOTE = 'SUBSOCIAL_UPVOTE',
  SUBSQUID_RAW = 'SUBSQUID_RAW',
  SUBSTRATE_ASSET_BALANCE = 'SUBSTRATE_ASSET_BALANCE',
  SUBSTRATE_BALANCE = 'SUBSTRATE_BALANCE',
  SUBSTRATE_BOND_POOL = 'SUBSTRATE_BOND_POOL',
  SUBSTRATE_HOLD_NFT = 'SUBSTRATE_HOLD_NFT',
  SUBSTRATE_NOMINATE = 'SUBSTRATE_NOMINATE',
  SUBSTRATE_QUERY = 'SUBSTRATE_QUERY',
  SUBSTRATE_STAKE = 'SUBSTRATE_STAKE',
  TELEGRAM_JOIN = 'TELEGRAM_JOIN',
  TERMS_DOTSAMA = 'TERMS_DOTSAMA',
  TERMS_EVM = 'TERMS_EVM',
  TERMS_TEXT = 'TERMS_TEXT',
  TWITTER_FOLLOW = 'TWITTER_FOLLOW',
  TWITTER_JOIN = 'TWITTER_JOIN',
  TWITTER_LIKE = 'TWITTER_LIKE',
  TWITTER_LIKE_RETWEET = 'TWITTER_LIKE_RETWEET',
  TWITTER_POST = 'TWITTER_POST',
  TWITTER_RETWEET = 'TWITTER_RETWEET',
  TWITTER_UGC = 'TWITTER_UGC',
  TWITTER_UPLOAD = 'TWITTER_UPLOAD',
  TWITTER_WHITELIST = 'TWITTER_WHITELIST',
  UPLOAD_FILE = 'UPLOAD_FILE',
  URL_SHARE = 'URL_SHARE',
  URL_VIEW = 'URL_VIEW',
  URL_VISIT = 'URL_VISIT',
  WALLET_DOTSAMA = 'WALLET_DOTSAMA',
  WALLET_EVM = 'WALLET_EVM',
  YOUTUBE_SHARE = 'YOUTUBE_SHARE',
  YOUTUBE_VIEW = 'YOUTUBE_VIEW',
  YOUTUBE_VISIT = 'YOUTUBE_VISIT'
}

export enum TelegramChatType {
  CHANNEL = 'CHANNEL',
  SUPER_GROUP = 'SUPER_GROUP'
}

export type TelegramJoinTaskData = {
  __typename?: 'TelegramJoinTaskData';
  chatId: Scalars['String'];
  createdAt: Scalars['DateTime'];
  title: Scalars['String'];
  type: TelegramChatType;
  updatedAt: Scalars['DateTime'];
  username: Scalars['String'];
};

export type TelegramJoinTaskDataInput = {
  chatId: Scalars['String'];
  title: Scalars['String'];
  type: TelegramChatType;
  username: Scalars['String'];
};

export type TelegramJoinTaskDataUpdateInput = {
  chatId?: InputMaybe<Scalars['String']>;
  title?: InputMaybe<Scalars['String']>;
  type?: InputMaybe<TelegramChatType>;
  username?: InputMaybe<Scalars['String']>;
};

export type TelegramJoinTaskInput = {
  data: TelegramJoinTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TelegramJoinTaskUpdateInput = {
  data: TelegramJoinTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum TemplateTags {
  COMING_SOON = 'COMING_SOON',
  POPULAR = 'POPULAR',
  TELEGRAM = 'TELEGRAM',
  TWITTER = 'TWITTER',
  WEEK_1 = 'WEEK_1',
  WEEK_2 = 'WEEK_2',
  WEEK_3 = 'WEEK_3',
  WEEK_4 = 'WEEK_4',
  WEEK_5 = 'WEEK_5',
  WEEK_6 = 'WEEK_6',
  WEEK_7 = 'WEEK_7'
}

export enum TemplateView {
  DEFAULT = 'DEFAULT',
  LARGE = 'LARGE'
}

export enum ThemeMode {
  DARK = 'DARK',
  LIGHT = 'LIGHT'
}

export type TokenUsage = {
  __typename?: 'TokenUsage';
  c?: Maybe<Scalars['Float']>;
  p?: Maybe<Scalars['Float']>;
  t?: Maybe<Scalars['Float']>;
};

export enum TransactionStatus {
  FAILED = 'FAILED',
  ISSUED = 'ISSUED',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS'
}

export enum TweetSubmitType {
  QUOTE = 'QUOTE',
  REPLY = 'REPLY',
  TWEET = 'TWEET'
}

export enum TwitterAction {
  LIKE = 'LIKE',
  RETWEET = 'RETWEET'
}

export type TwitterFollowTaskData = {
  __typename?: 'TwitterFollowTaskData';
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type TwitterFollowTaskDataInput = {
  url: Scalars['String'];
};

export type TwitterFollowTaskDataUpdateInput = {
  url?: InputMaybe<Scalars['String']>;
};

export type TwitterFollowTaskInput = {
  data: TwitterFollowTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterFollowTaskUpdateInput = {
  data: TwitterFollowTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type TwitterLikeRetweetTaskData = {
  __typename?: 'TwitterLikeRetweetTaskData';
  actions: Array<TwitterAction>;
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type TwitterLikeRetweetTaskDataInput = {
  actions: Array<TwitterAction>;
  url: Scalars['String'];
};

export type TwitterLikeRetweetTaskDataUpdateInput = {
  actions?: InputMaybe<Array<TwitterAction>>;
  url?: InputMaybe<Scalars['String']>;
};

export type TwitterLikeRetweetTaskInput = {
  data: TwitterLikeRetweetTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterLikeRetweetTaskUpdateInput = {
  data: TwitterLikeRetweetTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type TwitterLikeTaskData = {
  __typename?: 'TwitterLikeTaskData';
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type TwitterLikeTaskDataInput = {
  url: Scalars['String'];
};

export type TwitterLikeTaskDataUpdateInput = {
  url?: InputMaybe<Scalars['String']>;
};

export type TwitterLikeTaskInput = {
  data: TwitterLikeTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterLikeTaskUpdateInput = {
  data: TwitterLikeTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type TwitterPostTaskData = {
  __typename?: 'TwitterPostTaskData';
  content?: Maybe<Scalars['String']>;
  createdAt: Scalars['DateTime'];
  hashtags?: Maybe<Array<Scalars['String']>>;
  mentionsCount?: Maybe<Scalars['Int']>;
  minimumMediaEntries?: Maybe<Scalars['Int']>;
  type?: Maybe<TweetSubmitType>;
  updatedAt: Scalars['DateTime'];
  url?: Maybe<Scalars['String']>;
  userMentions?: Maybe<Array<Scalars['String']>>;
};

export type TwitterPostTaskDataInput = {
  content?: InputMaybe<Scalars['String']>;
  hashtags?: InputMaybe<Array<Scalars['String']>>;
  mentionsCount?: InputMaybe<Scalars['Int']>;
  minimumMediaEntries?: InputMaybe<Scalars['Int']>;
  type?: InputMaybe<TweetSubmitType>;
  url?: InputMaybe<Scalars['String']>;
  userMentions?: InputMaybe<Array<Scalars['String']>>;
};

export type TwitterPostTaskDataUpdateInput = {
  content?: InputMaybe<Scalars['String']>;
  hashtags?: InputMaybe<Array<Scalars['String']>>;
  mentionsCount?: InputMaybe<Scalars['Int']>;
  minimumMediaEntries?: InputMaybe<Scalars['Int']>;
  type?: InputMaybe<TweetSubmitType>;
  url?: InputMaybe<Scalars['String']>;
  userMentions?: InputMaybe<Array<Scalars['String']>>;
};

export type TwitterPostTaskInput = {
  data: TwitterPostTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterPostTaskParticipationData = {
  __typename?: 'TwitterPostTaskParticipationData';
  url: Scalars['String'];
};

export type TwitterPostTaskParticipationInput = {
  url: Scalars['String'];
};

export type TwitterPostTaskUpdateInput = {
  data: TwitterPostTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type TwitterRetweetTaskData = {
  __typename?: 'TwitterRetweetTaskData';
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  url: Scalars['String'];
};

export type TwitterRetweetTaskDataInput = {
  url: Scalars['String'];
};

export type TwitterRetweetTaskDataUpdateInput = {
  url?: InputMaybe<Scalars['String']>;
};

export type TwitterRetweetTaskInput = {
  data: TwitterRetweetTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterRetweetTaskUpdateInput = {
  data: TwitterRetweetTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type TwitterUgcTaskData = {
  __typename?: 'TwitterUgcTaskData';
  createdAt: Scalars['DateTime'];
  hashtags?: Maybe<Array<Scalars['String']>>;
  includeText?: Maybe<Scalars['Boolean']>;
  mentionsCount?: Maybe<Scalars['Int']>;
  minimumMediaEntries?: Maybe<Scalars['Int']>;
  type?: Maybe<TweetSubmitType>;
  updatedAt: Scalars['DateTime'];
  url?: Maybe<Scalars['String']>;
  userMentions?: Maybe<Array<Scalars['String']>>;
};

export type TwitterUgcTaskDataInput = {
  hashtags?: InputMaybe<Array<Scalars['String']>>;
  includeText?: InputMaybe<Scalars['Boolean']>;
  mentionsCount?: InputMaybe<Scalars['Int']>;
  minimumMediaEntries?: InputMaybe<Scalars['Int']>;
  type?: InputMaybe<TweetSubmitType>;
  url?: InputMaybe<Scalars['String']>;
  userMentions?: InputMaybe<Array<Scalars['String']>>;
};

export type TwitterUgcTaskDataUpdateInput = {
  hashtags?: InputMaybe<Array<Scalars['String']>>;
  includeText?: InputMaybe<Scalars['Boolean']>;
  mentionsCount?: InputMaybe<Scalars['Int']>;
  minimumMediaEntries?: InputMaybe<Scalars['Int']>;
  type?: InputMaybe<TweetSubmitType>;
  url?: InputMaybe<Scalars['String']>;
  userMentions?: InputMaybe<Array<Scalars['String']>>;
};

export type TwitterUgcTaskInput = {
  data: TwitterUgcTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterUgcTaskParticipationData = {
  __typename?: 'TwitterUgcTaskParticipationData';
  media?: Maybe<Array<Scalars['String']>>;
  tweet?: Maybe<Scalars['String']>;
  url: Scalars['String'];
};

export type TwitterUgcTaskParticipationInput = {
  media?: InputMaybe<Array<Scalars['String']>>;
  tweet?: InputMaybe<Scalars['String']>;
  url: Scalars['String'];
};

export type TwitterUgcTaskUpdateInput = {
  data: TwitterUgcTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type TwitterWhitelistTaskData = {
  __typename?: 'TwitterWhitelistTaskData';
  createdAt: Scalars['DateTime'];
  updatedAt: Scalars['DateTime'];
  /** List of twitter handles to whitelist */
  whitelist?: Maybe<Array<Scalars['String']>>;
};

export type TwitterWhitelistTaskDataInput = {
  /** List of twitter handles to whitelist */
  whitelist?: InputMaybe<Array<Scalars['String']>>;
};

export type TwitterWhitelistTaskDataUpdateInput = {
  /** List of twitter handles to whitelist */
  whitelist?: InputMaybe<Array<Scalars['String']>>;
};

export type TwitterWhitelistTaskInput = {
  data: TwitterWhitelistTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type TwitterWhitelistTaskUpdateInput = {
  data: TwitterWhitelistTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type UpdateResult = {
  __typename?: 'UpdateResult';
  affected?: Maybe<Scalars['Int']>;
};

export type UploadTaskData = {
  __typename?: 'UploadTaskData';
  createdAt: Scalars['DateTime'];
  fileTypes?: Maybe<Array<Scalars['String']>>;
  maxCount: Scalars['Int'];
  maxSize?: Maybe<Scalars['Int']>;
  prompt?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  /** Example files */
  urls?: Maybe<Array<Scalars['String']>>;
};

export type UploadTaskDataInput = {
  fileTypes?: InputMaybe<Array<Scalars['String']>>;
  maxCount: Scalars['Int'];
  maxSize?: InputMaybe<Scalars['Int']>;
  prompt?: InputMaybe<Scalars['String']>;
  /** Example files */
  urls?: InputMaybe<Array<Scalars['String']>>;
};

export type UploadTaskDataUpdateInput = {
  fileTypes?: InputMaybe<Array<Scalars['String']>>;
  maxCount?: InputMaybe<Scalars['Int']>;
  maxSize?: InputMaybe<Scalars['Int']>;
  prompt?: InputMaybe<Scalars['String']>;
  /** Example files */
  urls?: InputMaybe<Array<Scalars['String']>>;
};

export type UploadTaskInput = {
  data: UploadTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type UploadTaskParticipationData = {
  __typename?: 'UploadTaskParticipationData';
  aiVerification?: Maybe<AIVerification>;
  fileTypes?: Maybe<Array<Scalars['String']>>;
  maxCount: Scalars['Int'];
  maxSize?: Maybe<Scalars['Int']>;
  /** Example files */
  urls?: Maybe<Array<Scalars['String']>>;
};

export type UploadTaskParticipationInput = {
  urls: Array<Scalars['String']>;
};

export type UploadTaskUpdateInput = {
  data: UploadTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export type User = {
  __typename?: 'User';
  auth: Array<Auth>;
  auths: Array<Auth>;
  avatar?: Maybe<Scalars['String']>;
  cookieConsent?: Maybe<CookieConsent>;
  createdAt: Scalars['DateTime'];
  email?: Maybe<Scalars['String']>;
  firstName?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  lastName?: Maybe<Scalars['String']>;
  onboarded?: Maybe<Array<Onboarding>>;
  updatedAt: Scalars['DateTime'];
  username?: Maybe<Scalars['String']>;
};

export type UserCountPoint = {
  __typename?: 'UserCountPoint';
  sum: Scalars['Int'];
  weekStart: Scalars['DateTime'];
};

export enum UserCountType {
  TOTAL = 'TOTAL',
  WEEKLY = 'WEEKLY'
}

export type UserInput = {
  avatar?: InputMaybe<Scalars['String']>;
  email?: InputMaybe<Scalars['String']>;
  firstName?: InputMaybe<Scalars['String']>;
  lastName?: InputMaybe<Scalars['String']>;
  username?: InputMaybe<Scalars['String']>;
};

export type UserNotification = {
  __typename?: 'UserNotification';
  createdAt: Scalars['DateTime'];
  eventPublicLink?: Maybe<Scalars['String']>;
  eventTitle?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  message?: Maybe<Scalars['String']>;
  notificationType: UserNotificationType;
  projectName?: Maybe<Scalars['String']>;
  projectPublicLink?: Maybe<Scalars['String']>;
  read: Scalars['Boolean'];
  taskId?: Maybe<Scalars['String']>;
  taskTitle?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
};

export type UserNotificationData = {
  __typename?: 'UserNotificationData';
  data: Array<Maybe<UserNotification>>;
  total: Scalars['Float'];
  unreadCount: Scalars['Int'];
};

export enum UserNotificationType {
  CLAIM_FAILED = 'CLAIM_FAILED',
  CLAIM_INPROGRESS = 'CLAIM_INPROGRESS',
  CLAIM_SUCCESS = 'CLAIM_SUCCESS',
  NEW_PROJECT_EVENT = 'NEW_PROJECT_EVENT',
  NEW_SHOP_ITEM = 'NEW_SHOP_ITEM',
  REWARD_SETTLED = 'REWARD_SETTLED',
  TASK_FAILED = 'TASK_FAILED',
  TASK_INPROGRESS = 'TASK_INPROGRESS',
  TASK_SUCCESS = 'TASK_SUCCESS'
}

export type UsernameInput = {
  username?: InputMaybe<Scalars['String']>;
};

export type VerificationResult = {
  __typename?: 'VerificationResult';
  aiVerification?: Maybe<AIVerification>;
};

export enum VerifyType {
  AI = 'AI',
  AUTO = 'AUTO',
  MANUAL = 'MANUAL',
  NONE = 'NONE'
}

export type WalletAddressDateData = {
  __typename?: 'WalletAddressDateData';
  count: Scalars['Int'];
  date: Scalars['DateTime'];
};

export type WalletAddressTaskData = {
  __typename?: 'WalletAddressTaskData';
  blockchainId: Scalars['String'];
  createdAt: Scalars['DateTime'];
  excludedWallets?: Maybe<Array<Web3WalletType>>;
  updatedAt: Scalars['DateTime'];
  verification: WalletVerificationType;
  verifiedWallet?: Maybe<Scalars['Boolean']>;
  verify?: Maybe<Scalars['Boolean']>;
  /** This is a list of whitelisted when verification is list address */
  walletList?: Maybe<Array<Scalars['String']>>;
};

export type WalletAddressTaskDataInput = {
  blockchainId: Scalars['String'];
  excludedWallets?: InputMaybe<Array<Web3WalletType>>;
  verification?: WalletVerificationType;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
  verify?: InputMaybe<Scalars['Boolean']>;
  /** This is a list of whitelisted when verification is list address */
  walletList?: InputMaybe<Array<Scalars['String']>>;
};

export type WalletAddressTaskDataUpdateInput = {
  blockchainId?: InputMaybe<Scalars['String']>;
  excludedWallets?: InputMaybe<Array<Web3WalletType>>;
  verification?: InputMaybe<WalletVerificationType>;
  verifiedWallet?: InputMaybe<Scalars['Boolean']>;
  verify?: InputMaybe<Scalars['Boolean']>;
  /** This is a list of whitelisted when verification is list address */
  walletList?: InputMaybe<Array<Scalars['String']>>;
};

export type WalletAddressTaskInput = {
  data: WalletAddressTaskDataInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points: Scalars['Int'];
  taskType: TaskType;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp: Scalars['Int'];
};

export type WalletAddressTaskParticipationData = {
  __typename?: 'WalletAddressTaskParticipationData';
  address: Scalars['String'];
  verified?: Maybe<Scalars['Boolean']>;
};

export type WalletAddressTaskUpdateInput = {
  data: WalletAddressTaskDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  frequency?: InputMaybe<Frequency>;
  guardConfig?: InputMaybe<TaskGuardInput>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  iconUrl?: InputMaybe<Scalars['String']>;
  integrations?: InputMaybe<Array<IntegrationTaskInput>>;
  parentId?: InputMaybe<Scalars['String']>;
  points?: InputMaybe<Scalars['Int']>;
  title?: InputMaybe<Scalars['String']>;
  verify?: InputMaybe<VerifyType>;
  xp?: InputMaybe<Scalars['Int']>;
};

export enum WalletVerificationType {
  ANY = 'ANY',
  LIST = 'LIST'
}

export enum Web3WalletType {
  DOTSAMA_MANUAL = 'DOTSAMA_MANUAL',
  DOTSAMA_NOVA = 'DOTSAMA_NOVA',
  DOTSAMA_POLKADOT_JS = 'DOTSAMA_POLKADOT_JS',
  DOTSAMA_SUBWALLET = 'DOTSAMA_SUBWALLET',
  DOTSAMA_TALISMAN = 'DOTSAMA_TALISMAN',
  EVM_MANUAL = 'EVM_MANUAL',
  EVM_METAMASK = 'EVM_METAMASK',
  EVM_SUBWALLET = 'EVM_SUBWALLET',
  EVM_TALISMAN = 'EVM_TALISMAN',
  EVM_WALLET_CONNECT = 'EVM_WALLET_CONNECT'
}

export type WhitelistGiveawayData = {
  __typename?: 'WhitelistGiveawayData';
  distributionMsg?: Maybe<Scalars['String']>;
  reward: Scalars['String'];
  rewardAmount?: Maybe<Scalars['String']>;
};

export type WhitelistGiveawayDataInput = {
  distributionMsg?: InputMaybe<Scalars['String']>;
  reward: Scalars['String'];
  rewardAmount?: InputMaybe<Scalars['String']>;
};

export type WhitelistGiveawayDataUpdateInput = {
  distributionMsg?: InputMaybe<Scalars['String']>;
  reward?: InputMaybe<Scalars['String']>;
  rewardAmount?: InputMaybe<Scalars['String']>;
};

export type WhitelistGiveawayInput = {
  condition?: GiveawayEventCondition;
  data: WhitelistGiveawayDataInput;
  description?: InputMaybe<Scalars['String']>;
  distributionType: DistributionType;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: Scalars['Boolean'];
  frequency?: InputMaybe<Frequency>;
  giveawayType: GiveawayType;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type WhitelistGiveawayUpdateInput = {
  condition?: InputMaybe<GiveawayEventCondition>;
  data: WhitelistGiveawayDataUpdateInput;
  description?: InputMaybe<Scalars['String']>;
  endTime?: InputMaybe<Scalars['DateTime']>;
  eventRelativeWindow?: InputMaybe<Scalars['Boolean']>;
  frequency?: InputMaybe<Frequency>;
  hidden?: InputMaybe<Scalars['Boolean']>;
  icon?: InputMaybe<Scalars['String']>;
  parentId?: InputMaybe<Scalars['ID']>;
  startTime?: InputMaybe<Scalars['DateTime']>;
  title?: InputMaybe<Scalars['String']>;
  whitelistWallets?: InputMaybe<Array<Scalars['String']>>;
  winnerCount?: InputMaybe<Scalars['Int']>;
  winningMessage?: InputMaybe<Scalars['String']>;
};

export type Widget = {
  __typename?: 'Widget';
  config?: Maybe<WidgetConfig>;
  createdAt: Scalars['DateTime'];
  domain?: Maybe<Scalars['String']>;
  event: ProjectEvent;
  eventId: Scalars['String'];
  id: Scalars['ID'];
  updatedAt: Scalars['DateTime'];
};

export type WidgetColorConfig = {
  __typename?: 'WidgetColorConfig';
  accent?: Maybe<HSLColor>;
  accentForeground?: Maybe<HSLColor>;
  background?: Maybe<HSLColor>;
  border?: Maybe<HSLColor>;
  foreground?: Maybe<HSLColor>;
  link?: Maybe<HSLColor>;
  muted?: Maybe<HSLColor>;
  mutedForeground?: Maybe<HSLColor>;
  popover?: Maybe<HSLColor>;
  popoverForeground?: Maybe<HSLColor>;
  primary?: Maybe<HSLColor>;
  primaryForeground?: Maybe<HSLColor>;
  secondary?: Maybe<HSLColor>;
  secondaryForeground?: Maybe<HSLColor>;
};

export type WidgetColorConfigInput = {
  accent?: InputMaybe<HSLColorInput>;
  accentForeground?: InputMaybe<HSLColorInput>;
  background?: InputMaybe<HSLColorInput>;
  border?: InputMaybe<HSLColorInput>;
  foreground?: InputMaybe<HSLColorInput>;
  link?: InputMaybe<HSLColorInput>;
  muted?: InputMaybe<HSLColorInput>;
  mutedForeground?: InputMaybe<HSLColorInput>;
  popover?: InputMaybe<HSLColorInput>;
  popoverForeground?: InputMaybe<HSLColorInput>;
  primary?: InputMaybe<HSLColorInput>;
  primaryForeground?: InputMaybe<HSLColorInput>;
  secondary?: InputMaybe<HSLColorInput>;
  secondaryForeground?: InputMaybe<HSLColorInput>;
};

export type WidgetConfig = {
  __typename?: 'WidgetConfig';
  color?: Maybe<WidgetColorConfig>;
  forcedTheme?: Maybe<ThemeMode>;
  hideFuel?: Maybe<Scalars['Boolean']>;
  hideXp?: Maybe<Scalars['Boolean']>;
};

export type WidgetConfigInput = {
  color?: InputMaybe<WidgetColorConfigInput>;
  forcedTheme?: InputMaybe<ThemeMode>;
  hideFuel?: InputMaybe<Scalars['Boolean']>;
  hideXp?: InputMaybe<Scalars['Boolean']>;
};

export type WidgetDataInput = {
  config?: InputMaybe<WidgetConfigInput>;
  domain?: InputMaybe<Scalars['String']>;
  eventId: Scalars['String'];
};

export type WidgetDataUpdateInput = {
  config?: InputMaybe<WidgetConfigInput>;
  domain?: InputMaybe<Scalars['String']>;
  eventId?: InputMaybe<Scalars['String']>;
};

export type WidgetOption = {
  __typename?: 'WidgetOption';
  icon?: Maybe<Scalars['String']>;
  id: Scalars['String'];
  name: Scalars['String'];
  tag?: Maybe<Scalars['String']>;
};

export type WidgetTaskData = {
  __typename?: 'WidgetTaskData';
  hidden?: Maybe<Scalars['Boolean']>;
  id: Scalars['String'];
  options: Array<Maybe<WidgetOption>>;
  order: Scalars['Int'];
  required: Scalars['Boolean'];
  title: Scalars['String'];
  values?: Maybe<Array<FormDataValue>>;
  widget: FormWidgetType;
};

export type Winner = {
  __typename?: 'Winner';
  amount?: Maybe<Scalars['String']>;
  burnPoints?: Maybe<Scalars['Int']>;
  createdAt: Scalars['DateTime'];
  displayName: Scalars['String'];
  formAnswers?: Maybe<Array<Maybe<FormAnswer>>>;
  giveawayId: Scalars['String'];
  hashedIp?: Maybe<Scalars['String']>;
  id: Scalars['ID'];
  info: GiveawayInfoUnion;
  primaryAuth?: Maybe<Auth>;
  ruleId: Scalars['String'];
  status: RewardStatus;
  txHash?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  wallet?: Maybe<Scalars['String']>;
};

export type WinnerInput = {
  amount?: InputMaybe<Scalars['String']>;
  userId: Scalars['String'];
};

export type WinnerWhereInput = {
  date?: InputMaybe<DateInput>;
  eventId: Scalars['String'];
  giveawayId: Scalars['String'];
  status?: InputMaybe<Array<InputMaybe<RewardStatus>>>;
};

export type WinnersData = {
  __typename?: 'WinnersData';
  data: Array<Winner>;
  total: Scalars['Float'];
};

export type WithdrawBlockchainPoolInput = {
  amount: Scalars['String'];
  assetType: AssetType;
  id: Scalars['String'];
  userBlockchainAddress: Scalars['String'];
  withdrawRemaining?: InputMaybe<Scalars['Boolean']>;
};

export type WithdrawBlockchainPoolOutput = {
  __typename?: 'WithdrawBlockchainPoolOutput';
  hash: Scalars['String'];
  updateResult: UpdateResult;
};

export type ZapierIntegrationData = {
  __typename?: 'ZapierIntegrationData';
  createdAt: Scalars['DateTime'];
  publicKey?: Maybe<Scalars['String']>;
  updatedAt: Scalars['DateTime'];
  webhooks?: Maybe<Array<ZapierWebhookData>>;
};

export type ZapierWebhookData = {
  __typename?: 'ZapierWebhookData';
  name: Scalars['String'];
  webhookId?: Maybe<Scalars['String']>;
  webhookUrl: Scalars['String'];
};

export type ZapierWebhookDataInput = {
  name: Scalars['String'];
  webhookId?: InputMaybe<Scalars['String']>;
  webhookUrl: Scalars['String'];
};
