import { UseGuards } from '@nestjs/common';
import {
  Args,
  ID,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { JwtGqlAuthGuard } from '@root/auth/jwt/jwt-gql-auth.guard';
import { SuperAdminGuard } from '@root/authorization/super-admin.guard';
import { DeleteResult } from '@root/core/dto/delete-result.dto';
import { UpdateResult } from '@root/core/dto/update-result.dto';
import { BlockchainAsset } from './blockchain-asset.entity';
import {
  BlockchainAssetWhereInput,
  BlockchainInput,
  BlockchainUpdateInput,
  BlockchainWhereInput,
  BlockchainAssetInput,
  BlockchainAssetUpdateInput,
  BlockchainAssetsResponse,
} from './blockchain.dto';
import { Blockchain } from './blockchain.entity';
import { BlockchainService } from './blockchain.service';
import { DotsamaNft } from './dotsama-nft.entity';
import { PaginationInput } from '@root/core/dto/pagination.dto';

@Resolver((of) => Blockchain)
export class BlockchainResolver {
  constructor(private readonly blockchainService: BlockchainService) {}

  @Query((returns) => Blockchain)
  blockchain(@Args('id', { type: () => ID }) id: string) {
    // Always use isPrivate=false to ensure private RPC URLs are filtered out for public use
    return this.blockchainService.findById(id, false);
  }

  @Query((returns) => [Blockchain], { nullable: true })
  blockchains(@Args('where') where: BlockchainWhereInput) {
    // Always use isPrivate=false to ensure private RPC URLs are filtered out for public use
    return this.blockchainService.getBlockchains(where, false);
  }

  @Query((returns) => [BlockchainAsset], { nullable: true })
  blockchainAssets(
    @Args('blockchainId', { type: () => ID }) blockchainId: string,
    @Args('where', { nullable: true }) where?: BlockchainAssetWhereInput,
  ) {
    return this.blockchainService.findBlockchainAssets(blockchainId, where);
  }

  @Query((returns) => [DotsamaNft], { nullable: true })
  getDotsamaPoolAssetNftItems(
    @Args('blockchainPoolId', { type: () => ID }) blockchainPoolId: string,
    @Args('blockchainAssetId', { type: () => ID }) blockchainAssetId: string,
  ) {
    return this.blockchainService.getDotsamaNftItems(
      blockchainPoolId,
      blockchainAssetId,
    );
  }

  @ResolveField('assets', (returns) => [BlockchainAsset], { nullable: true })
  getBlockchainAssets(@Parent() blockchain: Blockchain) {
    if (!blockchain.id) return;
    return this.blockchainService.findBlockchainAssets(blockchain.id);
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Mutation((returns) => Blockchain, {
    description: 'Internal Use Only - Add a new blockchain on the platform',
  })
  addBlockchain(@Args('data') data: BlockchainInput) {
    return this.blockchainService.create(data);
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Mutation((returns) => UpdateResult, {
    description:
      'Internal Use Only - Update an existing blockchain on the platform',
  })
  updateBlockchain(
    @Args('id', { type: () => ID }) id: string,
    @Args('data') data: BlockchainUpdateInput,
  ) {
    return this.blockchainService.update(id, data);
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Mutation((returns) => DeleteResult, {
    description: 'Internal Use Only - Delete a blockchain from the platform',
  })
  deleteBlockchain(@Args('id', { type: () => ID }) id: string) {
    return this.blockchainService.delete({ id });
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Query((returns) => BlockchainAssetsResponse, {
    description:
      'Internal Use Only - Get all whitelisted assets on the platform',
    nullable: true,
  })
  airpoolAssetWhitelist(@Args('pagination') pagination: PaginationInput) {
    return this.blockchainService.getAirpoolAssetWhitelist(pagination);
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Mutation((returns) => BlockchainAsset, {
    description:
      'Internal Use Only - Add a new blockchain asset on the platform',
  })
  addBlockchainAsset(@Args('data') data: BlockchainAssetInput) {
    return this.blockchainService.createAsset(data);
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Mutation((returns) => UpdateResult, {
    description:
      'Internal Use Only - Update an existing blockchain asset on the platform',
  })
  updateBlockchainAsset(
    @Args('id', { type: () => ID }) id: string,
    @Args('data') data: BlockchainAssetUpdateInput,
  ) {
    return this.blockchainService.updateAsset(id, data);
  }

  @UseGuards(JwtGqlAuthGuard, SuperAdminGuard)
  @Mutation((returns) => DeleteResult, {
    description:
      'Internal Use Only - Delete a blockchain asset from the platform',
  })
  deleteBlockchainAsset(@Args('id', { type: () => ID }) id: string) {
    return this.blockchainService.deleteAsset({ id });
  }
}
