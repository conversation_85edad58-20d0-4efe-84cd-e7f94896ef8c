import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Contract } from '@root/contracts/contract.entity';
import { BaseCrudService } from '@root/core/base-crud/base-crud.service';
import { <PERSON>tityManager, ILike, Repository } from 'typeorm';
import { AssetType, BlockchainAsset } from './blockchain-asset.entity';
import {
  BlockchainAssetWhereInput,
  BlockchainWhereInput,
  BlockchainAssetInput,
  BlockchainAssetUpdateInput,
} from './blockchain.dto';
import { Blockchain } from './blockchain.entity';
import { DotsamaNft } from './dotsama-nft.entity';
import { PaginationInput } from '@root/core/dto/pagination.dto';
import {
  BlockchainIntegrationStatus,
  PRIVATE_RPC_URLS,
} from './blockchain.constants';

@Injectable()
export class BlockchainService extends BaseCrudService<Blockchain> {
  constructor(
    @InjectRepository(Blockchain)
    repository: Repository<Blockchain>,
    @InjectRepository(BlockchainAsset)
    private readonly blockchainAssetRepo: Repository<BlockchainAsset>,
    @InjectRepository(DotsamaNft)
    private readonly dotsamaNftRepo: Repository<DotsamaNft>,
  ) {
    super(repository);
  }

  /**
   * Filter RPC URLs based on whether we want private URLs or not
   * @param rpcUrls - The original array of RPC URLs
   * @param includePrivate - When true (backend use), prioritize private URLs if available.
   *                         When false (frontend use), filter out private URLs.
   * @returns Filtered array of RPC URLs
   */
  private filterRpcUrls(rpcUrls: string[], includePrivate: boolean): string[] {
    if (includePrivate) {
      // For backend use (includePrivate=true):
      // If there are any private URLs in the list, return ONLY those private URLs
      // This ensures backend calls use private URLs when available
      const privateUrls = rpcUrls.filter((url) =>
        PRIVATE_RPC_URLS.includes(url),
      );
      if (privateUrls.length > 0) {
        return privateUrls;
      }
      // If no private URLs found, return all URLs
      return rpcUrls;
    } else {
      // For frontend use (includePrivate=false):
      // Filter out all private URLs to prevent frontend access to them
      return rpcUrls.filter((url) => !PRIVATE_RPC_URLS.includes(url));
    }
  }

  /**
   * Process a blockchain to filter RPC URLs based on private flag
   * @param blockchain - The blockchain to process
   * @param includePrivate - When true (backend), prioritize private URLs.
   *                         When false (frontend), remove private URLs.
   * @returns Processed blockchain with filtered RPC URLs
   */
  private processBlockchain(
    blockchain: Blockchain,
    includePrivate: boolean,
  ): Blockchain {
    if (blockchain && blockchain.rpcUrls) {
      blockchain.rpcUrls = this.filterRpcUrls(
        blockchain.rpcUrls,
        includePrivate,
      );
    }
    return blockchain;
  }

  /**
   * Find blockchain by ID and filter RPC URLs based on the private flag
   * @param id - The blockchain ID
   * @param includePrivate - When true (backend use), prioritize private URLs if available.
   *                         When false (frontend use), filter out private URLs.
   * @returns Blockchain with filtered RPC URLs
   */
  async findById(id: string, includePrivate = false) {
    const blockchain = await this.repository.findOne({
      where: { id },
    });

    return this.processBlockchain(blockchain, includePrivate);
  }

  findAssetByCreator(walletAddress: string, blockchainId: string) {
    return this.blockchainAssetRepo.findOne({
      where: { creatorBlockchainAddress: ILike(walletAddress), blockchainId },
    });
  }

  /**
   * Get blockchains with filtered RPC URLs based on the private flag
   * @param where - Query conditions
   * @param includePrivate - When true (backend use), prioritize private URLs if available.
   *                         When false (frontend use), filter out private URLs.
   * @returns Array of blockchains with filtered RPC URLs
   */
  async getBlockchains(where?: BlockchainWhereInput, includePrivate = false) {
    const q = this.repository.createQueryBuilder('blockchain');
    if (where?.contractType)
      q.distinct()
        .innerJoin(Contract, 'contract', 'contract.blockchain=blockchain.id')
        .andWhere('contract.contractType = :contractType', {
          contractType: where.contractType,
        });
    if (where?.blockchainType)
      q.andWhere('blockchain.type = :blockchainType', {
        blockchainType: where.blockchainType,
      });

    const blockchains = await q.orderBy('blockchain.order', 'ASC').getMany();

    // Process each blockchain to filter RPC URLs based on the private flag
    return blockchains.map((blockchain) =>
      this.processBlockchain(blockchain, includePrivate),
    );
  }

  async getAirpoolAssetWhitelist(pagination: PaginationInput) {
    const q = this.blockchainAssetRepo
      .createQueryBuilder('asset')
      .leftJoinAndSelect('asset.blockchain', 'blockchain')
      .where('asset.status = :status', {
        status: BlockchainIntegrationStatus.VERIFIED,
      })
      .orderBy('asset.name', 'ASC');

    const [data, total] = await q

      .take(pagination.take)
      .skip(pagination.skip)
      .getManyAndCount();
    return { data, total };
  }

  findBlockchainAssets(
    blockchainId: string,
    where?: BlockchainAssetWhereInput,
  ) {
    const q = this.blockchainAssetRepo
      .createQueryBuilder()
      .where('"blockchainId" = :blockchainId', {
        blockchainId: blockchainId,
      });

    if (where?.assetType) {
      const assetTypes =
        where?.assetType === AssetType.ERC20
          ? [AssetType.ERC20, AssetType.NATIVE]
          : [where.assetType];

      q.andWhere('"assetType" IN (:...assetTypes)', {
        assetTypes,
      });
    }

    if (where?.statusIn && where?.statusIn.length > 0) {
      q.andWhere('status IN (:...statusIn)', {
        statusIn: where.statusIn,
      });
    }

    if (where?.tokenId) {
      q.andWhere('"tokenId" = :tokenId', {
        tokenId: where.tokenId,
      });
    }

    return q.getMany();
  }

  getDotsamaNftItems(blockchainPoolId: string, blockchainAssetId: string) {
    return this.dotsamaNftRepo.find({
      where: { blockchainAssetId, blockchainPoolId },
    });
  }

  insertDotsamaNftItem(
    transactionManager: EntityManager,
    items: Array<number>,
    assetId: string,
    poolId: string,
  ) {
    return transactionManager
      .createQueryBuilder()
      .insert()
      .into(DotsamaNft)
      .values(
        items.map((itemId) => ({
          blockchainAssetId: assetId,
          blockchainPoolId: poolId,
          itemId: itemId,
        })),
      )
      .orIgnore()
      .returning('*');
  }

  findAssetsById(id: string) {
    return this.blockchainAssetRepo
      .createQueryBuilder()
      .where('"id" = :id', { id })
      .getOne();
  }

  findAssetsByBlockchainId(blockchainId: string) {
    return this.blockchainAssetRepo.find({
      where: {
        blockchain: { id: blockchainId },
      },
    });
  }

  findAssetById(blockchainId: string, assetAddress: string) {
    return this.blockchainAssetRepo.findOne({
      where: {
        blockchain: { id: blockchainId },
        address: assetAddress,
      },
    });
  }

  findAssetByGiveawayId(giveawayId: string) {
    return this.blockchainAssetRepo
      .createQueryBuilder('asset')
      .innerJoinAndSelect('asset.blockchain', 'blockchain')
      .innerJoin('asset.blockchainPools', 'blockchainPool')
      .innerJoin('blockchainPool.giveawayPool', 'giveawayPool')
      .where('giveawayPool.giveawayId = :giveawayId', { giveawayId })
      .getOne();
  }

  createAsset(data: BlockchainAssetInput) {
    return this.blockchainAssetRepo.save(this.blockchainAssetRepo.create(data));
  }

  updateAsset(id: string, data: BlockchainAssetUpdateInput) {
    return this.blockchainAssetRepo.update({ id }, data);
  }

  deleteAsset(where: { id: string }) {
    return this.blockchainAssetRepo.delete(where);
  }
}
