import React, { useEffect } from 'react';
import { Form, Input, Select, Button, Typography, Modal } from 'antd';
import { FlashcardSection } from '@airlyft/types';
import RichTextEditor from '@Components/RichTextEditor/RichTextEditor';
import ImageDragger from '@Pages/project/components/ImageDragger';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Row } from '@Components/AutoGrid';
import { UploadType } from '@Root/types';

interface FlashcardSectionFormProps {
  visible: boolean;
  onClose: () => void;
  onSave: (section: FlashcardSection) => void;
  selectedItem?: FlashcardSection;
  nextOrder: number;
}

const FlashcardSectionForm: React.FC<FlashcardSectionFormProps> = ({
  visible,
  onClose,
  onSave,
  selectedItem,
  nextOrder,
}) => {
  const [form] = Form.useForm();
  const isEditing = !!selectedItem;

  useEffect(() => {
    if (visible) {
      if (selectedItem) {
        form.setFieldsValue(selectedItem);
      } else {
        form.resetFields();
        form.setFieldsValue({
          id: `${Date.now()}`,
          order: nextOrder,
        });
      }
    }
  }, [visible, selectedItem, form, nextOrder]);

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onSave(values);
      onClose();
    });
  };

  if (!visible) {
    return null;
  }

  return (
    <Form layout="vertical" form={form}>
      <Form.Item name="id" hidden>
        <Input />
      </Form.Item>

      <Form.Item name="order" hidden>
        <Input type="number" />
      </Form.Item>

      <Form.Item
        name="content"
        label="Content"
        rules={[{ required: true, message: "can't be blank" }]}
      >
        <RichTextEditor
          defaultValue={
            form.getFieldValue('content') || selectedItem?.content || ''
          }
          onChange={(value) => {
            form.setFieldsValue({ content: value });
          }}
        />
      </Form.Item>

      <Row>
        <Button
          type="primary"
          size="large"
          ghost
          block
          shape="round"
          onClick={onClose}
        >
          <ArrowLeftOutlined />
          Back
        </Button>
        <Button
          block
          shape="round"
          type="primary"
          size="large"
          onClick={handleSubmit}
        >
          {isEditing ? 'Save Changes' : 'Add Section'}
        </Button>
      </Row>
    </Form>
  );
};

export default FlashcardSectionForm;
