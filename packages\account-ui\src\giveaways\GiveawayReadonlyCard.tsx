import Box from '@Components/Box';
import { StyledImage, StyledVideo } from '@Components/Image';
import { useAppDispatch, useAppSelector } from '@Root/store.hooks';
import { shortenText } from '@Root/utils/stringUtils';
import {
  DistributionType,
  EventState,
  Giveaway,
  ProjectEvent,
} from '@airlyft/types';
import { EditOutlined } from '@ant-design/icons';
import { Button, Space, Switch, Tag, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';
import { DeleteGiveaway } from './components/DeleteGiveaway';
import {
  giveawayTypeToCategory,
  selectGiveawayCategoryByType,
} from './giveaway.helper';
import { GiveawayScope, openGiveawayModal } from './giveaway.slice';
import useWinnerActiveGiveaway from '@Pages/winners/hooks/useWinnerActiveGiveaway';
import { t } from 'i18next';
import { justifyContent } from 'styled-system';
import { HideGiveaway } from './components/HideGiveaway';

const { Title } = Typography;

export default function GiveawayReadonlyCard({
  projectId,
  projectEventId,
  giveaway,
  projectEvent,
}: {
  projectId: string;
  projectEventId: string;
  giveaway: Giveaway;
  projectEvent: ProjectEvent;
}) {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const category = giveawayTypeToCategory(giveaway.giveawayType);
  const giveawayCategory = useAppSelector(
    selectGiveawayCategoryByType(category),
  );

  const { activate: activateGiveaway } =
    useWinnerActiveGiveaway(projectEventId);

  const scope =
    giveaway.distributionType === DistributionType.SHOP
      ? GiveawayScope.SHOP
      : GiveawayScope.EVENT;

  return (
    <Box padding={3} bordered={true} radius="lg">
      <Space align="start" size="middle">
        {giveaway.icon &&
          (giveaway.icon.endsWith('.mp4') ||
          giveaway.icon.endsWith('.mov') ||
          giveaway.icon.endsWith('.webm') ||
          giveaway.icon.endsWith('.avi') ? (
            <StyledVideo
              width={150}
              height={150}
              className="rounded-lg"
              autoPlay
              muted
              loop
            >
              <source src={giveaway.icon} type="video/mp4" />
              Your browser does not support the video tag.
            </StyledVideo>
          ) : (
            <StyledImage
              preview={false}
              height={150}
              width={150}
              src={giveaway.icon || '/images/whitelist.webp'}
            />
          ))}

        <div
          style={{
            height: 130,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Title level={5}>{shortenText(giveaway.title!, 40)}</Title>
            <HideGiveaway
              giveaway={giveaway}
              projectId={projectId}
              projectEventId={projectEventId}
            />
          </div>
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
            }}
          >
            <Tag>{shortenText(giveawayCategory!.title, 30)}</Tag>
            {scope !== GiveawayScope.SHOP && (
              <Tag>
                {t(`distribution-type.${giveaway.distributionType}.short`)}
              </Tag>
            )}
          </div>
          <div style={{ flex: 1 }}></div>

          <div
            style={{
              display: 'flex',
              gap: 10,
              alignItems: 'center',
            }}
          >
            <DeleteGiveaway
              giveawayId={giveaway.id}
              projectId={projectId}
              projectEventId={projectEventId}
            />
            <Button
              disabled={scope !== GiveawayScope.SHOP}
              shape="circle"
              size="small"
              icon={<EditOutlined />}
              onClick={(event) => {
                event.stopPropagation();
                dispatch(
                  openGiveawayModal({
                    projectEventId: projectEvent?.id || '',
                    category,
                    scope: GiveawayScope.SHOP,
                    giveawayType: giveaway.giveawayType,
                    data: giveaway,
                  }),
                );
              }}
            />
            <Button
              shape="round"
              type="primary"
              size="small"
              disabled={projectEvent.state === EventState.DRAFT}
              onClick={(event) => {
                if (scope === GiveawayScope.SHOP) {
                  navigate(`/shop/${projectEventId}/${giveaway.id}`);
                } else {
                  activateGiveaway(giveaway.id);
                  navigate(`/campaigns/${projectEventId}/winners`);
                }
              }}
            >
              {scope === GiveawayScope.SHOP ? 'View Claims' : 'View Winners'}
            </Button>
          </div>
        </div>
      </Space>
    </Box>
  );
}
