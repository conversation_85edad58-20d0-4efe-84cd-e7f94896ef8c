import { BadRequestException, Injectable } from '@nestjs/common';
import '@polkadot/api-augment';
import { BN } from '@polkadot/util';
import { DotsamaClientService } from './dotsama-client.service';
import { BlockchainAsset } from '@root/blockchain/blockchain-asset.entity';

@Injectable()
export class DotsamaAssetsService {
  constructor(private readonly dotsamaClientService: DotsamaClientService) {}

  create({
    blockchainId,
    userId,
    name,
    ticker,
    decimals = 18,
  }: {
    blockchainId: string;
    userId: string;
    name: string;
    ticker: string;
    decimals?: number;
    assetId: number;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        const nextAssetId = (await api.query.assets.nextAssetId()).toString();

        const result = await api.tx.utility
          .batch([
            api.tx.assets.create(nextAssetId, user.address, 1),
            api.tx.assets.setMetadata(nextAssetId, name, ticker, decimals),
          ])
          .signAndSend(user, { nonce });

        return {
          creatorAddress: user.address,
          txHash: result,
          assetId: nextAssetId,
        };
      },
      userId,
    );
  }

  transferAsset(
    blockchainId: string,
    userId: string,
    toAddress: string,
    amount: string,
    tokenId: string,
  ) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        const data = await api.query.assets.account(tokenId, user.address);
        const balance = data?.value.balance;

        if (!balance || balance.lt(new BN(amount))) {
          throw new BadRequestException(
            'Insufficient balance. Please contact the community host for assistance.',
          );
        }

        const transfer = api.tx.assets.transfer(tokenId, toAddress, amount);

        return transfer.signAndSend(user, { nonce });
      },
      userId,
    );
  }

  mint({
    blockchainId,
    userId,
    toAddress,
    amount,
    tokenId,
  }: {
    blockchainId: string;
    userId: string;
    toAddress: string;
    amount: bigint;
    tokenId: string;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        const { data } = await api.query.system.account(user.address);
        const balance = data?.free ?? new BN('0');
        const ed = api.consts.balances.existentialDeposit;
        const maxAvailable = balance.sub(ed);
        const tx = api.tx.assets.mint(parseInt(tokenId), toAddress, amount);
        const info = await tx.paymentInfo(user.address);
        const txGas = new BN(info.partialFee);
        if (!balance || maxAvailable.lt(txGas)) {
          throw new BadRequestException(
            'Insufficient balance for gas. Please contact the community host for assistance.',
          );
        }
        return tx.signAndSend(user, { nonce });
      },
      userId,
    );
  }

  balance({
    blockchainId,
    userId,
    tokenId,
  }: {
    blockchainId: string;
    userId: string;
    tokenId: string;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const data = await api.query.assets.account(tokenId, user.address);
        const balance = data?.value.balance ?? 0;

        return balance.toString();
      },
      userId,
    );
  }
}
