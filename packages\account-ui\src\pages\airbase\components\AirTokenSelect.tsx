import { AppContext } from '@Root/states/AppContext';
import {
  AssetType,
  BlockchainAsset,
  BlockchainIntegrationStatus,
  TransactionStatus,
} from '@airlyft/types';
import { shortenAddress } from '@airlyft/web3-evm';
import { ReloadOutlined, RocketOutlined } from '@ant-design/icons';
import { Avatar, Button, Divider, Select, Space } from 'antd';
import { useContext } from 'react';
import { useDispatch } from 'react-redux';
import styled, { ThemeContext } from 'styled-components';
import { openTokenActionModal } from '../airbase.slice';
import { useGetAirTokens } from '../hooks/useGetAirTokens';
import { ethers } from 'ethers';
import { StyledVideo } from '@Components/Image';

const StyledSelect = styled.div`
  .airtoken-select {
    height: 58px !important;
    .ant-select-selector {
      height: 58px !important;
    }

    .ant-select-selection-item,
    .ant-select-selection-placeholder {
      line-height: 22px !important;
      padding-top: 4px !important;
    }
  }
`;

export default function AirTokenSelect({
  assetType,
  onChange,
  onAirToken,
  disabled,
  value,
}: {
  assetType: AssetType | undefined;
  onChange?: (value: string) => void;
  onAirToken?: (value: BlockchainAsset) => void;
  disabled?: boolean;
  value?: string;
}) {
  const {
    state: { projectId },
  } = useContext(AppContext);
  const { data, loading } = useGetAirTokens(
    projectId,
    { assetType },
    !assetType || !projectId,
  );
  const tokens = data?.airTokens?.filter(
    (t) =>
      t.status === BlockchainIntegrationStatus.AIR_TOKEN &&
      t.txStatus === TransactionStatus.SUCCESS,
  );
  const theme = useContext(ThemeContext);
  const dispatch = useDispatch();
  return (
    <StyledSelect>
      <Select
        className="airtoken-select"
        onChange={(id: string) => {
          onChange?.(id);
          const selectedAsset = tokens?.find((asset) => asset.id === id);
          selectedAsset && onAirToken?.(selectedAsset);
        }}
        value={value}
        placeholder={
          <Space size={10}>
            <div style={{ position: 'relative' }}>
              <Avatar
                size={34}
                src={<RocketOutlined />}
                style={{ backgroundColor: theme.colors.primary }}
              />
            </div>
            <Space direction="vertical" size={[0, 0]}>
              <span style={{ fontWeight: 500 }}>Please Select an AirToken</span>
              <span>Click to select</span>
            </Space>
          </Space>
        }
        style={{ width: '100%' }}
        disabled={disabled}
        loading={loading}
        dropdownRender={(menu) => (
          <>
            <div
              style={{
                padding: '0 8px',
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              <Button type="link" icon={<ReloadOutlined />}></Button>
              <Button
                type="link"
                size="small"
                onClick={() =>
                  dispatch(
                    openTokenActionModal({
                      actionType: 'Create',
                      assetType,
                      hideBack: true,
                    }),
                  )
                }
              >
                Create new token
              </Button>
            </div>
            <Divider style={{ margin: '0' }} />
            {menu}
          </>
        )}
      >
        {tokens?.map(
          (
            { id, icon, ticker, address, name, assetType, blockchain, tokenId },
            key,
          ) => (
            <Select.Option value={id} key={key} style={{ height: 50 }}>
              <Space size={10}>
                {icon &&
                  (icon.endsWith('.mp4') ||
                  icon.endsWith('.mov') ||
                  icon.endsWith('.webm') ||
                  icon.endsWith('.avi') ? (
                    <video width={34} height={34} autoPlay muted loop>
                      <source src={icon} type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  ) : (
                    <Avatar size={34} src={icon} shape="square" />
                  ))}

                <Space direction="vertical" size={[0, 0]}>
                  <span>
                    {name} (<b>{ticker}</b>)
                  </span>

                  <Space>
                    <span>
                      {address === ethers.constants.AddressZero
                        ? tokenId
                        : shortenAddress(address, 3)}
                    </span>
                    <span>|</span>
                    <span>{assetType}</span>
                    <span>|</span>
                    {blockchain && (
                      <Space size="small">
                        <Avatar src={blockchain.icon} size={18} />
                        <span>{blockchain.name}</span>
                      </Space>
                    )}
                  </Space>
                </Space>
              </Space>
            </Select.Option>
          ),
        )}
      </Select>
    </StyledSelect>
  );
}
