import {
  BadGatewayException,
  BadRequestException,
  Injectable,
} from '@nestjs/common';
import '@polkadot/api-augment';
import { DotsamaClientService } from './dotsama-client.service';
import { DotsamaTransactionService } from './dotsama-transaction.service';
import { BN } from '@polkadot/util';

@Injectable()
export class DotsamaNFTService {
  constructor(
    private readonly dotsamaTransactionService: DotsamaTransactionService,
    private readonly dotsamaClientService: DotsamaClientService,
  ) {}

  getUri({
    tokenId,
    itemId,
    baseURI,
  }: {
    tokenId: string | number;
    itemId?: string | number;
    baseURI?: string;
  }) {
    if (baseURI) return baseURI;
    const uri = process.env.BACKEND_URL + '/api/meta/assethub/' + tokenId;
    if (itemId) return `${uri}/${itemId}`;

    return uri;
  }

  mint({
    blockchainId,
    userId,
    toAddress,
    amount,
    tokenId,
  }: {
    blockchainId: string;
    userId: string;
    toAddress: string;
    amount: bigint;
    tokenId: string;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        const parsedTokenId = parseInt(tokenId);

        const collectionDetails = await api.query.nfts.collection(
          parsedTokenId,
        );

        const nextItemId = Number(collectionDetails?.value?.items ?? 0);

        const txs = [];
        for (let i = 0; i < amount; i++) {
          const itemId = nextItemId + i;
          txs.push(
            api.tx.nfts.mint(parsedTokenId, itemId, toAddress, null),
            api.tx.nfts.setMetadata(
              parsedTokenId,
              itemId,
              this.getUri({ tokenId, itemId }),
            ),
          );
        }

        const { data } = await api.query.system.account(user.address);
        const balance = data?.free ?? new BN('0');
        const ed = api.consts.balances.existentialDeposit;
        const maxAvailable = balance.sub(ed);
        const tx = api.tx.utility.batch(txs);
        const info = await tx.paymentInfo(user.address);
        const txGas = new BN(info.partialFee);

        if (!balance || maxAvailable.lt(txGas)) {
          throw new BadRequestException(
            'Insufficient balance for gas. Please contact the community host for assistance.',
          );
        }

        return tx.signAndSend(user, { nonce });
      },
      userId,
    );
  }

  transfer({
    blockchainId,
    userId,
    tokenId,
    items,
    toAddress,
  }: {
    blockchainId: string;
    userId: string;
    tokenId: string;
    items: Array<string>;
    toAddress: string;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        if (items.length === 1) {
          const itemId = items[0];
          const itemInfo = await api.query.nfts.item(tokenId, itemId);

          if (!itemInfo.value?.owner.eq(user.address)) {
            throw new BadGatewayException('Invalid token/itemId');
          }

          return api.tx.nfts
            .transfer(tokenId, itemId, toAddress)
            .signAndSend(user);
        }

        const query = items.map((itemId) => [tokenId, itemId]);

        const itemsInfo = await api.query.nfts.item.multi(query);
        const txs = [];

        for (let i = 0; i < itemsInfo.length; i++) {
          const itemInfo = itemsInfo[i].value;

          if (itemInfo.owner.eq(user.address)) {
            txs.push(api.tx.nfts.transfer(tokenId, items[i], toAddress));
          }
        }

        const { data } = await api.query.system.account(user.address);
        const balance = data?.free ?? new BN('0');
        const ed = api.consts.balances.existentialDeposit;
        const maxAvailable = balance.sub(ed);
        const tx = api.tx.utility.batch(txs);
        const info = await tx.paymentInfo(user.address);
        const txGas = new BN(info.partialFee);

        if (!balance || maxAvailable.lt(txGas)) {
          throw new BadRequestException(
            'Insufficient balance for gas. Please contact the community host for assistance.',
          );
        }

        return tx.signAndSend(user, { nonce });
      },
      userId,
    );
  }

  getAccountNFTs({
    blockchainId,
    userId,
    collectionId,
  }: {
    blockchainId: string;
    userId: string;
    collectionId: string;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const nfts = await api.query.nfts.account.entries(
          user.address,
          collectionId,
        );

        return nfts.map(([key, data]) => key.args[2].toNumber());
      },
      userId,
    );
  }

  create({
    blockchainId,
    userId,
    baseURI,
    transferable = true,
  }: {
    blockchainId: string;
    userId: string;
    baseURI?: string;
    transferable?: boolean;
  }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const createResult =
          await this.dotsamaTransactionService.executeTransaction<{
            collection: number;
          }>(
            api.tx.nfts.create(user.address, {
              settings: transferable ? 0 : 1,
            }),
            user,
            api,
            'Created',
          );

        const collectionId = createResult.event.data.collection;

        await api.tx.nfts
          .setCollectionMetadata(
            createResult.event.data.collection,
            this.getUri({ tokenId: collectionId, baseURI }),
          )
          .signAndSend(user);

        return {
          creatorAddress: user.address,
          txHash: createResult.txHash,
          collectionId,
        };
      },
      userId,
    );
  }
}
