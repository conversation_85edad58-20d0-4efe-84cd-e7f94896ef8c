import React, { useState } from 'react';
import {
  FlashcardSection,
  FlashcardTaskInput,
  FlashcardTaskUpdateInput,
  VerifyType,
  FlashcardTaskData,
} from '@airlyft/types';
import FlashcardDraggableList from './components/FlashcardDraggableList';
import FlashcardSectionForm from './components/FlashcardSectionForm';
import { Button, Form, Tooltip } from 'antd';
import TaskForm, {
  formatTaskFormBasicDetails,
  getTaskFormDefaultDetails,
} from '../components/TaskForm';
import { TaskProps } from '../components/TaskRenderer';
import useMutateTask from '../hooks/useMutateTask';
import {
  useCreateFlashcardTask,
  useUpdateFlashcardTask,
} from './flashcard.gql';

const FlashcardViewForm = ({
  data,
  disabled,
  projectId,
  eventId,
  onCompleted,
  onError,
}: TaskProps) => {
  const [form] = Form.useForm();
  const [selectedItem, setSelectedItem] = useState<FlashcardSection>();
  const [isAddItemVisible, setAddItemVisible] = useState(false);
  const flashcardData = data?.info as FlashcardTaskData;
  const [sections, setSections] = useState<Array<FlashcardSection>>(
    flashcardData?.sections || [],
  );

  const { mutateTask, mutating } = useMutateTask<
    FlashcardTaskInput,
    FlashcardTaskUpdateInput
  >(
    useCreateFlashcardTask,
    useUpdateFlashcardTask,
    projectId,
    eventId,
    onCompleted,
    onError,
    data?.id,
  );

  const onFormFinish = (values: any) => {
    if (!eventId) return;

    const baseTaskData = {
      ...formatTaskFormBasicDetails(values),
      verify: VerifyType.AUTO,
      projectId,
      eventId,
      data: {
        sections: sections.map((section) => ({
          id: section.id,
          order: section.order,
          content: section.content,
        })),
      },
    };

    if (data?.id) {
      mutateTask({
        ...baseTaskData,
        id: data.id,
      } as FlashcardTaskUpdateInput);
    } else {
      mutateTask(baseTaskData as FlashcardTaskInput);
    }
  };

  const noSections = () => !(sections && sections.length > 0);

  const handleSectionsChange = (newSections: FlashcardSection[]) => {
    const updatedSections = newSections.map((section, index) => ({
      ...section,
      order: index,
    }));

    setSections(updatedSections);
  };

  const handleAddSection = (section: FlashcardSection) => {
    if (!section) return;
    setSections([...sections, { ...section, order: sections.length }]);
    setAddItemVisible(false);
  };

  const handleUpdateSection = (updatedSection: FlashcardSection) => {
    setSections(
      sections.map((section) => {
        if (section.id === updatedSection.id) return updatedSection;
        return section;
      }),
    );

    setSelectedItem(undefined);
    setAddItemVisible(false);
  };

  const setFormComponentVisible = (visible: boolean) =>
    setAddItemVisible(visible);

  const updateSelectedItem = (item: FlashcardSection | undefined) =>
    setSelectedItem(item);

  if (isAddItemVisible) {
    return (
      <FlashcardSectionForm
        visible={true}
        onClose={() => {
          setSelectedItem(undefined);
          setAddItemVisible(false);
        }}
        onSave={selectedItem ? handleUpdateSection : handleAddSection}
        selectedItem={selectedItem}
        nextOrder={sections.length}
      />
    );
  }

  return (
    <React.Fragment>
      <div>
        Create educational content with multiple sections for your users.
      </div>
      <TaskForm
        eventId={eventId}
        form={form}
        initialValues={{
          ...getTaskFormDefaultDetails(data, {
            title: 'View Flashcard',
          }),
          verify: VerifyType.AUTO,
        }}
        onFormFinish={onFormFinish}
      >
        <FlashcardDraggableList
          sections={sections}
          onSectionsChange={handleSectionsChange}
          setFormComponentVisible={setFormComponentVisible}
          updateSelectedItem={updateSelectedItem}
          disabled={disabled}
        />

        <div style={{ display: 'flex', justifyContent: 'end' }}>
          <Tooltip
            title={
              noSections()
                ? 'Please add at least one section to create a flashcard task'
                : ''
            }
          >
            <Button
              block
              shape="round"
              disabled={disabled || noSections() || mutating}
              loading={mutating}
              size="large"
              htmlType="submit"
              type="primary"
            >
              {data?.id ? 'Update' : 'Add Task'}
            </Button>
          </Tooltip>
        </div>
      </TaskForm>
    </React.Fragment>
  );
};

export default FlashcardViewForm;
