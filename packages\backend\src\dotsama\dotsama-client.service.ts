import { BadRequestException, Injectable } from '@nestjs/common';
import { ApiPromise, WsProvider } from '@polkadot/api';
import '@polkadot/api-augment';

import { Blockchain, BlockchainType } from '@root/blockchain/blockchain.entity';
import { BlockchainService } from '@root/blockchain/blockchain.service';
import { DotsamaAccountService } from './dotsama-account.service';
import { KeyringPair } from '@polkadot/keyring/types';

type RunFunctionWithoutUserId<T> = (params: {
  api: ApiPromise;
  blockchain: Blockchain;
}) => Promise<T>;
type RunFunctionWithUserId<T> = (params: {
  api: ApiPromise;
  blockchain: Blockchain;
  user: KeyringPair;
}) => Promise<T>;

@Injectable()
export class DotsamaClientService {
  constructor(
    private readonly blockchainService: BlockchainService,
    private readonly dotsamaAccountService: DotsamaAccountService,
  ) {}

  async withClient<T>(
    blockchainId: string,
    run: RunFunctionWithoutUserId<T>,
    userId?: never,
  ): Promise<T>;
  async withClient<T>(
    blockchainId: string,
    run: RunFunctionWithUserId<T>,
    userId: string,
  ): Promise<T>;
  async withClient<T>(
    blockchainId: string,
    run: RunFunctionWithoutUserId<T> | RunFunctionWithUserId<T>,
    userId?: string,
  ): Promise<T> {
    const { api, blockchain, disconnect } = await this.getClient(blockchainId);

    try {
      let result: T;

      if (userId) {
        const user = this.dotsamaAccountService.generateKeyPair(
          userId,
          blockchain.chainId,
          blockchain.keyPairType || 'sr25519',
        );

        result = await (run as RunFunctionWithUserId<T>)({
          api,
          blockchain,
          user,
        });
      } else {
        result = await (run as RunFunctionWithoutUserId<T>)({ api, blockchain });
      }
      await disconnect();
      return result;
    } catch (err) {
      await disconnect();
      throw new BadRequestException(err, 'Something went wrong');
    }
  }

  async getClient(blockchainId) {
    const blockchain = await this.blockchainService.findById(blockchainId, true);

    if (
      !blockchain ||
      blockchain.type !== BlockchainType.DOTSAMA ||
      !blockchain.rpcUrls.length
    )
      throw new BadRequestException(
        'The requested blockchain is not supported.',
      );
    const provider = new WsProvider(blockchain.rpcUrls);
    try {
      const api = await ApiPromise.create({
        provider,
        throwOnConnect: true,
        noInitWarn: true,
      });

      await api.isReady;
      return {
        api,
        blockchain,
        disconnect: async () => {
          try {
            await api.disconnect();
            await provider.disconnect();
          } catch (err) {}
        },
      };
    } catch (error) {
      provider.disconnect();
      throw new BadRequestException(
        `Failed to connect to RPC Node: ${blockchain.rpcUrls}`,
      );
    }
  }
}
