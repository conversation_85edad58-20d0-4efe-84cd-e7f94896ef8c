"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useScrollPosition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useScrollPosition */ \"./hooks/useScrollPosition.ts\");\n/* harmony import */ var _Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useUserDetails */ \"./hooks/useUserDetails.ts\");\n/* harmony import */ var _Root_config_config_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/config/config.json */ \"./config/config.json\");\n/* harmony import */ var _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/context/Actions */ \"./context/Actions.ts\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ClientOnly__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClientOnly */ \"./components/ClientOnly.tsx\");\n/* harmony import */ var _FeatureGuard__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./FeatureGuard */ \"./components/FeatureGuard.tsx\");\n/* harmony import */ var _Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n/* harmony import */ var _UserNotification_UserNotification__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./UserNotification/UserNotification */ \"./components/UserNotification/UserNotification.tsx\");\n/* harmony import */ var _UserProfile_UserMenu__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./UserProfile/UserMenu */ \"./components/UserProfile/UserMenu.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var _GlobalLeaderboard_useGlobalLeaderboard_gql__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./GlobalLeaderboard/useGlobalLeaderboard.gql */ \"./components/GlobalLeaderboard/useGlobalLeaderboard.gql.ts\");\n/* harmony import */ var _Root_helpers__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @Root/helpers */ \"./helpers/index.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ROUTES_CONFIG = _Root_config_config_json__WEBPACK_IMPORTED_MODULE_3__.root.routes;\nconst DesktopNav = (param)=>{\n    let { me, loading } = param;\n    var _leaderboardRankingData_userGlobalRanking;\n    _s();\n    const scrollPosition = (0,_Hooks_useScrollPosition__WEBPACK_IMPORTED_MODULE_1__.useScrollPosition)();\n    const { theme, setTheme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme)();\n    const { dispatch, state: { isAuthenticated } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    // Inorder to enable Geo Blocking restrictions, make the following change\n    // const isRestrictedPage = router.pathname === restrictedPage;\n    const isRestrictedPage = false;\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    const subdomain = (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_6__.getSubdomainClient)();\n    const sorter = {\n        sortKey: _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.GlobalLeaderboardSortKey.TOTAL_XP,\n        direction: _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.SortDirection.DESC\n    };\n    const where = {};\n    const { data: leaderboardRankingData } = (0,_GlobalLeaderboard_useGlobalLeaderboard_gql__WEBPACK_IMPORTED_MODULE_19__.useGlobalLeaderboardRanking)(sorter, where, !(0,_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.isFeatureEnabled)(\"HEADER_POINTS\"));\n    var _leaderboardRankingData_userGlobalRanking_totalXp;\n    const totalXp = (_leaderboardRankingData_userGlobalRanking_totalXp = leaderboardRankingData === null || leaderboardRankingData === void 0 ? void 0 : (_leaderboardRankingData_userGlobalRanking = leaderboardRankingData.userGlobalRanking) === null || _leaderboardRankingData_userGlobalRanking === void 0 ? void 0 : _leaderboardRankingData_userGlobalRanking.totalXp) !== null && _leaderboardRankingData_userGlobalRanking_totalXp !== void 0 ? _leaderboardRankingData_userGlobalRanking_totalXp : 0;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"hidden lg:block\", scrollPosition > 0 ? \"border-b bg-background/80  sticky top-0 z-50 block bg-opacity-70 backdrop-blur-lg\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"hidden lg:flex relative z-50 items-center justify-between p-4 lg:px-8 max-w-7xl mx-auto\",\n            \"aria-label\": \"Global\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex lg:flex-1 lg:gap-x-10 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                            href: \"/\",\n                            className: \"ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Airlyft One\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    className: \"h-10 w-10 rounded-full shadow-sm shadow-yellow-400\",\n                                    src: \"https://pbs.twimg.com/profile_images/1494277092590387202/BMoaZmJ5_400x400.jpg\",\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:gap-x-10 text-ch text-sm font-semibold leading-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"EXPLORE\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.explore,\n                                        children: t(\"nav.explore\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"COMMUNITIES\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.communities,\n                                        children: t(\"nav.communities\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"CAMPAIGNS\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.campaigns,\n                                        children: t(\"nav.campaigns\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"GLOBAL_LEADERBOARD\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.leaderboard,\n                                        children: t(\"nav.leaderboard\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"POLKADOT\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        children: !subdomain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                            target: \"_blank\",\n                                            href: \"https://polkadot.airlyft.one\",\n                                            children: t(\"nav.polkadot\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:flex-1 lg:justify-end gap-x-6 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                feature: \"POLKADOT\",\n                                children: subdomain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/ecosystem/\".concat(subdomain, \"-\").concat(currentTheme, \".png\"),\n                                            className: \"h-9\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-r h-6 pl-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, undefined) : isAuthenticated && me ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                        feature: \"CREATE_CAMPAIGN\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                            href: \"https://account.airlyft.one\",\n                                            target: \"_blank\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                                rounded: \"full\",\n                                                size: \"sm\",\n                                                children: t(\"nav.create\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                        feature: \"HEADER_POINTS\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Sparkle, {\n                                                    size: 24,\n                                                    weight: \"duotone\",\n                                                    className: \"text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-ch\",\n                                                    children: [\n                                                        (0,_Root_helpers__WEBPACK_IMPORTED_MODULE_20__.formatNumber)(totalXp),\n                                                        \" \",\n                                                        (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_18__.pluralize)(totalXp, globalT(\"projectXp\"), globalT(\"projectXp_many\"))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                        feature: \"NOTIFICATIONS\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserNotification_UserNotification__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            rendererType: \"popover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserProfile_UserMenu__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        me: me\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : !isRestrictedPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: \"#\",\n                                        className: \"text-sm font-semibold leading-6 text-cs\",\n                                        onClick: ()=>{\n                                            dispatch({\n                                                type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                                                payload: null\n                                            });\n                                        },\n                                        children: t(\"nav.login\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-primary font-medium rounded-full px-4 py-2 text-primary-foreground text-sm\",\n                                        onClick: ()=>{\n                                            dispatch({\n                                                type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                                                payload: null\n                                            });\n                                        },\n                                        children: t(\"nav.signup\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                feature: \"THEME_TOGGLE\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>currentTheme == \"dark\" ? setTheme(\"light\") : setTheme(\"dark\"),\n                                    className: \"flex items-center\",\n                                    children: currentTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Sun, {\n                                        size: 28,\n                                        className: \"text-ch hover:cursor-pointer\",\n                                        weight: \"duotone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Moon, {\n                                        size: 28,\n                                        className: \"text-cl hover:cursor-pointer\",\n                                        weight: \"duotone\",\n                                        onClick: ()=>setTheme(\"dark\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DesktopNav, \"ZA6gqLRh+LCjyeb8P53WuI9wKeo=\", false, function() {\n    return [\n        _Hooks_useScrollPosition__WEBPACK_IMPORTED_MODULE_1__.useScrollPosition,\n        next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme,\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _GlobalLeaderboard_useGlobalLeaderboard_gql__WEBPACK_IMPORTED_MODULE_19__.useGlobalLeaderboardRanking,\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation,\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c = DesktopNav;\nconst MobileNavItem = (param)=>{\n    let { title, icon, active, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"button\",\n        className: \"inline-flex flex-col gap-1 items-center justify-center \".concat(active ? \"text-ch font-bold\" : \"text-cl\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = MobileNavItem;\nconst MobileNav = (param)=>{\n    let { me, loading } = param;\n    _s1();\n    const { dispatch, state: { isAuthenticated } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    // Inorder to enable Geo Blocking restrictions, make the following change\n    // const isRestrictedPage = router.pathname === restrictedPage;\n    const isRestrictedPage = false;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"lg:hidden fixed bottom-0 left-0 z-50 w-full h-16 shadow border border-t shadow-white/10 grid grid-cols-5 items-center place-content-center place-items-center bg-background/80 backdrop-blur-lg\",\n        \"aria-label\": \"Global\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                href: ROUTES_CONFIG.explore,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                    title: t(\"nav.explore\"),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Compass, {\n                        size: 22,\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 17\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserNotification_UserNotification__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                rendererType: \"modal\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                href: \"/\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        className: \"h-12 w-12 rounded-full shadow-sm shadow-yellow-400\",\n                        src: \"https://pbs.twimg.com/profile_images/1494277092590387202/BMoaZmJ5_400x400.jpg\",\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                title: t(\"nav.profile\"),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.UserCircle, {\n                    size: 22,\n                    weight: \"duotone\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 15\n                }, void 0),\n                onClick: ()=>isAuthenticated ? router.push(\"/user/\".concat(me === null || me === void 0 ? void 0 : me.id)) : dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                title: t(\"nav.settings\"),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Gear, {\n                    size: 22,\n                    weight: \"duotone\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 15\n                }, void 0),\n                onClick: ()=>{\n                    if (isRestrictedPage) return;\n                    return isAuthenticated ? router.push({\n                        pathname: router.pathname,\n                        query: {\n                            ...router.query,\n                            ...{\n                                settings: \"all\"\n                            }\n                        }\n                    }, undefined, {\n                        shallow: true\n                    }) : dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(MobileNav, \"hIwJKNGa/HltcXbA4xspnPHm/+U=\", false, function() {\n    return [\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c2 = MobileNav;\nfunction Header() {\n    _s2();\n    const { data, loading } = (0,_Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_2__.useUserDetails)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DesktopNav, {\n                me: data === null || data === void 0 ? void 0 : data.me,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNav, {\n                me: data === null || data === void 0 ? void 0 : data.me,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(Header, \"PLAvDeOs13GsWcIdcOX0j63+zYQ=\", false, function() {\n    return [\n        _Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_2__.useUserDetails\n    ];\n});\n_c3 = Header;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"DesktopNav\");\n$RefreshReg$(_c1, \"MobileNavItem\");\n$RefreshReg$(_c2, \"MobileNav\");\n$RefreshReg$(_c3, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n"));

/***/ })

});