import { UseGuards } from '@nestjs/common';
import { Args, ID, Mutation, Resolver } from '@nestjs/graphql';
import { AppTaskName } from '@root/apps/app.constant';
import { BaseTaskResolver } from '@root/apps/base-task.resolver';
import { TaskMutation } from '@root/apps/task-mutation.decorator';
import { JwtGqlAuthGuard } from '@root/auth/jwt/jwt-gql-auth.guard';
import { Resources, RoleResource } from '@root/authorization/authorization.dto';
import { RolesGuard } from '@root/authorization/roles.guard';
import { GqlAuthUser } from '@root/core/decorators/gql-auth-user.decorators';
import { GqlUserHashedIp } from '@root/core/decorators/gql-user-hashed-ip.decorators';
import {
  FlashcardTaskInput,
  FlashcardTaskUpdateInput,
} from './flashcard-task.dto';
import { ParticipationService } from '@root/task-participation/participation.service';
import { TaskService } from '@root/task/task.service';
import { AppType, TaskType } from '@root/task/task.constants';

@RoleResource(Resources.EVENT)
@UseGuards(JwtGqlAuthGuard)
@Resolver()
export class FlashcardTaskResolver extends BaseTaskResolver {
  static taskName: AppTaskName = 'Flashcard';

  constructor(
    private participationService: ParticipationService,
    private taskService: TaskService,
  ) {
    super();
  }

  @Mutation(() => Boolean)
  async completeFlashcardViewTask(
    @Args('eventId', { type: () => ID }) eventId: string,
    @Args('taskId', { type: () => ID }) taskId: string,
    @GqlAuthUser() user,
    @GqlUserHashedIp() hashedIp: string,
  ) {
    return this.participationService.participate(
      eventId,
      taskId,
      user.userId,
      {
        getData: () => null,
        validators: [],
      },
      {
        authId: user,
        hashedIp,
      },
    );
  }

  @UseGuards(RolesGuard)
  @TaskMutation(FlashcardTaskResolver.taskName, () => ID)
  create(
    @Args('projectId', { type: () => ID }) projectId: string,
    @Args('eventId', { type: () => ID }) eventId: string,
    @Args('data') data: FlashcardTaskInput,
  ) {
    return this.taskService.create(
      projectId,
      eventId,
      data,
      AppType.FLASHCARD,
      TaskType.FLASHCARD_VIEW,
    );
  }

  @UseGuards(RolesGuard)
  @TaskMutation(FlashcardTaskResolver.taskName, () => Boolean)
  update(
    @Args('projectId', { type: () => ID }) projectId: string,
    @Args('eventId', { type: () => ID }) eventId: string,
    @Args('taskId', { type: () => ID }) taskId: string,
    @Args('data') data: FlashcardTaskUpdateInput,
  ) {
    return this.taskService.update(projectId, eventId, taskId, data);
  }
}
