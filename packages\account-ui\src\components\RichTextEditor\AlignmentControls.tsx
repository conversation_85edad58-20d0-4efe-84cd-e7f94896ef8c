import {
  AlignCenterOutlined,
  Align<PERSON>eftOutlined,
  AlignRightOutlined,
} from '@ant-design/icons';
import { Space } from 'antd';
import { EditorState, Modifier } from 'draft-js';
import ControlButton from './ControlButton';

const ALIGNMENT_TYPES = [
  { label: 'Left', style: 'left', icon: AlignLeftOutlined },
  { label: 'Center', style: 'center', icon: AlignCenterOutlined },
  { label: 'Right', style: 'right', icon: AlignRightOutlined },
];

export default function AlignmentControls({
  editorState,
  onToggle,
  disabled,
}: {
  editorState: EditorState;
  onToggle: (alignment: string) => void;
  disabled?: boolean;
}) {
  const selection = editorState.getSelection();
  const currentContent = editorState.getCurrentContent();
  const currentBlock = currentContent.getBlockForKey(selection.getStartKey());
  const blockData = currentBlock.getData();
  const currentAlignment = blockData.get('textAlign') || 'left';

  return (
    <Space size="small">
      {ALIGNMENT_TYPES.map((type) => {
        const IconComponent = type.icon;
        return (
          <ControlButton
            key={type.style}
            $active={currentAlignment === type.style}
            $disabled={disabled}
            onMouseDown={(e) => {
              e.preventDefault();
              onToggle(type.style);
            }}
            title={`Align ${type.label}`}
          >
            <IconComponent />
          </ControlButton>
        );
      })}
    </Space>
  );
}

export function toggleTextAlignment(
  editorState: EditorState,
  alignment: string,
): EditorState {
  const selection = editorState.getSelection();
  const currentContent = editorState.getCurrentContent();
  const currentBlock = currentContent.getBlockForKey(selection.getStartKey());
  const blockData = currentBlock.getData();
  const currentAlignment = blockData.get('textAlign');

  const newAlignment = currentAlignment === alignment ? 'left' : alignment;

  const newBlockData = blockData.set('textAlign', newAlignment);

  const newContentState = Modifier.setBlockData(
    currentContent,
    selection,
    newBlockData,
  );

  return EditorState.push(editorState, newContentState, 'change-block-data');
}
