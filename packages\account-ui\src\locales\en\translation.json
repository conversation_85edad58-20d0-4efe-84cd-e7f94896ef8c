{"general": {"image-ratio": "Image should have {{ratio}} aspect ratio", "recommended-dimension": "Recommended Dimension {{dimension}}", "yes": "Yes", "no": "No", "login-subtitle": "You are a few clicks away from a thriving community", "login-title": "Sign Into your account", "platform": "AirLyft"}, "campaigns": "Campaigns", "integrations": "Integration", "questboard": {"title": "Questboard", "select-quests": "Select any quest below to add on your community's main quest board. This will enable your community to engage and provide continuous value.", "add-quests": "Add Quests", "description": "The questboard is shown directly on your community page"}, "campaign": {"create": "Create Campaign", "edit": "Edit Campaign", "delete": "Delete Campaign", "clone": "<PERSON>lone Campaign", "deleted": "Campaign Deleted", "cloned": "Campaign Cloned", "confirm-clone": "Confirm Clone!", "status": "Campaign Status", "overview": "Campaign Overview", "settings": "Campaign Settings", "quests": "Campaign Quests", "details": "Campaign Details", "description": "Campaign Description", "public-url": "Campaign Public URL", "title": "Campaign Title", "information": "Campaign Information", "views-or-participants": "Campaign Views / Participants", "only-delete-published": "You can only delete campaigns that have not been published.", "copy-url": "Copy Campaign URL", "url-copied": "Campaign URL Copied", "view-page": "View Campaign Page", "visibility": "Campaign Visibility", "banner": "Featured and Promotional Image", "publish-success": "Campaign Published Successfully", "settled": "Campaign Settled!", "cant-update-quests": "Below are all the added quests, update is restricted as the campaign is {{state}}", "error-updating": "Error updating campaign", "error-deleting": "Error deleting this campaign", "error-cloning": "Error cloning this campaign", "error-creating": "Error creating this campaign", "error-publishing": "Error publishing this campaign", "cant-deleted": "<PERSON><PERSON> delete this campaign", "delete-draft": "Are you sure you want to delete this draft campaign?", "clone-draft": "Are you sure you want to clone this campaign?", "visibility-label": {"PRIVATE": "Private - only people with the link can access this campaign page", "PUBLIC": "Public - visible in your community campaigns", "PUBLIC_EXPLORABLE": "Explorable - featured on {{platform}} Explore page (contact support for listing)"}, "quest-subtitle": "Add quests that participants perform in this campaign.", "quest-title": "Quests you have added for this campaign", "quest-add": "Add quests to your campaign", "task-form-title": "Basic Details", "task-form-description": "Customize Title, Description and Fuel", "task-form-media-title": "Custom Task Icon", "task-form-media-description": "Add a custom icon for your task", "promote": "Promote Your Campaign", "confirm-announcement": "Confirm Announcement", "announce-alert": "Upon announcement, all followers will be promptly notified of this forthcoming event through in-app notifications within the {{platform}} application", "clone-alert": "Please note that rewards (if any) will NOT be cloned. Kindly also re-configure the dates (start, end & quest conditions) before Publishing the campaign. Also, Quiz quest will NOT be cloned!", "announce": "Announce", "announce-box": "Announce the campaign to your followers", "publish-alert": "Once you Publish the campaign, you cannot update the start date & time, rest all other details can be updated", "confirm-publish": "Confirm Publish", "publish": "Publish", "next": "Next", "back": "Back", "fuel-filter": "The total cFuel and XP will be updated to reflect the selected date range.", "webhook": "Webhook", "zapierWebhook": "Zapier Webhook", "analytics": "Campaign Analytics"}, "task": {"title": "Task Title", "tooltip": "This is the task heading shown in the task list.", "description": "Task Description", "description-tooltip": "You may add additional description/instructions for this task and it will be shown when the user opens the task.", "description-placeholder": "An optional description of the task", "add": "Add Task", "update": "Update", "twitter-post-tip": "Besides verifying the above conditions, you can also manually review tweets before the user is rewarded cfuel and xp. This is helpful in case of user generated content.", "LUCKYDRAW_PLAY": {"title": "Spin the Wheel", "description": "Spin the wheel and stand a chance to win amazing prices."}, "LUCKYDRAW_SLOT": {"title": "Slot Machine", "description": "Spin the slot machine and stand a chance to win amazing prices."}, "LUCKYDRAW_BOX": {"title": "Mystery Box", "description": "Open the mystery box and stand a chance to win amazing prices."}, "BLOG_COMMENT": {"title": "Blog Comment", "description": "Ask users to comment on your blog post"}, "BLOG_WRITE": {"title": "Blog Write", "description": "Ask users to write a blog post"}}, "error": {"invalid-credentials": "Invalid credentials! Please try again.", "account-linked-to-another-user": "Connection Failed! Account linked to another user", "wallet-not-installed": "Could not find selected wallet on this browser", "wallet-sign-in-rejected": "Sign in was rejected! Please retry and sign the message to continue", "token-expired": "This email link has expired. Please login again", "connection-token-expired": "Connection timed out. Please try again.", "connection-link-expired": "This verification link has expired. Please request a new one.", "something-went-wrong": "Something went wrong, please try again", "event-not-ongoing": "This campaign is not ongoing. Please wait for the campaign to start and then try again.", "email-missing-from-list": "This email address does not belong in host's verification list", "wallet-missing-from-list": "This EVM wallet address does not belong in host's verification list", "incorrect-url-entered": "The url entered is not as expected, please check if the URL is correct.", "post-not-found": "Could not find the post", "post-not-a-comment": "You have entered a link that is not a comment, please change the URL.", "invalid-post": "This link is invalid. Please check the url and try again.", "post-not-a-share": "You have entered a link that is not a shared post.", "no-giveaway": "No giveaway found", "event-settled-already": "Campaign settled already", "community-url": "Community Url cannot contain upper case letters"}, "fuel": {"title": "c<PERSON><PERSON>", "task-description": "Amount of cFuel users will get for completing this task", "help": "cFuel is a reward. Participants can burn (spend) it in your Shop for community-based rewards.", "released": "c<PERSON><PERSON>", "collected": "<PERSON><PERSON><PERSON>", "reward-any-email": "Approve any verified email address", "reward-list-email": "Approve specific email addresses only", "reward-any-wallet": "Approve any wallets", "reward-list-wallet": "Approve specific wallets only", "average": "Average cFuel", "max": "<PERSON> cFuel", "csv": "You can also download the complete cFuel CSV data", "statistics": "Community Fuel Statistics", "total": "Total cFuel"}, "xp": {"title": "XP", "help": "XP will be used to score users. It will be utilized in all kinds of rankings and leaderboards.", "task-description": "Amount of xp users will get for completing this task"}, "quests": "Quests", "quest": {"rearrange": "You can drag items to rearrange them."}, "rewards": "Rewards", "reward": {"add": "Add <PERSON>", "campaign-subtitle": "Set up rewards and the winner selection strategy.", "empty": "There are no rewards for this campaign at the moment. To boost engagement, please add some rewards.", "fuel-burn": "<PERSON> cFuel to claim reward", "nft-burn": "Burn NFTs to claim reward", "update-secondary": "Updating this reward will only effect future claims and not the ones that have already claimed."}, "widgets": "Widgets", "widget": {"campaign-subtitle": "Use this campaign in your own side using embeddable widget.", "empty": "There are no widgets for this campaign at the moment."}, "distribution": {"title": "Winner Selection Strategy", "subtitle": "How would you like the winners to be selected"}, "giveaway": {"MERCHANDISE": {"title": "Merchandise", "description": "Use this to give merchandise in return of cFuel", "description-tooltip": "This will be shown to the users when they swap their cFuel for this merchandise"}}, "app": {"AIRBOOST": {"title": "Airboost Referral", "description": "Allow users to refer their friends & family to enter your campaign.", "long-description": "Airboost helps you boost your campaign using the power of referrals. Users can invite their friends and family to participate in your campaign by sharing their unique referral link. Every user who successfully refers someone else can earn entries.", "task-form-description": "Click here to customize title and description", "task-form-alert": "Following fields can not be updated once saved", "task-form-xp-label": "Xp per successful referral", "task-form-xp-tooltip": "The number of xp each participant gets when a referred user successfully completes at least one task", "task-form-fuel-label": "cFuel per successful referral", "task-form-fuel-tooltip": "The number of cFuel each participant gets when a referred user successfully completes at least one task", "task-form-max-label": "Maximum Referrals per user", "task-form-max-tooltip": "The maximum number of referrals each person needs to perform", "task-form-share-title-label": "Share Title", "task-form-share-title-tooltip": "Text that goes in emails subjects, tweets, and DMs. Referral URL is automatically attached after the title (or after the body in emails).", "task-form-share-title-placeholder": "Participate in this amazing campaign at {{platform}}", "task-form-share-body-label": "Share Text Body", "task-form-share-body-tooltip": "Text that goes in the email body and other long-form text areas. Referral URL is automatically attached after the body (where present).", "task-form-share-body-placeholder": "Hey, I wanted to inform you that I just took part in this amazing campaign at {{platform}} and I wanted to ask you to also participate in the campaign for a chance to win great prizes!"}, "CHECKIN": {"title": "Check-in", "description": "A daily check-in task that gives users the option to come and perform check-ins to your campaign."}, "LUCKYDRAW": {"title": "Lucky draw", "description": "Reward the users with fuel or points through this lucky draw task, you can also set the probability of winning a reward"}, "BLOG": {"title": "Blog", "description": "Ask users to write a blog or comment on your blog post"}}, "communities": "Communities", "community": {"premium": "{{platform}} Premium", "premium-subtitle": "Check your premium status and upgrade your plan", "advance": "Advanced Settings", "advance-subtitle": "Setup Webhook secret and other settings", "advance-webhook": "Webhook Signature Secret", "create": "Create Community", "title": "Community", "settings": "Community Settings", "information": "Community Information", "bio": "Bio", "bio-description": "", "information-subtitle": "Add info that helps people know more about your community", "urls": "Public Links", "urls-subtitle": "URLs for your homepage, socials etc.", "name": "Community Name", "name-tooltip": "Name for your community For example: {{platform}}, Beamswap, Moonbeam etc.", "public-link": "Your Community's {{platform}} Page", "public-link-tooltip": "This would be the url that you can share to your community for quests, campaigns and rewards.", "description": "Community Description", "logo": "Community Logo", "logo-tooltip": "Please keep the logo aspect ratio as 1:1 for it to be clearly visible to the community", "banner": "Community Banner", "step1": "Community Basic Details", "step1-description": "Please create a community. This can be the name of your product, organization, NFT collection, GameFi App, dApp or similar. Your community can then have quests, campaigns and rewards.", "step2-description": "In this section, you can add some optional information that will show up on your community's main page.", "switched": "Community Switched", "created": "Community Created", "creating": "Creating Community ...", "could-not-create": "Could not create community", "ecosystem": "Ecosystem", "ecosystem-tooltip": "Select all the blockchain ecosystem your community works on", "sector": "Sector", "sector-tooltip": "Select all the sectors your project provides service for", "ecosystem-AVAX": "Avalanche", "ecosystem-BNB": "BNB", "ecosystem-CRAB": "<PERSON><PERSON>", "ecosystem-ETH": "Ethereum", "ecosystem-EVMOS": "Evmos", "ecosystem-IOTEX": "IoTex", "ecosystem-MOONBEAM": "Moonbeam", "ecosystem-MOONRIVER": "Moonriver", "ecosystem-TELOS": "Telos", "ecosystem-POLYGON": "Polygon", "ecosystem-DOGECHAIN": "<PERSON><PERSON><PERSON>", "ecosystem-POLKADOT": "<PERSON><PERSON>t", "ecosystem-ASTAR": "Astar", "ecosystem-POLIMEC": "Polimec", "ecosystem-SHARDEUM": "Shardeum", "ecosystem-ARBITRUM": "Arbitrum", "ecosystem-SONEIUM": "Soneium", "sector-DEFI": "<PERSON><PERSON><PERSON>", "sector-GAMEFI": "GameFi", "sector-NFT": "NFT", "sector-DAO": "DAO", "sector-METVERSE": "Metaverse", "sector-DEX": "DEX", "sector-STAKING": "Staking", "sector-BRIDGE": "Bridge", "sector-LAUNCHPAD": "Launchpad", "sector-CEFI": "CeFi", "sector-COLLECTIBLES": "Collectibles", "sector-WEB3": "Web3", "sector-DID": "DID", "sector-SOCIAL": "Social", "sector-INFRASTRUCTURE": "Infrastructure", "sector-WALLET": "Wallet", "sector-PRIVACY": "Privacy", "sector-STORAGE": "Storage", "sector-CEX": "CEX", "team": "Team Access", "team-subtitle": "Manage community with your team", "role.edit": "Edit Role", "role.delete": "Delete User from Project", "join": "Join a Team with Invite Code", "or-create": "or create your own community below", "integrations": "Task Integrations", "integrations-subtitle": "Manage your task integrations"}, "winners": {"columns": {"amount": "<PERSON><PERSON> Amount", "status": "Reward Status", "updatedAt": "Updated At", "details": "Claim details"}, "status": {"DRAFT": "Draft", "ISSUED": "<PERSON><PERSON><PERSON>", "PROCESSING": "Claiming", "SUCCESS": "Claimed", "FAILED": "Failed"}, "settlement": {"review-participants": "Review Participants", "review-submissions": "Review Submissions", "additional": "Giveaway Settlement Information", "date-time": "Settlement Date and Time:", "winners-count": "Number of Winners:", "info": "The distribution type of this giveaway is a {{distributionInfo}}. Additionally, you can review user submissions and mark them as valid or invalid if you find any submissions suspicious."}, "manual-selection": {"title": "It's almost time to choose winners", "subtitle": "Please follow the steps to choose winners", "download": {"title": "Download Participants CSV", "subtitle": " Apply as many filters and download the participants CSV to review the participants"}, "upload": {"title": "Review and Upload Winners.", "subtitle": "Review the downloaded CSV participant file, keep the participants you want to choose as winners, and remove the remaining ones.You can also remove any column except userId. Once finalized, please upload here.", "success": "Winners have been successfully uploaded. Please review your changes and click on submit", "dragger": {"title": "Click or drag the participants CSV file (only CSV files are accepted)", "subtitle": "{{platform}} will identify the participants based on the User ID column, so please ensure that you do not delete the User ID column.", "no-data-error": "Unable to retrieve any users. Please check the CSV file and try again.", "invalid-file": "Error reading the file.", "no-new-winners": "No new winners were added. Please check that your uploaded file before adding the winners."}}, "settle-actions": {"clear": {"title": "Clear Draft Winners", "confirm-msg": "Are you sure you want to clear the draft winners?", "confirm-warning": "This action will remove all draft winners, but confirmed winners will remain unaffected.", "success": "Draft winners have been successfully removed, and you can start over.", "failed": "Failed to remove draft winners. Please try again."}, "settle": {"title": "Submit Selected Winners", "confirm-msg": "Are you sure you want to submit the selected winners?", "confirm-warning": "Submitted winners are final and cannot be modified. You can add more winners later.", "success": "Winners have been successfully submitted for this giveaway.", "failed": "Failed to submit winners. Please try again."}, "delete": {"success": "Winner has been successfully deleted.", "failed": "Error while deleting winner!"}}}}, "shop": {"title": "Shop", "info": "The shop is shown directly on your community page. Each shop item can only be claimed once per user.", "add-rewards": "Add <PERSON>", "select-reward": "Select any reward below to add on your community. This will enable your community to engage and provide continuous value.", "shop-page": "Shop Page", "active-reward": "The rewards below are active right now.", "no-reward": "No Rewards created yet", "item-details": "Shop Item Details"}, "integration": {"add-platform": "Add Platform", "select-platform": "You can connect and configure your platform by clicking on the platform you want to connect and configure"}, "distribution-type": {"FCFS_TASK_ID": {"short": "FCFS Task", "long": "Specific task-based FCFS with instant claim"}, "FCFS_RANGE": {"short": "FCFS Range", "long": "First come first serve with instant claim"}, "MANUAL_CONTINUOUS": {"short": "Manual", "long": "Manual selection during the campaign"}, "FCFS_RANGE_END": {"short": "FCFS After End", "long": "First come first serve at the end of campaign", "description": "Participants who meet the criteria will be able to claim the reward at the end of the campaign"}, "MANUAL_END": {"short": "Manual selection at end of campaign", "long": "Manual Selection at end of campaign", "description": "You will be able to pick the winners once the campaign has ended"}, "RANDOM_END": {"short": "Ranked <PERSON> Raffle End", "long": "Ranked <PERSON> Raffle at the end of campaign", "description": "Our system will automatically select the winner(s) at the end of the campaign"}, "RANDOM_REC": {"short": "Ranked Random Raffle", "long": "Ranked <PERSON> during the campaign", "description": "Our system will automatically select the winner(s) during the campaign"}, "RANDOM_SPECIFIC_END": {"short": "Ranked Random Raffle", "long": "Ranked Random Raffle at specific time", "description": "Our system will automatically select the winner(s) at the specified time"}, "RANK_END": {"short": "Rank After End", "long": "Rank based selection at the end of campaign", "description": "Out system will automatically select the winner(s) at the end of the campaign"}}, "onboarding-apps": {"AIRPOOLS": {"videoLink": "https://www.loom.com/embed/4ccad8426fbc40ac80c809f3fd1aa98e?sid=e3f11c39-5a14-424e-a2e0-9dd2eb10e118", "docsLink": "https://docs.airlyft.one/air-pool/", "title": "A quick video to show how AirPool works", "description": "Air Pools allow you to deposit tokens or NFTs to be utilized as rewards for campaign hosted on the platform. Any rewards that remain unclaimed by participants can be withdrawn back by you or used for a different campaign."}, "AIRTOKENS": {"videoLink": "https://www.loom.com/embed/098ded20edb640d7a11e9ef995b9ad5a?sid=ae1b9e5b-355e-4427-862a-315c07695807", "docsLink": "https://docs.airlyft.one/air-token/", "title": "A quick video to show how AirToken works", "description": "Air Token allows you to deploy tokens or NFTs. By doing so, these assets can be utilized as rewards for one or more campaigns hosted on the platform."}, "CAMPAIGNS": {"videoLink": "https://www.loom.com/embed/098ded20edb640d7a11e9ef995b9ad5a?sid=ae1b9e5b-355e-4427-862a-315c07695807", "docsLink": "https://docs.airlyft.one/event/", "title": "A quick video to show how Campaigns works", "description": "Campaigns are marketing events which you can create on {{platform}} where you can configure all the details, specify quests and decide reward distribution strategy."}}, "subscription": {"view": "View Plans & Pricing", "monthly-usage": "Monthly Entries Usage", "monthly-usage-ai": "Monthly AI Verification Usage", "plan": {"ENTERPRISE_TRIAL": "Enterprise Trial", "ENTERPRISE": "Enterprise", "FREE": "Free", "PROFESSIONAL": "Professional", "PROFESSIONAL_BIANNUAL": "Professional Half Yearly", "STARTUP": "Startup", "STARTUP_BIANNUAL": "Startup Half Yearly", "TRIAL": "Trial"}}, "superadmin": {"settings": "Super Admin Settings", "chains": "Blockchains", "chains-subtitle": "Add new Blockchains to the platform", "project-verification": "Manage Communities", "project-verification-subtitle": "Manage visibility of community on the public platform", "analytics": "Super Admin Analytics", "analytics-subtitle": "Shows graphs & statistics across all projects", "airpool-assets": "Airpool Assets", "airpool-assets-subtitle": "Manage Airpool asset whitelist", "dau": "Daily Active Users", "mau": "Monthly Active Users", "wallet-address-date": "Unique Wallet Addresses Growth", "top-n-quest": "Top 20 Quests", "weekly-top-n-quest": "Weekly Top Quests", "weekly-user-count": "New Users Onboarded", "total-user-count": "User Growth", "top-countries": "Top Countries", "global-leaderboard": "Global Leaderboard", "global-leaderboard-subtitle": "View & export a global leaderboard which is across all projects", "referrals": "Global Referral Codes", "referrals-subtitle": "Manage global referral codes", "referral-codes": "Referral Codes", "promote": "Promote Campaigns", "promote-subtitle": "Manage promotions"}, "airpool": {"create-title": "Select Asset Type", "create-subtitle": "Select an asset that you want to deposit. You can then use it as a reward."}, "air-token": {"create-title": "Select Token Type", "create-subtitle": "Select an asset type to create. You can then use it as a reward."}, "asset-type": {"ERC20": "ERC20", "ERC721": "ERC721", "ERC1155": "ERC1155", "NATIVE": "Native", "DOTSAMA_TOKEN": "Substrate Native / Asset", "DOTSAMA_NFT": "Substrate NFT"}}