import { AuthProvider } from '@models/auth';
import { BadRequestException, ParseUUIDPipe } from '@nestjs/common';
import {
  Args,
  Context,
  ID,
  Int,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { decodeAddress, encodeAddress } from '@polkadot/util-crypto';
import { ProjectUserInfo } from '@root/xp/project-stats.dto';
import { XPService } from '@root/xp/xp.service';
import { PublicUser } from './user.entity';
import { UserService } from './user.service';

@Resolver((of) => PublicUser)
export class UserPublicResolver {
  constructor(
    private readonly userService: UserService,
    private readonly xpService: XPService,
  ) {}

  @Query((returns) => PublicUser, { description: 'User details' })
  user(
    @Args('username', { type: () => String, nullable: true }) username?: string,
  ) {
    return this.userService.findOne({
      where: {
        id: username,
      },
    });
  }

  @Query((returns) => ProjectUserInfo, {
    description: "Get user stats in your project using user's providerId",
  })
  async userByProvider(
    @Args('projectId', { type: () => ID! }, ParseUUIDPipe) projectId: string,
    @Args('providerId', { type: () => String! }) providerId: string,
    @Args('provider', { type: () => AuthProvider! }) provider: AuthProvider,
  ) {
    const whitelistedProjects = [
      '8a9d186c-69da-400d-ab50-326247bf3d5f',
      '25cb54a8-7468-4bfe-b056-52f3628688a9',
      'bfb7fbf6-bcd9-487b-804f-371663fc2efe',
      '7066ff25-8f9c-4805-b112-d8acdd69c57a', // subwallet
      'cbab3dad-2ce1-4e36-99c6-060f6c50b315', // playnation
      '4e19e055-314d-4dbf-99b2-d846dab7b5e8', // booka
    ];
    if (whitelistedProjects.indexOf(projectId) < -1) {
      throw new BadRequestException('Invalid project');
    }

    if (provider === AuthProvider.DOTSAMA_BLOCKCHAIN) {
      try {
        providerId = encodeAddress(decodeAddress(providerId), 42);
      } catch (error) {
        throw new BadRequestException('Invalid providerId');
      }
    }
    if (provider === AuthProvider.EVM_BLOCKCHAIN) {
      try {
        providerId = providerId.toLowerCase();
      } catch (error) {
        throw new BadRequestException('Invalid providerId');
      }
    }

    const auth = await this.userService.findAuth(provider, providerId);
    if (!auth) {
      throw new BadRequestException('User not found');
    }
    return this.xpService.projectUserInfo(projectId, auth.userId);
  }

  @ResolveField((returns) => Int)
  async rep(@Parent() parent: PublicUser) {
    if (!parent.id) return 0;
    return this.xpService.getUserReputation(parent.id);
  }

  @Query((returns) => ID, {
    nullable: true,
    description: 'Get a temporary userId',
  })
  tempUserId(@Context() context: { req: any }) {
    return context.req.sessionID;
  }
}
