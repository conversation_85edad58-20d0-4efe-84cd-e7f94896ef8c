import { gql, useMutation } from '@apollo/client';
import {
  InsertResult,
  Mutation_createFlashcardTaskArgs,
  Mutation_updateFlashcardTaskArgs,
  UpdateResult,
} from '@airlyft/types';

export const FLASHCARD_QUERY_FRAGMENT = gql`
  fragment FlashcardTaskDataFragment on FlashcardTaskData {
    sections {
      id
      order
      content
    }
  }
`;

const CREATE_FLASHCARD_TASK_MUTATION = gql`
  mutation CreateFlashcardTask(
    $projectId: ID!
    $eventId: ID!
    $data: FlashcardTaskInput!
  ) {
    createFlashcardTask(projectId: $projectId, eventId: $eventId, data: $data)
  }
`;

export function useCreateFlashcardTask() {
  return useMutation<InsertResult, Mutation_createFlashcardTaskArgs>(
    CREATE_FLASHCARD_TASK_MUTATION,
  );
}

const UPDATE_FLASHCARD_TASK_MUTATION = gql`
  mutation UpdateFlashcardTask(
    $projectId: ID!
    $eventId: ID!
    $taskId: ID!
    $data: FlashcardTaskUpdateInput!
  ) {
    updateFlashcardTask(
      projectId: $projectId
      eventId: $eventId
      taskId: $taskId
      data: $data
    )
  }
`;

export function useUpdateFlashcardTask() {
  return useMutation<UpdateResult, Mutation_updateFlashcardTaskArgs>(
    UPDATE_FLASHCARD_TASK_MUTATION,
  );
}
