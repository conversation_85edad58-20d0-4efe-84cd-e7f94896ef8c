import { S3Client } from '@aws-sdk/client-s3';
import {
  createPresignedPost,
  PresignedPostOptions,
} from '@aws-sdk/s3-presigned-post';
import { StorageDto, UploadType } from '@models/storage/storage.dto';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

const FILE_SIZE_LIMITS = {
  [UploadType.TASK_ICON]: 0.5 * 1024 * 1024,
  [UploadType.PROJECT_LOGO]: 1 * 1024 * 1024,
  [UploadType.BLOCKCHAIN_LOGO]: 1 * 1024 * 1024,
  [UploadType.ASSET_LOGO]: 10 * 1024 * 1024,
  [UploadType.BANNER]: 3 * 1024 * 1024,
  [UploadType.USER_IMAGE]: 2 * 1024 * 1024,
  [UploadType.PUBLIC_TASK_IMAGE]: 10 * 1024 * 1024,
  [UploadType.RICH_TEXT_MEDIA]: 10 * 1024 * 1024,
};

@Injectable()
export class StorageService {
  s3Client;
  constructor(private readonly configService: ConfigService) {
    this.s3Client = new S3Client({
      region: configService.get<string>('S3_REGION'),
      credentials: {
        accessKeyId: configService.get<string>('S3_KEY'),
        secretAccessKey: configService.get<string>('S3_SECRET'),
      },
    });
  }

  async getSignedUrlOurs(dto: StorageDto, userId: string) {
    const maxFileSize = FILE_SIZE_LIMITS[dto.uploadType] || 1 * 1024 * 1024;

    const isVideo =
      dto.key.endsWith('.mp4') ||
      dto.key.endsWith('.mov') ||
      dto.key.endsWith('.webm') ||
      dto.key.endsWith('.avi');

    const contentType = isVideo ? 'video/*' : 'image/*';
    const maxFileSizeInMB = isVideo
      ? FILE_SIZE_LIMITS[UploadType.PUBLIC_TASK_IMAGE] / (1024 * 1024)
      : maxFileSize / (1024 * 1024);

    const fileSizeInMB = dto.size / (1024 * 1024);

    if (fileSizeInMB > maxFileSizeInMB) {
      throw new BadRequestException(
        `File is too large! Max allowed size for ${dto.uploadType} is ${maxFileSizeInMB}MB`,
      );
    }

    try {
      const param: PresignedPostOptions = {
        Key: dto.key,
        Bucket: this.configService.get<string>('S3_BUCKET'),
        Fields: {
          key: dto.key,
          'Content-Type': contentType,
        },
        Conditions: [
          ['eq', '$key', dto.key],
          ['content-length-range', 0, maxFileSize],
          ['starts-with', '$Content-Type', contentType],
        ],
        Expires: 3 * 60, // 3mins
      };

      try {
        const presignedPost = await createPresignedPost(this.s3Client, param);
        return presignedPost;
      } catch (err) {
        console.error('Error generating presigned URL:', err);
        throw new BadRequestException('Failed to generate upload URL');
      }
    } catch (error) {
      if (error.code && error.code === '23505') {
        throw new BadRequestException('Error getting signed URL');
      }
      const errorMessage = error.message || 'Unknown error';
      throw new BadRequestException(`Error: ${errorMessage}`);
    }
  }
}
