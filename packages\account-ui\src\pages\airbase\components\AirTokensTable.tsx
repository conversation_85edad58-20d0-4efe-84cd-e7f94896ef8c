import {
  AssetType,
  BlockchainAsset,
  BlockchainType,
  TransactionStatus,
} from '@airlyft/types';
import { shortenAddress } from '@airlyft/web3-evm';
import {
  CheckCircleFilled,
  DeleteOutlined,
  LoadingOutlined,
  WarningFilled,
} from '@ant-design/icons';
import StyledButton from '@Components/StyledButton';
import Table from '@Components/Table';
import { PAGE_SIZE } from '@Root/constants';
import { useAppDispatch } from '@Root/store.hooks';
import { Avatar, Popconfirm, Space, Tag, Typography } from 'antd';
import { ColumnsType } from 'antd/lib/table/interface';
import { useContext, useState } from 'react';
import { ThemeContext } from 'styled-components';
import { useDeleteAirToken } from '../hooks/useDeleteAirToken';

import SyncAirPoolButton from './SyncAirTokenButton';
import { NotificationContext } from '@Pages/App';
import { useTranslation } from 'react-i18next';
import { formatDotsamaAccount } from '@Root/utils/dotsama';
import { ethers } from 'ethers';

const DeleteAirToken = ({
  projectId,
  id,
}: {
  projectId: string;
  id: string;
}) => {
  const [open, setOpen] = useState(false);
  const [deleteAirToken, { loading }] = useDeleteAirToken();
  const { api } = useContext(NotificationContext);

  const handleOk = () => {
    deleteAirToken({
      variables: {
        projectId,
        id,
      },
      onCompleted: () => {
        setOpen(false);
      },
      onError: () => {
        setOpen(false);
        api?.error({
          message: 'Error',
          description: 'Failed to delete Air Token',
        });
      },
    });
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <Popconfirm
      title="Are you sure？"
      okText="Yes"
      open={open}
      onConfirm={handleOk}
      okButtonProps={{ loading }}
      onCancel={handleCancel}
    >
      <StyledButton
        onClick={() => setOpen(true)}
        type="link"
        icon={<DeleteOutlined />}
      ></StyledButton>
    </Popconfirm>
  );
};

export default function AirTokensTable({
  projectId,
  data,
  loading,
}: {
  projectId: string;
  data?: Array<BlockchainAsset>;
  loading?: boolean;
}) {
  const theme = useContext(ThemeContext);
  const { t } = useTranslation();

  const columns: ColumnsType<any> = [
    {
      title: 'Asset',
      key: 'asset',
      render: (_, record: BlockchainAsset) => {
        return (
          <Space>
            {record.icon &&
              (record.icon.endsWith('.mp4') ||
              record.icon.endsWith('.mov') ||
              record.icon.endsWith('.webm') ||
              record.icon.endsWith('.avi') ? (
                <video width={24} height={24} autoPlay muted loop>
                  <source src={record.icon} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ) : (
                <Avatar size="small" src={record.icon} />
              ))}
            {record.assetType !== AssetType.NATIVE &&
              record.address != ethers.constants.AddressZero && (
                <Typography.Paragraph
                  style={{ margin: 0 }}
                  copyable={{
                    text: record.address,
                  }}
                ></Typography.Paragraph>
              )}
            <span>({record.ticker})</span>
          </Space>
        );
      },
    },
    {
      title: 'Token Id',
      key: 'tokenId',
      render: (_, record: BlockchainAsset) => {
        switch (record.assetType) {
          case AssetType.ERC1155:
          case AssetType.DOTSAMA_TOKEN:
          case AssetType.DOTSAMA_NFT:
            return record.tokenId;
          default:
            return '-';
        }
      },
    },
    {
      title: 'Asset Type',
      key: 'assetType',
      render: (_, record: BlockchainAsset) => {
        return <span>{t(`asset-type.${record.assetType}`)}</span>;
      },
    },
    {
      title: 'Blockchain',
      key: 'blockchain',
      render: (_, record: BlockchainAsset) => {
        return (
          <Space>
            <Avatar size="small" src={record.blockchain.icon} />
            <span>{record.blockchain.name}</span>
          </Space>
        );
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (_, record: BlockchainAsset) => {
        const txStatus = record.txStatus;
        if (txStatus === TransactionStatus.PROCESSING) {
          return <LoadingOutlined />;
        }

        if (txStatus === TransactionStatus.FAILED) {
          return (
            <Space size={4}>
              <WarningFilled style={{ color: 'red' }} />
              <span> Failed</span>
            </Space>
          );
        }

        if (txStatus === TransactionStatus.SUCCESS) {
          return (
            <Space size={4}>
              <CheckCircleFilled style={{ color: theme.colors.primary }} />
              <span> Created</span>
            </Space>
          );
        }

        return <Tag> {txStatus} </Tag>;
      },
    },
    {
      title: 'Creator',
      key: 'tokenCreator',
      render: (_, record: BlockchainAsset) => {
        const creatorBlockchainAddress = record.creatorBlockchainAddress;

        if (!creatorBlockchainAddress) return '-';
        return (
          <Typography.Paragraph
            style={{ margin: 0 }}
            copyable={{
              text: creatorBlockchainAddress,
            }}
          >
            {record.blockchain.type === BlockchainType.EVM
              ? shortenAddress(creatorBlockchainAddress, 3)
              : formatDotsamaAccount(
                  { address: record.creatorBlockchainAddress! },
                  record.blockchain.chainId,
                )}
          </Typography.Paragraph>
        );
      },
    },
    {
      title: 'Action',
      key: 'action',
      fixed: 'right',
      render: (text: any, record: BlockchainAsset) => (
        <Space size="middle">
          {record.blockchain.type == BlockchainType.EVM && (
            <SyncAirPoolButton projectId={projectId} id={record.id} />
          )}
          {record.txStatus === TransactionStatus.FAILED && (
            <DeleteAirToken projectId={projectId} id={record.id} />
          )}
          {/* <Dropdown
            menu={{
              items: [],
            }}
            trigger={['click']}
          >
            <a>
              More <DownOutlined />
            </a>
          </Dropdown> */}
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={data || []}
      rowKey={'id'}
      scroll={{ x: 'max-content' }}
      loading={loading}
      pagination={{
        pageSize: PAGE_SIZE,
      }}
    />
  );
}
