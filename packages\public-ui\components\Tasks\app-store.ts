import { AuthProvider, Task } from '@airlyft/types';
import { create, useStore } from 'zustand';
import { AirboostApp } from './Airboost/airboost.app';
import { AirquestApp } from './Airquest/airquest.app';
import { getCustomApps } from './app-store.gql';
import {
  AirlyftApp,
  AirLyftTaskData,
  Provider,
  ProviderCategory,
} from './app-store.types';
import { BlogApp } from './Blog/blog.app';
import { ClaimTaskData } from './Claim/claim.app';
import { DiscordApp, DiscordProvider } from './Discord/discord.app';
import { EmailApp, EmailProvider } from './Email/email.app';
import { EvmApp } from './Evm/contract.app';
import { FaucetApp } from './Faucet/Faucet.app';
import { FormApp } from './Form/form.app';
import { InstagramApp } from './Instagram/instagram.app';
import { KickstarterApp } from './Kickstarter/kickstarter.app';
import { LuckydrawApp } from './Luckydraw/luckydraw.app';
import { MobileAppApp } from './MobileApp/mobile-app.app';
import { ProducthuntApp, ProducthuntProvider } from './Producthunt/producthunt.app';
import { QuizApp } from './Quiz/quiz.app';
import { RestApp } from './Rest/rest.app';
import { SecretCodeApp } from './SecretCode/secret-code.app';
import { SubgraphApp } from './Subgraph/subgraph.app';
import { SubsocialApp } from './Subsocial/subsocial.app';
import { SubstrateApp } from './Substrate/substrate.app';
import { TelegramApp, TelegramProvider } from './Telegram/telegram.app';
import { TermsApp } from './Terms/terms.app';
import { TwitterApp, TwitterProvider } from './Twitter/twitter.app';
import { UploadApp } from './Upload/upload.app';
import { UrlApp } from './Url/url.app';
import { WalletApp } from './Wallet/wallet.app';
import { YoutubeApp } from './Youtube/youtube.app';
import { CheckinApp } from './Checkin/checkin.app';

export interface AppStoreState {
  initialized: boolean;
  apps: AirlyftApp[];
  connectionProviders: Provider[];
  connectionGuards?: Array<AuthProvider>;
  data?: AirLyftTaskData[] | Task[];
}

const EvmProvider = {
  providerType: AuthProvider.EVM_BLOCKCHAIN,
  providerCategory: ProviderCategory.EVM_PROVIDER,
  lock: true,
  lockReason: 'Please connect wallet to unlock this task',
  config: {
    disclaimer: {
      connectAccount:
        'AirLyft never collects your private information and only uses your public address to verify your entries.',
    },
  },
};
const DotsamaProvider = {
  providerType: AuthProvider.DOTSAMA_BLOCKCHAIN,
  providerCategory: ProviderCategory.DOTSAMA_PROVIDER,
  lock: true,
  lockReason: 'Please connect wallet to unlock this task',
  config: {
    disclaimer: {
      connectAccount:
        'AirLyft never collects your private information and only uses your public address to verify your entries.',
    },
  },
};

const state: AppStoreState = {
  initialized: false,
  apps: [
    DiscordApp,
    TwitterApp,
    TelegramApp,
    SubsocialApp,
    FormApp,
    InstagramApp,
    YoutubeApp,
    UrlApp,
    UploadApp,
    QuizApp,
    EvmApp,
    SubstrateApp,
    WalletApp,
    SubgraphApp,
    RestApp,
    AirboostApp,
    EmailApp,
    TermsApp,
    FaucetApp,
    AirquestApp,
    LuckydrawApp,
    MobileAppApp,
    SecretCodeApp,
    ProducthuntApp,
    BlogApp,
    KickstarterApp,
    CheckinApp,
  ],
  connectionProviders: [
    ProducthuntProvider,
    DiscordProvider,
    TwitterProvider,
    EvmProvider,
    DotsamaProvider,
    TelegramProvider,
    EmailProvider,
  ],
  connectionGuards: [],
  data: [ClaimTaskData],
};

export const useAppStore = create<AppStoreState & { init: () => void }>(
  (set) => ({
    ...state,
    init: async () => {
      let customApps: AirlyftApp[] = [];
      try {
        const customAppsData = await getCustomApps();
        customApps = (
          (customAppsData.data.getCustomApps || []) as AirlyftApp[]
        ).map((customApp) => {
          const appConfig = state.apps.find(
            (item) => customApp.appType === item.appType,
          );

          return {
            ...customApp,
            tasks: (customApp.tasks ?? []).map((customAppTask) => {
              const taskConfig = (appConfig?.tasks ?? []).find(
                (appTask) => customAppTask.taskType === appTask.taskType,
              );

              return {
                ...customAppTask,
                hooks: taskConfig?.hooks,
                renderer: taskConfig?.renderer ?? customAppTask.renderer,
              };
            }),
          };
        });
      } catch (err) {}

      return set((state) => {
        return {
          ...state,
          apps: [...state.apps, ...customApps],
          initialized: true,
        };
      });
    },
  }),
);

export default function useAppSelector<T>(
  selector: (state: AppStoreState) => T,
): T {
  return useStore(useAppStore, selector);
}

useAppStore.getState().init();
