import { cn } from '@Root/utils/utils';
import { CaretDown, CaretUp } from '@phosphor-icons/react';
import {
  CompositeDecorator,
  ContentState,
  convertFromRaw,
  Editor,
  EditorState,
  ContentBlock,
} from 'draft-js';
import 'draft-js/dist/Draft.css';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

function MediaDisplay({
  block,
  contentState,
}: {
  block: ContentBlock;
  contentState: ContentState;
}) {
  const entityKey = block.getEntityAt(0);
  if (!entityKey) return null;

  const entity = contentState.getEntity(entityKey);
  const { src, mediaType } = entity.getData();

  if (!src) return null;

  if (mediaType === 'video' || entity.getType() === 'VIDEO') {
    return (
      <video
        controls
        preload="metadata"
        style={{
          maxWidth: '100%',
          height: 'auto',
          borderRadius: '6px',
          margin: '8px 0',
        }}
        src={src}
      >
        Your browser does not support the video tag.
      </video>
    );
  }

  return (
    <img
      src={src}
      alt="Rich Text Image"
      style={{
        maxWidth: '100%',
        height: 'auto',
        borderRadius: '6px',
        margin: '8px 0',
      }}
    />
  );
}

function mediaBlockRenderer(block: ContentBlock) {
  if (block.getType() === 'atomic') {
    return {
      component: MediaDisplay,
      editable: false,
    };
  }
  return null;
}

export default function TextEditor({
  readonly = true,
  defaultValue,
  expandable = false,
  maxHeight = 100,
  className,
}: {
  defaultValue: string;
  readonly?: boolean;
  expandable?: boolean;
  maxHeight?: number;
  className?: string;
}) {
  const ref = useRef<any>(null);
  const [isOverflow, setIsOverflow] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const { theme, systemTheme } = useTheme();
  const currentTheme = theme === 'system' ? systemTheme : theme;

  useEffect(() => {
    const container = ref.current;

    if (!container || !setIsOverflow) return;

    if (expandable && container.offsetHeight >= maxHeight) {
      setIsOverflow(true);
    }
  }, [ref, setIsOverflow]);

  function Link(props: any) {
    const { url } = props.contentState.getEntity(props.entityKey).getData();
    return (
      <a
        href={url}
        target="_blank"
        rel="noreferrer nofollow"
        referrerPolicy="no-referrer"
        style={{
          color: currentTheme === 'dark' ? '#93cefe' : '#3b5998',
          textDecoration: 'underline',
        }}
      >
        {props.children}
      </a>
    );
  }
  function findLinkEntities(
    contentBlock: any,
    callback: any,
    contentState: any,
  ) {
    contentBlock.findEntityRanges((character: any) => {
      const entityKey = character.getEntity();
      return (
        entityKey !== null &&
        contentState.getEntity(entityKey).getType() === 'LINK'
      );
    }, callback);
  }

  const decorator = new CompositeDecorator([
    {
      strategy: findLinkEntities,
      component: Link,
    },
  ]);
  let editorState = EditorState.createEmpty(decorator);

  if (!defaultValue) return <></>;

  if (defaultValue) {
    try {
      const parsedJson = JSON.parse(defaultValue);
      editorState = EditorState.createWithContent(
        convertFromRaw(parsedJson),
        decorator,
      );
    } catch (err) {
      editorState = EditorState.createWithContent(
        ContentState.createFromText(defaultValue),
        decorator,
      );
    }
  }

  if (!editorState.getCurrentContent().hasText()) return <></>;

  return (
    <div className="relative">
      {isOverflow && (
        <>
          {!isExpanded && (
            <div className="absolute bottom-0 z-10 h-[40px] w-full bg-gradient-to-t from-background/60 to-background/0"></div>
          )}
          <div
            className={`absolute z-20 flex items-center w-full justify-center -bottom-3`}
          >
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 z-10 rounded-full border bg-background/90 text-cs text-sm font-extrabold"
            >
              {isExpanded ? (
                <CaretUp size={16} weight="bold" />
              ) : (
                <CaretDown size={16} weight="bold" />
              )}
            </button>
          </div>{' '}
        </>
      )}

      <div
        className={cn(
          `text-base text-ch leading-normal overflow-hidden`,
          className,
          isExpanded ? 'mb-10' : '',
        )}
        ref={ref}
        style={
          expandable && !isExpanded
            ? {
                maxHeight,
                marginBottom: 0,
              }
            : {}
        }
      >
        <style jsx>{`
          :global(.RichEditor-text-align-left),
          :global(.RichEditor-text-align-left .public-DraftStyleDefault-block) {
            text-align: left !important;
          }
          :global(.RichEditor-text-align-center),
          :global(
              .RichEditor-text-align-center .public-DraftStyleDefault-block
            ) {
            text-align: center !important;
          }
          :global(.RichEditor-text-align-right),
          :global(
              .RichEditor-text-align-right .public-DraftStyleDefault-block
            ) {
            text-align: right !important;
          }
        `}</style>
        <Editor
          editorState={editorState}
          readOnly={readonly}
          onChange={() => {}}
          blockRendererFn={mediaBlockRenderer}
          blockStyleFn={(block) => {
            const blockData = block.getData();
            const textAlign = blockData.get('textAlign') || 'left';
            let className = '';

            switch (block.getType()) {
              case 'blockquote':
                className = 'RichEditor-blockquote';
                break;
              default:
                className = '';
            }

            className += ` RichEditor-text-align-${textAlign}`;
            return className.trim();
          }}
        />
      </div>
    </div>
  );
}
