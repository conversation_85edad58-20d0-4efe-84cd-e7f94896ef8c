import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { Alert, Col, Form, Input, Row, Spin, Tag, Tooltip } from 'antd';
import { NotificationContext } from '@Pages/App';
import {
  ApiKeyIntegrationData,
  Integration,
  IntegrationPlatformConfig,
  IntegrationType,
  PlatformField,
} from '@airlyft/types';
import StyledButton from '@Components/StyledButton';
import { firstOrNull } from '@Root/utils/arrayUtils';
import {
  useCreateApiKeyIntegration,
  useUpdateApiKeyIntegration,
} from './gql/api-key-integrations.gql';
import IntegrationTableModal from './IntegrationTableModal';
import { FormInstance } from 'antd/lib';
import useDebounce from '@Hooks/useDebounce';
import { useTelegramBotInfo } from '@Root/apps/Telegram/useTelegramQuery';
import Icon, { CloseCircleFilled } from '@ant-design/icons';
import { ReactComponent as TelegramSvg } from '@Components/Icons/telegram.svg';
import IntegrationDisplay from './IntegrationDisplay';

interface TelegramIntegrationModalProps {
  platform: IntegrationPlatformConfig;
  projectId: string;
  onCancel: () => void;
}

export default function TelegramIntegrationModal({
  platform,
  projectId,
  onCancel,
}: TelegramIntegrationModalProps) {
  return (
    <IntegrationTableModal
      projectId={projectId}
      platform={platform}
      onCancel={onCancel}
    >
      {(integrationData, onFormCancel, onMutate) => (
        <TelegramForm
          platform={platform}
          projectId={projectId}
          integrationData={integrationData}
          onCancel={onFormCancel}
          onMutate={onMutate}
        />
      )}
    </IntegrationTableModal>
  );
}

export const TelegramForm = ({
  platform,
  projectId,
  integrationData,
  onCancel,
  onMutate,
}: {
  platform: IntegrationPlatformConfig;
  projectId: string;
  integrationData: Integration | null;
  onCancel: () => void;
  onMutate?: (id: string) => void;
}) => {
  const [form] = Form.useForm();
  const { api } = useContext(NotificationContext);
  const botName = useMemo(
    () => (integrationData?.info as ApiKeyIntegrationData)?.name,
    [integrationData?.info],
  );
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createApiKeyIntegration, { loading: creating }] =
    useCreateApiKeyIntegration();
  const [updateApiKeyIntegration, { loading: updating }] =
    useUpdateApiKeyIntegration();

  const create = (values: any) => {
    createApiKeyIntegration({
      variables: {
        projectId,
        integrationName: platform.platformType,
        displayName: values.displayName,
        data: {
          name: platform.platformType,
          type: IntegrationType.API_KEY_INTEGRATION,
          data: {
            apiKey: values.apiKey,
            name: values.username,
          },
        },
      },
      onCompleted: ({ createApiKeyIntegration }) => {
        const id = firstOrNull(createApiKeyIntegration?.identifiers)?.id;
        if (id) {
          api?.success({
            message: 'Success',
            description: 'Integration created successfully',
          });
          onCancel();
          onMutate?.(id);
        } else {
          api?.error({
            message: 'Failed',
            description: 'Could not create the api key',
          });
        }
      },
      onError: (error) => {
        api?.error({
          message: 'Failed',
          description: 'Could not create the api key',
        });
      },
    });
  };

  const update = (values: any) => {
    if (!integrationData?.id) return;
    updateApiKeyIntegration({
      variables: {
        integrationName: platform.platformType,
        displayName: values.displayName,
        integrationId: integrationData?.id,
        projectId,
        data: {
          data: {
            apiKey: values.apiKey,
            name: values.username,
          },
        },
      },
      onCompleted: () => {
        api?.success({
          message: 'Success',
          description: 'Updated the api key',
        });
        onCancel();
      },
      onError: (error) => {
        api?.error({
          message: 'Failed',
          description: 'Could not update the api key',
        });
      },
    });
  };

  const onFinish = (values: any) => {
    integrationData?.id ? update(values) : create(values);
  };

  return (
    <Form
      layout="vertical"
      onFinish={onFinish}
      form={form}
      initialValues={{
        displayName: integrationData?.displayName,
      }}
    >
      {integrationData ? (
        isEditing ? (
          <TelegramInputs
            fields={platform.fields ?? []}
            form={form}
            integrationData={integrationData}
            handleSubmit={(isBotValid) => {
              setIsSubmitting(isBotValid);
            }}
          />
        ) : (
          <IntegrationDisplay
            title={
              <>
                Your API Key is already set up
                {botName && botTag(botName)}
              </>
            }
            handleEditIntegration={() => {
              setIsEditing(true);
            }}
          />
        )
      ) : (
        <TelegramInputs
          fields={platform.fields ?? []}
          form={form}
          integrationData={integrationData}
          handleSubmit={(isBotValid) => {
            setIsSubmitting(isBotValid);
          }}
        />
      )}

      <Form.Item>
        <Row justify="end" gutter={[16, 16]}>
          <Col>
            <StyledButton type="default" size="large" onClick={onCancel}>
              Cancel
            </StyledButton>
          </Col>
          {(isEditing || !integrationData) && (
            <Col>
              <StyledButton
                loading={creating || updating}
                disabled={creating || updating || !isSubmitting}
                type="primary"
                htmlType="submit"
                size="large"
              >
                {integrationData?.id ? 'Update' : 'Create'}
              </StyledButton>
            </Col>
          )}
        </Row>
      </Form.Item>
    </Form>
  );
};

function TelegramInputs({
  fields,
  form,
  integrationData,
  handleSubmit,
}: {
  fields: PlatformField[];
  form: FormInstance;
  integrationData: Integration | null;
  handleSubmit: (isBotValid: boolean) => void;
}) {
  const field = fields[0];
  const apiKey = Form.useWatch('apiKey', form);
  const debouncedApiKey = useDebounce(apiKey, 500);
  const { data: botInfo, isLoading } = useTelegramBotInfo(debouncedApiKey);
  const botName = useMemo(
    () => (integrationData?.info as ApiKeyIntegrationData)?.name,
    [integrationData?.info],
  );
  const isBotValid = useCallback(
    () => !!botInfo?.result?.username,
    [botInfo?.result?.username],
  );

  const validationStatusTpl = () => {
    if (!apiKey && botName) {
      return botTag(botName);
    }

    if (!apiKey) {
      return <></>;
    }

    if (isLoading) {
      return <Spin size="small" />;
    }

    {
      return isBotValid() ? (
        botTag(botInfo?.result?.username || botInfo?.result?.first_name)
      ) : (
        <Tooltip title="Could not find a group/channel with this username">
          <CloseCircleFilled style={{ color: '#E52935' }} />
        </Tooltip>
      );
    }
  };

  useEffect(() => {
    if (isBotValid()) {
      form.setFieldsValue({
        username: botInfo?.result?.username,
      });
    }
    handleSubmit(isBotValid());
  }, [isBotValid, botInfo?.result?.username, form, handleSubmit]);

  return (
    <>
      <Form.Item
        key="displayName"
        style={{ marginTop: '10px' }}
        label="Integration Name"
        name="displayName"
        rules={[{ required: true, message: 'Please provide a name' }]}
      >
        <Input size="large" />
      </Form.Item>

      <Form.Item
        key={field.name}
        style={{ marginTop: '10px' }}
        label={field.label}
        name={field.name}
        rules={[
          {
            required: true,
            message: `Please provide ${field.label}`,
          },
        ]}
      >
        <Input
          {...(field.placeholder && { placeholder: field.placeholder })}
          size="large"
          addonAfter={validationStatusTpl()}
        />
      </Form.Item>

      <Form.Item label="Bot Info" name="username" hidden>
        <Input value={botInfo?.result?.username} disabled />
      </Form.Item>

      {isBotValid() && (
        <Alert
          showIcon
          message="Please make sure you add the bot to the group/channel you want to validate as an admin, otherwise the validation will fail!"
          type="warning"
          style={{ marginBottom: '24px' }}
        />
      )}
    </>
  );
}

export const botTag = (name?: string) => {
  return (
    <Tag
      icon={<Icon component={TelegramSvg} style={{ color: '#26A5E4' }} />}
      color="processing"
      style={{
        marginRight: 0,
        fontWeight: 'bold',
      }}
    >
      {name}
    </Tag>
  );
};
