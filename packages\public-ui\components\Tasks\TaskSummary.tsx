import { useUserCurrentTaskParticipationMap } from '@Hooks/useTaskParticipation';
import { ParticipationStatus, ProjectEvent, Task } from '@airlyft/types';
import { Check } from '@phosphor-icons/react';
import { Fragment } from 'react';
import useAppSelector from './app-store';
import { selectAppByType, selectTaskByType } from './app-store.helper';
import AppStoreIconRenderer from './components/AppStoreIconRenderer';
import { useGetTasks } from './hooks/useGetTasks';
import { useRouter } from 'next/router';

const TaskSummaryTag = ({
  task,
  verified,
  onClick,
}: {
  task: Task;
  verified: boolean;
  onClick?: () => void;
}) => {
  const { appType, taskType, title } = task;

  const appKey = task.appKey as string | undefined;
  const taskKey = task.taskKey as string | undefined;
  const app = useAppSelector(selectAppByType(appType, appKey));
  const appTask = useAppSelector(
    selectTaskByType(appType, taskType, { appKey, taskKey }),
  );

  if (!app || !appTask) return <Fragment />;

  return (
    <div
      className={`text-xs cursor-pointer component-bg-hover border border-foreground/20 rounded py-1.5 px-2 flex space-x-1 items-center`}
      onClick={onClick}
    >
      {task.iconUrl ? (
        <div className="w-4 h-4 relative flex items-center justify-center">
          <img
            src={task.iconUrl}
            alt="Custom Task Icon"
            className="w-full h-full object-cover rounded"
          />
        </div>
      ) : (
        <AppStoreIconRenderer
          iconKey={task.taskType}
          className="h-4 w-4 flex-shrink-0"
          color={appTask.config?.color || app?.config.color}
          url={appTask?.config?.iconUrl || app?.config.iconUrl}
        />
      )}

      <span className="line-clamp-1"> {title}</span>
      {verified && (
        <Check
          size={16}
          className="text-primary-foreground bg-primary rounded-full p-1 flex-shrink-0"
          weight="bold"
        />
      )}
    </div>
  );
};

const TaskSummaryList = ({
  projectEvent,
  taskIds,
}: {
  projectEvent: ProjectEvent;
  taskIds: Array<string>;
}) => {
  const router = useRouter();
  const { data, loading: taskLoading } = useGetTasks(projectEvent.id);
  const { data: taskParticipation, loading: isParticipationLoading } =
    useUserCurrentTaskParticipationMap(projectEvent.id);

  let tasks = (data?.pTasks || []).filter(
    (task) => taskIds.findIndex((id) => task.id === id) >= 0,
  );

  return (
    <div className="flex flex-wrap gap-1">
      {tasks.map((task, key) => (
        <TaskSummaryTag
          task={task}
          key={key}
          verified={
            !!(
              taskParticipation?.get(task.id || '')?.status ==
              ParticipationStatus.VALID
            )
          }
          onClick={() => {
            if (!task) return;
            router.query['taskid'] = task.id;
            router.push(router, undefined, { shallow: true });
          }}
        />
      ))}
    </div>
  );
};

export default TaskSummaryList;
