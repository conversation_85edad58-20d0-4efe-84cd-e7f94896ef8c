import { createUnionType } from '@nestjs/graphql';
import { CustomTaskData } from '@root/custom-apps/custom-apps.dto';
import { NullableTaskData } from '@root/task/nullable-task-data.entity';
import { TaskType } from '@root/task/task.constants';
import { TwitterLikeRetweetTaskData } from '@twitter-app/tasks/twitter-like-retweet/twitter-like-retweet-task.dto';
import { AirboostReferralTaskData } from './airboost/airboost-referral-task.dto';
import { AirquestFollowTaskData } from './airquest/tasks/follow/airquest-follow-task.dto';
import { DiscordJoinTaskData } from './discord/tasks/join/discord-join-task.dto';
import { EmailWhitelistTaskData } from './email/tasks/whitelist/email-whitelist-task.dto';
import { EvmContractInteractTaskData } from './evm/tasks/evm-contract/evm-contract-interact-task.dto';
import { FaucetRawTaskData } from './faucet/faucet-task.dto';
import { FlashcardTaskData } from './flashcard/flashcard-task.dto';
import { FormAnswerTaskDataItems } from './form/tasks/form-answer/form-answer-task.dto';
import { LinkTaskData } from './link/link-task.dto';
import { LuckydrawTaskData } from './luckydraw/luckydraw-task.dto';
import { MobileAppTaskData } from './mobile-app/mobile-app-task.dto';
import { QuizTaskData } from './quiz/quiz-task.dto';
import { RestRawTaskData } from './rest/rest-raw-task.dto';
import { SubgraphRawTaskData } from './subgraph/subgraph-raw-task.dto';
import { SubsocialCommentTaskData } from './subsocial/tasks/subsocial-comment/subsocial-comment-task.dto';
import { SubsocialFollowTaskData } from './subsocial/tasks/subsocial-follow/subsocial-follow-task.dto';
import { SubsocialPostTaskData } from './subsocial/tasks/subsocial-post/subsocial-post-task.dto';
import { SubsocialShareTaskData } from './subsocial/tasks/subsocial-share/subsocial-share-task.dto';
import { SubsocialUpvoteTaskData } from './subsocial/tasks/subsocial-upvote/subsocial-upvote-task.dto';
import { SubstrateQueryTaskData } from './substrate/tasks/substrate-query-task.dto';
import { TelegramJoinTaskData } from './telegram/tasks/telegram-join/telegram-join-task.dto';
import { SignTermsTaskData } from './terms/terms-task.dto';
import { TwitterFollowTaskData } from './twitter/tasks/twitter-follow/twitter-follow-task.dto';
import { TwitterLikeTaskData } from './twitter/tasks/twitter-like/twitter-like-task.dto';
import { TwitterPostTaskData } from './twitter/tasks/twitter-post/twitter-post-task.dto';
import { TwitterRetweetTaskData } from './twitter/tasks/twitter-retweet/twitter-retweet-task.dto';
import { TwitterUgcTaskData } from './twitter/tasks/twitter-ugc/twitter-ugc-task.dto';
import { TwitterWhitelistTaskData } from './twitter/tasks/twitter-whitelist/twitter-whitelist-task.dto';
import { UploadTaskData } from './upload/upload-task.dto';
import { WalletAddressTaskData } from './wallet/wallet-address-task.dto';
import { SecretCodeTaskData } from './secret-code/secret-code-task.dto';
import { ProducthuntUpvoteTaskData } from './producthunt/tasks/upvote/producthunt-upvote-task.dto';
import { ProducthuntFollowTaskData } from './producthunt/tasks/follow/producthunt-follow-task.dto';
import { KickstarterTaskData } from './kickstarter/kickstarter-task.dto';
import { BlogCommentTaskData } from './blog/tasks/comment/blog-comment-task.dto';
export const TaskInfoUnion = createUnionType({
  name: 'TaskInfoUnion',
  types: () =>
    [
      AirquestFollowTaskData,
      AirboostReferralTaskData,
      UploadTaskData,
      LinkTaskData,
      TelegramJoinTaskData,
      QuizTaskData,
      DiscordJoinTaskData,
      FormAnswerTaskDataItems,
      EvmContractInteractTaskData,
      SubstrateQueryTaskData,
      TwitterFollowTaskData,
      TwitterLikeTaskData,
      TwitterPostTaskData,
      TwitterRetweetTaskData,
      TwitterUgcTaskData,
      TwitterLikeRetweetTaskData,
      TwitterWhitelistTaskData,
      SubsocialCommentTaskData,
      SubsocialFollowTaskData,
      SubsocialPostTaskData,
      SubsocialShareTaskData,
      SubsocialUpvoteTaskData,
      WalletAddressTaskData,
      SubgraphRawTaskData,
      RestRawTaskData,
      EmailWhitelistTaskData,
      SignTermsTaskData,
      FaucetRawTaskData,
      CustomTaskData,
      LuckydrawTaskData,
      MobileAppTaskData,
      ProducthuntUpvoteTaskData,
      ProducthuntFollowTaskData,
      SecretCodeTaskData,
      BlogCommentTaskData,
      NullableTaskData,
      KickstarterTaskData,
      FlashcardTaskData,
    ] as const,
  resolveType: (value: any) => {
    if (value.appKey && value.taskKey && !value.isPublicResolver) {
      return CustomTaskData;
    }

    if (value.taskType === TaskType.SECRET_CODE_VALIDATE && value.isPublicResolver) {
      return NullableTaskData;
    }

    switch (value.taskType) {
      case TaskType.AIRQUEST_FOLLOW:
        return AirquestFollowTaskData;
      case TaskType.AIRBOOST_REFERRAL:
        return AirboostReferralTaskData;
      case TaskType.UPLOAD_FILE:
        return UploadTaskData;
      case TaskType.DISCORD_JOIN:
        return DiscordJoinTaskData;
      case TaskType.FORM_ANSWER:
        return FormAnswerTaskDataItems;
      case TaskType.QUIZ_PLAY:
        return QuizTaskData;
      case TaskType.TELEGRAM_JOIN:
        return TelegramJoinTaskData;
      case TaskType.WALLET_DOTSAMA:
      case TaskType.WALLET_EVM:
        return WalletAddressTaskData;
      case TaskType.URL_SHARE:
      case TaskType.URL_VIEW:
      case TaskType.URL_VISIT:
      case TaskType.INSTAGRAM_SHARE:
      case TaskType.INSTAGRAM_VIEW:
      case TaskType.INSTAGRAM_VISIT:
      case TaskType.YOUTUBE_SHARE:
      case TaskType.YOUTUBE_VIEW:
      case TaskType.YOUTUBE_VISIT:
        return LinkTaskData;
      case TaskType.EVM_CONTRACT:
        return EvmContractInteractTaskData;
      case TaskType.TWITTER_FOLLOW:
        return TwitterFollowTaskData;
      case TaskType.TWITTER_LIKE:
        return TwitterLikeTaskData;
      case TaskType.TWITTER_LIKE_RETWEET:
        return TwitterLikeRetweetTaskData;
      case TaskType.TWITTER_POST:
        return TwitterPostTaskData;
      case TaskType.TWITTER_RETWEET:
        return TwitterRetweetTaskData;
      case TaskType.TWITTER_UGC:
        return TwitterUgcTaskData;
      case TaskType.TWITTER_WHITELIST:
        return TwitterWhitelistTaskData;
      case TaskType.SUBGRAPH_RAW:
        return SubgraphRawTaskData;
      case TaskType.SUBSOCIAL_COMMENT:
        return SubsocialCommentTaskData;
      case TaskType.SUBSOCIAL_FOLLOW:
        return SubsocialFollowTaskData;
      case TaskType.SUBSOCIAL_POST:
        return SubsocialPostTaskData;
      case TaskType.SUBSOCIAL_SHARE:
        return SubsocialShareTaskData;
      case TaskType.SUBSOCIAL_UPVOTE:
        return SubsocialUpvoteTaskData;
      case TaskType.REST_RAW:
      case TaskType.REST_EVM:
      case TaskType.REST_DOTSAMA:
        return RestRawTaskData;
      case TaskType.TERMS_TEXT:
      case TaskType.TERMS_EVM:
      case TaskType.TERMS_DOTSAMA:
        return SignTermsTaskData;
      case TaskType.CHECKIN_DAILY:
      case TaskType.SUBSOCIAL_PROFILE:
      case TaskType.SUBSOCIAL_SPACE:
      case TaskType.BLOG_WRITE:
        return NullableTaskData;
      case TaskType.EMAIL_WHITELIST:
        return EmailWhitelistTaskData;
      case TaskType.FAUCET_DOTSAMA:
      case TaskType.FAUCET_EVM:
        return FaucetRawTaskData;
      case TaskType.SUBSTRATE_QUERY:
      case TaskType.SUBSTRATE_ASSET_BALANCE:
      case TaskType.SUBSTRATE_BALANCE:
      case TaskType.SUBSTRATE_HOLD_NFT:
      case TaskType.SUBSTRATE_STAKE:
        return SubstrateQueryTaskData;
      case TaskType.LUCKYDRAW_PLAY:
      case TaskType.LUCKYDRAW_SLOT:
      case TaskType.LUCKYDRAW_BOX:
        return LuckydrawTaskData;
      case TaskType.MOBILE_APP_INSTALL:
        return MobileAppTaskData;
      case TaskType.SECRET_CODE_VALIDATE:
        return SecretCodeTaskData;
      case TaskType.PRODUCTHUNT_UPVOTE:
        return ProducthuntUpvoteTaskData;
      case TaskType.BLOG_COMMENT:
        return BlogCommentTaskData;
      case TaskType.PRODUCTHUNT_FOLLOW:
        return ProducthuntFollowTaskData;
      case TaskType.KICKSTARTER_SUPPORT:
        return KickstarterTaskData;
      case TaskType.FLASHCARD_VIEW:
        return FlashcardTaskData;
      default:
        return NullableTaskData;
    }
  },
});
