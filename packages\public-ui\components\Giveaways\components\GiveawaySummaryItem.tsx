import { Button } from '@Components/ui/button';
import { Check } from '@phosphor-icons/react';
import Image from 'next/image';

export default function GiveawaySummaryItem({
  banner,
  children,
  bannerTag,
  onClick,
  size = 'default',
  actionText,
  className,
  actionSuccess,
}: {
  children: React.ReactNode;
  banner: string;
  actionText?: string;
  bannerTag?: React.ReactNode;
  onClick?: () => void;
  size?: 'small' | 'default' | 'large';
  className?: string;
  actionSuccess?: boolean;
}) {
  const isVideo =
    banner?.endsWith('.mp4') ||
    banner?.endsWith('.webm') ||
    banner?.endsWith('.mov');

  const isGif = banner?.endsWith('.gif');

  if (size === 'small' || size == 'default') {
    return (
      <div
        className={`relative gap-4 grid grid-cols-[120px_1fr] items-center ${
          size === 'default' ? 'sm:grid-cols-[170px_1fr]' : ''
        } ${className || ''}`}
      >
        <div className="relative">
          {isVideo ? (
            <video
              autoPlay
              playsInline
              loop
              muted
              className="object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]"
            >
              <source src={banner} type="video/mp4" />
            </video>
          ) : (
            <Image
              height={200}
              width={200}
              src={banner}
              className={`object-cover aspect-square rounded-xl absolute top-0 left-0 w-full h-full blur-[100px]`}
              alt="giveaway-image"
              unoptimized={isGif}
            />
          )}

          <div className="relative z-10 max-w-sm m-auto">
            {isVideo ? (
              <video
                autoPlay
                playsInline
                loop
                muted
                className="rounded-xl w-full object-cover"
              >
                <source src={banner} type="video/mp4" />
              </video>
            ) : (
              <Image
                height={200}
                width={200}
                src={banner}
                className="object-cover w-full aspect-square rounded-xl"
                alt="giveaway-image"
                loading="lazy"
                unoptimized={isGif}
              />
            )}
            <div className="absolute bottom-1 left-0 w-full flex justify-center">
              {bannerTag}
            </div>
          </div>
        </div>
        <div className="relative z-20 gap-2 flex flex-col">
          <>{children}</>
          {actionText && (
            <div
              className="text-sm flex items-center gap-1 text-blue-500 font-medium cursor-pointer"
              onClick={onClick}
            >
              {actionSuccess && <Check weight="bold" size={15} />}
              <span className="line-clamp-1 break-all">{actionText}</span>
              <span aria-hidden="true">→</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="relative gap-3">
      <div className="relative">
        <div
          className={`absolute transform-gpu -z-10 rounded-3xl w-full blur-3xl`}
        >
          {isVideo ? (
            <video
              autoPlay
              playsInline
              loop
              muted
              className="rounded-xl w-full object-cover"
            >
              <source src={banner} type="video/mp4" />
            </video>
          ) : (
            <Image
              height={400}
              width={400}
              src={
                banner ||
                'https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80'
              }
              className={`object-cover w-full aspect-square rounded-xl`}
              alt="giveaway-image"
              loading="lazy"
              unoptimized={isGif}
            />
          )}
        </div>
        <div className="pb-2 relative z-10">
          {isVideo ? (
            <video
              autoPlay
              playsInline
              loop
              muted
              className="rounded-xl w-full object-cover"
            >
              <source src={banner} type="video/mp4" />
            </video>
          ) : (
            <Image
              height={400}
              width={400}
              src={
                banner ||
                'https://images.unsplash.com/photo-1655720408861-8b04c0724fd9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1932&q=80'
              }
              className={`object-cover aspect-square w-full rounded-xl`}
              alt="giveaway-image"
              loading="lazy"
              unoptimized={isGif}
            />
          )}
          <div className="absolute top-2 right-2">{bannerTag}</div>
        </div>
      </div>
      <div className="relative z-20 space-y-2">
        {children}
        <div>
          <Button
            size="sm"
            rounded="full"
            className="!font-semibold mt-2"
            block
            onClick={onClick}
          >
            <div className="flex gap-1 items-center">
              {actionSuccess && <Check weight="bold" size={16} />}
              <span className="line-clamp-1 break-all">{actionText}</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
}
