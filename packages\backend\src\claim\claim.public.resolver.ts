import {
  Args,
  ID,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { PaginationInput } from '@root/core/dto/pagination.dto';
import { Giveaway } from '@root/giveaway/entities/giveaway.entity';
import { GiveawayInfoUnion } from '@root/giveaways/giveaway-info.union';
import { ClaimService } from './claim.service';
import { EventRewardsResponse } from './event-reward.dto';
import { EventReward } from './event-reward.entity';

@Resolver((of) => EventReward)
export class ClaimPublicResolver {
  constructor(private readonly claimService: ClaimService) {}

  @Query((returns) => EventRewardsResponse)
  userRewards(
    @Args('pagination') pagination: PaginationInput,
    @Args('userId', { type: () => ID }) userId: string,
  ) {
    return this.claimService.getUserRewards(userId, pagination);
  }

  @ResolveField((returns) => GiveawayInfoUnion, { name: 'info' })
  giveawayData(@Parent() giveaway: Giveaway) {
    if (!giveaway.data) return;
    const { giveawayType, data, airPool, airToken } = giveaway;
    return {
      ...(Array.isArray(data) ? { items: data } : data),
      giveawayType,
      airToken: airToken?.token,
      airPool: airPool?.blockchainPool,
    };
  }
}
