import React from 'react';
import { Button, Form } from 'antd';
import { FlashcardSection } from '@airlyft/types';
import FlashcardDraggableItem from './FlashcardDraggableItem';

interface ExtendedFlashcardSection extends FlashcardSection {
  hidden?: boolean;
}

interface FlashcardDraggableListProps {
  sections: ExtendedFlashcardSection[];
  onSectionsChange: (sections: ExtendedFlashcardSection[]) => void;
  setFormComponentVisible: (visible: boolean) => void;
  updateSelectedItem: (item: ExtendedFlashcardSection | undefined) => void;
  disabled?: boolean;
  isReadonly?: boolean;
  dbSections?: ExtendedFlashcardSection[];
}

const FlashcardDraggableList: React.FC<FlashcardDraggableListProps> = ({
  sections,
  onSectionsChange,
  disabled,
  isReadonly,
  setFormComponentVisible,
  updateSelectedItem,
  dbSections,
}) => {
  const handleOrderChange = (items: ExtendedFlashcardSection[]) => {
    if (!items) return;
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index,
    }));
    onSectionsChange(updatedItems);
  };

  const handleRemove = (removedItem: ExtendedFlashcardSection) => {
    onSectionsChange(sections.filter((item) => item.id !== removedItem.id));
    updateSelectedItem(undefined);
    setFormComponentVisible(false);
  };

  const onHiddenToggle = (item: ExtendedFlashcardSection, status: boolean) => {
    onSectionsChange(
      sections.map((i) => {
        if (i.id === item.id) return { ...i, hidden: !status };
        else return i;
      }),
    );
  };

  return (
    <>
      <Form name="dummy" layout="vertical" disabled={true}>
        <Form.Item label="Flashcard Sections" style={{ marginBottom: 10 }}>
          <FlashcardDraggableItem
            items={sections}
            disabled={disabled}
            onEdit={(selectedItem: ExtendedFlashcardSection) => {
              updateSelectedItem(selectedItem);
              setFormComponentVisible(true);
            }}
            onRemove={handleRemove}
            onOrderChange={handleOrderChange}
            onHiddenToggle={onHiddenToggle}
            dbItems={dbSections}
          />
        </Form.Item>
      </Form>
      {!isReadonly && (
        <Button
          type="primary"
          size="large"
          block
          ghost
          shape="round"
          onClick={() => {
            updateSelectedItem(undefined);
            setFormComponentVisible(true);
          }}
          disabled={disabled}
          style={{ marginBottom: 20 }}
        >
          Add new section
        </Button>
      )}
    </>
  );
};

export default FlashcardDraggableList;
