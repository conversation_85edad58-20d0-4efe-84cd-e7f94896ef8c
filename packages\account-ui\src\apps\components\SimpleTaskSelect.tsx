import { AppType } from '@airlyft/types';
import { useGetTasks } from '@Pages/events/hooks/useGetTasks';
import { useAppSelector } from '@Root/store.hooks';
import { Select, Space } from 'antd';
import { selectApps } from '../app-store.helper';
import AppStoreIconRenderer from './AppStoreIconRenderer';
import { isHierarchicalTask } from './TaskSelect';
import { AppContext } from '@Root/states/AppContext';
import { useContext } from 'react';

/**
 * Only allows to select non-hierarchical tasks
 */
export default function SimpleTaskSelect({
  projectEventId,
  disabled,
  onChange,
  value,
  excludeIds,
  disabledIds,
  mode,
  size,
}: {
  projectEventId: string;
  disabled?: boolean;
  onChange?: (value: string[]) => void;
  value?: string[];
  excludeIds?: string[];
  disabledIds?: string[];
  mode?: 'single' | 'multiple';
  size?: 'large' | 'middle' | 'small';
}) {
  const {
    state: { projectId },
  } = useContext(AppContext);

  const { data: taskData, loading: taskLoading } = useGetTasks(
    projectEventId as string,
    projectId as string,
  );

  const apps = useAppSelector(selectApps);
  const appColor = (appType: AppType) =>
    apps.find((app) => app.appType === appType)?.config?.color;

  const tasks = taskData?.tasks || [];

  return (
    <Select
      mode={mode === 'single' ? undefined : 'multiple'}
      size={size}
      placeholder="Please select task"
      onChange={onChange}
      loading={taskLoading}
      style={{ width: '100%' }}
      value={
        Array.isArray(value)
          ? value?.filter(
              (item) => tasks.findIndex((task) => task.id == item) >= 0,
            )
          : value
      }
    >
      {tasks
        .filter(
          (t) =>
            !excludeIds?.includes(t.id) &&
            !isHierarchicalTask(t.taskType) &&
            !t.hidden,
        )
        .map((item, key) => (
          <Select.Option
            key={key}
            value={item.id}
            disabled={disabledIds?.includes(item.id)}
          >
            <Space>
              {item.iconUrl ? (
                <div
                  style={{
                    width: 16,
                    height: 16,
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <img
                    src={item.iconUrl}
                    alt="Custom Task Icon"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '4px',
                    }}
                  />
                </div>
              ) : (
                <AppStoreIconRenderer
                  iconKey={item.appType}
                  style={{ color: appColor(item.appType) }}
                />
              )}
              <div
                style={{
                  width: '220px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {item.title}
              </div>
            </Space>
          </Select.Option>
        ))}
    </Select>
  );
}
