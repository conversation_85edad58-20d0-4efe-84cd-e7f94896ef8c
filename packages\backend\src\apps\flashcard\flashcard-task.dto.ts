import {
  Field,
  InputType,
  Int,
  ObjectType,
  OmitType,
  PartialType,
  registerEnumType,
} from '@nestjs/graphql';
import { BaseTaskData } from '@root/task/base-task-data.entity';
import { TaskInput, TaskUpdateInput } from '@root/task/task.dto';

export enum FlashcardContentType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
}

registerEnumType(FlashcardContentType, { name: 'FlashcardContentType' });

@InputType('FlashcardSectionInput')
@ObjectType()
export class FlashcardSection {
  @Field()
  id: string;

  @Field((type) => Int)
  order: number;

  @Field({ nullable: true })
  content: string;
}

@InputType('FlashcardTaskDataInput')
@ObjectType()
export class FlashcardTaskData extends BaseTaskData {
  @Field((type) => [FlashcardSection], { nullable: true })
  sections: FlashcardSection[];
}

@InputType()
export class FlashcardTaskInput extends TaskInput<FlashcardTaskData> {
  @Field((type) => FlashcardTaskData, { nullable: true })
  data: FlashcardTaskData;

  @Field()
  projectId: string;

  @Field()
  eventId: string;
}

// Update Input
@InputType()
export class FlashcardTaskDataUpdateInput extends OmitType(
  PartialType(FlashcardTaskData),
  ['createdAt', 'updatedAt'],
) {}

@InputType()
export class FlashcardTaskUpdateInput extends TaskUpdateInput<FlashcardTaskDataUpdateInput> {
  @Field((type) => FlashcardTaskDataUpdateInput, { nullable: true })
  data?: FlashcardTaskDataUpdateInput;

  @Field()
  projectId: string;

  @Field()
  eventId: string;

  @Field()
  id: string;
}

// Participation data
// Since the participation is similar to link task with no data,
// we'll just use NullableTaskData in the union
