import { CheckOutlined } from '@ant-design/icons';
import Input from '@Components/Input';
import { NotificationContext } from '@Pages/App';
import ImageDragger from '@Pages/project/components/ImageDragger';
import { UploadType } from '@Root/types';
import { Button, Space, Tabs, message } from 'antd';
import { AtomicBlockUtils, EditorState } from 'draft-js';
import { useContext, useState } from 'react';
import styled from 'styled-components';

const MediaControlWrapper = styled.div`
  padding: 8px;
  width: 400px;
`;

type MediaType = 'image' | 'video';

export const MediaStyleControls = ({
  editorState,
  onFinish,
  disabled,
  mediaType,
}: {
  editorState: EditorState;
  onFinish: (editorState: EditorState) => void;
  disabled?: boolean;
  mediaType: MediaType;
}) => {
  const [urlValue, setUrlValue] = useState('');
  const [activeTab, setActiveTab] = useState('url');
  const [selectedFile, setSelectedFile] = useState('');
  const { api } = useContext(NotificationContext);

  const confirmUrl = () => {
    if (!urlValue.trim()) {
      message.error('Please enter a valid URL');
      return;
    }

    insertMedia(urlValue);
  };

  const insertMedia = (src: string) => {
    const contentState = editorState.getCurrentContent();
    const contentStateWithEntity = contentState.createEntity(
      mediaType.toUpperCase(),
      'IMMUTABLE',
      { src, mediaType },
    );
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();

    const newEditorState = EditorState.set(editorState, {
      currentContent: contentStateWithEntity,
    });

    const editorStateWithMedia = AtomicBlockUtils.insertAtomicBlock(
      newEditorState,
      entityKey,
      ' ',
    );

    setUrlValue('');
    setSelectedFile('');
    onFinish(editorStateWithMedia);
  };

  const handleFileUpload = (url: string) => {
    if (url) {
      insertMedia(url);
    }
  };

  const tabItems = [
    {
      key: 'url',
      label: 'URL',
      children: (
        <Space.Compact block size="large">
          <Input
            placeholder={`Enter ${mediaType} URL...`}
            onChange={(e) => setUrlValue(e.target.value)}
            value={urlValue}
            onPressEnter={confirmUrl}
            disabled={disabled}
          />
          <Button
            disabled={!urlValue.trim() || disabled}
            onClick={confirmUrl}
            type="primary"
            icon={<CheckOutlined />}
          />
        </Space.Compact>
      ),
    },
    {
      key: 'upload',
      label: 'Upload',
      children: (
        <ImageDragger
          updateSelectedFile={handleFileUpload}
          handleRemove={() => setSelectedFile('')}
          selectedFile={selectedFile}
          handleDelete={() => setSelectedFile('')}
          disabled={disabled}
          uploadType={UploadType.RICH_TEXT_MEDIA}
          acceptVideo={mediaType === 'video'}
          hideHint={false}
          hint={`Upload ${mediaType}`}
          editor={false}
        />
      ),
    },
  ];

  return (
    <MediaControlWrapper>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="small"
      />
    </MediaControlWrapper>
  );
};
