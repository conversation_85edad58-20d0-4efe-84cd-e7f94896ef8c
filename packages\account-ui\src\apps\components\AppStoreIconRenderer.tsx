import {
  AppType,
  AssetType,
  IntegrationPlatform,
  TaskType,
  Web3WalletType,
} from '@airlyft/types';
import Icon, {
  ApartmentOutlined,
  ApiTwoTone,
  AuditOutlined,
  BookOutlined,
  CheckSquareOutlined,
  CloudUploadOutlined,
  CodeFilled,
  CodeOutlined,
  CommentOutlined,
  EditOutlined,
  FileDoneOutlined,
  FileImageOutlined,
  FormOutlined,
  FunnelPlotOutlined,
  GoldOutlined,
  HeartOutlined,
  KeyOutlined,
  LikeOutlined,
  LinkOutlined,
  LoginOutlined,
  MailOutlined,
  MobileOutlined,
  OrderedListOutlined,
  PictureOutlined,
  QuestionCircleFilled,
  RetweetOutlined,
  SendOutlined,
  ShareAltOutlined,
  SkinOutlined,
  SmileOutlined,
  TagsOutlined,
  UpOutlined,
  // TwitterOutlined,
  UsergroupAddOutlined,
  UserOutlined,
  WalletOutlined,
  ReadOutlined,
} from '@ant-design/icons';
import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { ReactComponent as DiscordSvg } from '@Components/Icons/discord.svg';
import { ReactComponent as DotsamaSvg } from '@Components/Icons/dotsama.svg';
import { ReactComponent as EthereumSvg } from '@Components/Icons/ethereum.svg';
import { ReactComponent as GraphSvg } from '@Components/Icons/graph.svg';
import { ReactComponent as InstagramSvg } from '@Components/Icons/instagram.svg';
import { ReactComponent as LuckydrawIcon } from '@Components/Icons/luckydraw.svg';
import { ReactComponent as MetamaskSvg } from '@Components/Icons/metamask.svg';
import { ReactComponent as NovaSvg } from '@Components/Icons/nova.svg';
import { ReactComponent as PolkadotSvg } from '@Components/Icons/polkadotjs.svg';
import { ReactComponent as ProducthuntIcon } from '@Components/Icons/producthunt.svg';
import { ReactComponent as SubQuerySvg } from '@Components/Icons/subquery.svg';
import { ReactComponent as SubsocialSvg } from '@Components/Icons/subsocial.svg';
import { ReactComponent as SubSquidSvg } from '@Components/Icons/subsquid.svg';
import { ReactComponent as SubWalletSvg } from '@Components/Icons/subwallet.svg';
import { ReactComponent as TalismanSvg } from '@Components/Icons/talisman.svg';
import { ReactComponent as TelegramSvg } from '@Components/Icons/telegram.svg';
import { ReactComponent as TwitterOutlined } from '@Components/Icons/twitter.svg';
import { ReactComponent as WalletConnectSvg } from '@Components/Icons/walletconnect.svg';
import { ReactComponent as YoutubeSvg } from '@Components/Icons/youtube.svg';
import { ReactComponent as SlotMachineSvg } from '@Components/Icons/slotmachine.svg';
import { ReactComponent as MysteryBoxSvg } from '@Components/Icons/mysterybox.svg';
import { ReactComponent as KickstarterSvg } from '@Components/Icons/kickstarter.svg';
import { ReactComponent as MailchimpSvg } from '@Components/Icons/mailchimp.svg';
import { ReactComponent as ConvertkitSvg } from '@Components/Icons/convertkit.svg';
import { ReactComponent as KlaviyoSvg } from '@Components/Icons/klaviyo.svg';
import { ReactComponent as MailerliteSvg } from '@Components/Icons/mailerlite.svg';
import { ReactComponent as MailjetSvg } from '@Components/Icons/mailjet.svg';
import { ReactComponent as SendgridSvg } from '@Components/Icons/sendgrid.svg';
import { ReactComponent as SendinblueSvg } from '@Components/Icons/brevo.svg';
import { ReactComponent as ShopifySvg } from '@Components/Icons/shopify.svg';
import { ReactComponent as ZapierSvg } from '@Components/Icons/zapier.svg';
import { ReactComponent as WebhooksSvg } from '@Components/Icons/webhooks.svg';
import { RocketLaunch, Article } from '@phosphor-icons/react';
import { GiveawayCategoryType } from '@Root/giveaways/giveaway.slice';
import { Avatar } from 'antd';
import React from 'react';

export type AppStoreIconKey =
  | AppType
  | TaskType
  | AssetType
  | keyof typeof Web3WalletType
  | GiveawayCategoryType
  | IntegrationPlatform;

const AppStoreIconMap: Partial<Record<AppStoreIconKey, React.ReactNode>> = {
  [AppType.TWITTER]: TwitterOutlined,
  [AppType.UPLOAD]: CloudUploadOutlined,
  [AppType.DISCORD]: DiscordSvg,
  [AppType.TELEGRAM]: TelegramSvg,
  [AppType.INSTAGRAM]: InstagramSvg,
  [AppType.SUBSOCIAL]: SubsocialSvg,
  [AppType.URL]: LinkOutlined,
  [AppType.QUIZ]: QuestionCircleFilled,
  [AppType.YOUTUBE]: YoutubeSvg,
  [AppType.EVM]: CodeFilled,
  [AppType.FORM]: FormOutlined,
  [AppType.WALLET]: WalletOutlined,
  [AppType.SUBGRAPH]: ApartmentOutlined,
  [AppType.REST]: ApiTwoTone,
  [AppType.AIRBOOST]: SendOutlined,
  [AppType.EMAIL]: MailOutlined,
  [AppType.TERMS]: FileDoneOutlined,
  [AppType.FAUCET]: FunnelPlotOutlined,
  [AppType.CHECKIN]: LoginOutlined,
  [AppType.AIRQUEST]: RocketLaunch,
  [AppType.SUBSTRATE]: DotsamaSvg,
  [AppType.LUCKYDRAW]: LuckydrawIcon,
  [AppType.MOBILE_APP]: MobileOutlined,
  [AppType.SECRET_CODE]: KeyOutlined,
  [AppType.PRODUCTHUNT]: ProducthuntIcon,
  [AppType.BLOG]: CommentOutlined,
  [AppType.FLASHCARD]: ReadOutlined,
  [TaskType.DISCORD_JOIN]: DiscordSvg,
  [TaskType.EVM_CONTRACT]: CodeOutlined,
  [TaskType.FORM_ANSWER]: FormOutlined,
  [TaskType.INSTAGRAM_VIEW]: PictureOutlined,
  [TaskType.INSTAGRAM_VISIT]: LinkOutlined,
  [TaskType.QUIZ_PLAY]: QuestionCircleFilled,
  [TaskType.SUBGRAPH_RAW]: GraphSvg,
  [TaskType.SUBSQUID_RAW]: SubSquidSvg,
  [TaskType.SUBQUERY_RAW]: SubQuerySvg,
  [TaskType.SUBSOCIAL_COMMENT]: CommentOutlined,
  [TaskType.SUBSOCIAL_FOLLOW]: BookOutlined,
  [TaskType.SUBSOCIAL_POST]: EditOutlined,
  [TaskType.SUBSOCIAL_PROFILE]: UserOutlined,
  [TaskType.SUBSOCIAL_SHARE]: ShareAltOutlined,
  [TaskType.SUBSOCIAL_SPACE]: BookOutlined,
  [TaskType.SUBSOCIAL_UPVOTE]: LikeOutlined,
  [TaskType.TELEGRAM_JOIN]: TelegramSvg,
  [TaskType.TWITTER_FOLLOW]: UsergroupAddOutlined,
  [TaskType.TWITTER_LIKE]: HeartOutlined,
  [TaskType.TWITTER_POST]: FormOutlined,
  [TaskType.TWITTER_RETWEET]: RetweetOutlined,
  [TaskType.TWITTER_LIKE_RETWEET]: HeartOutlined,
  [TaskType.TWITTER_UGC]: SmileOutlined,
  [TaskType.TWITTER_WHITELIST]: AuditOutlined,
  [TaskType.URL_SHARE]: ShareAltOutlined,
  [TaskType.URL_VISIT]: LinkOutlined,
  [TaskType.WALLET_EVM]: EthereumSvg,
  [TaskType.WALLET_DOTSAMA]: DotsamaSvg,
  [TaskType.YOUTUBE_SHARE]: YoutubeSvg,
  [TaskType.YOUTUBE_VIEW]: YoutubeSvg,
  [TaskType.YOUTUBE_VISIT]: YoutubeSvg,
  [TaskType.FAUCET_DOTSAMA]: DotsamaSvg,
  [TaskType.FAUCET_EVM]: EthereumSvg,
  [TaskType.AIRQUEST_FOLLOW]: RocketLaunch,
  [TaskType.CHECKIN_DAILY]: LoginOutlined,
  [TaskType.REST_RAW]: ApiTwoTone,
  [TaskType.AIRBOOST_REFERRAL]: SendOutlined,
  [TaskType.REST_EVM]: EthereumSvg,
  [TaskType.REST_DOTSAMA]: DotsamaSvg,
  [TaskType.EMAIL_ADDRESS]: AuditOutlined,
  [TaskType.EMAIL_WHITELIST]: MailOutlined,
  [TaskType.EMAIL_SUBSCRIBE]: MailOutlined,
  [TaskType.TERMS_TEXT]: CheckSquareOutlined,
  [TaskType.TERMS_EVM]: EthereumSvg,
  [TaskType.TERMS_DOTSAMA]: DotsamaSvg,
  [TaskType.SUBSTRATE_QUERY]: DotsamaSvg,
  [TaskType.SUBSTRATE_ASSET_BALANCE]: DotsamaSvg,
  [TaskType.SUBSTRATE_HOLD_NFT]: DotsamaSvg,
  [TaskType.SUBSTRATE_BALANCE]: DotsamaSvg,
  [TaskType.SUBSTRATE_STAKE]: DotsamaSvg,
  [TaskType.LUCKYDRAW_PLAY]: LuckydrawIcon,
  [TaskType.LUCKYDRAW_SLOT]: SlotMachineSvg,
  [TaskType.LUCKYDRAW_BOX]: MysteryBoxSvg,
  [TaskType.FLASHCARD_VIEW]: ReadOutlined,
  [AssetType.ERC20]: GoldOutlined,
  [AssetType.ERC721]: FileImageOutlined,
  [AssetType.ERC1155]: GoldOutlined,
  [AssetType.DOTSAMA_TOKEN]: DotsamaSvg,
  [AssetType.DOTSAMA_NFT]: DotsamaSvg,
  [Web3WalletType.EVM_METAMASK]: MetamaskSvg,
  [Web3WalletType.EVM_SUBWALLET]: SubWalletSvg,
  [Web3WalletType.EVM_TALISMAN]: TalismanSvg,
  [Web3WalletType.EVM_WALLET_CONNECT]: WalletConnectSvg,
  [Web3WalletType.EVM_MANUAL]: FormOutlined,
  [Web3WalletType.DOTSAMA_SUBWALLET]: SubWalletSvg,
  [Web3WalletType.DOTSAMA_TALISMAN]: TalismanSvg,
  [Web3WalletType.DOTSAMA_POLKADOT_JS]: PolkadotSvg,
  [Web3WalletType.DOTSAMA_NOVA]: NovaSvg,
  [GiveawayCategoryType.GIVEAWAY_ERC20]: GoldOutlined,
  [GiveawayCategoryType.GIVEAWAY_ERC1155]: GoldOutlined,
  [GiveawayCategoryType.GIVEAWAY_ERC721]: FileImageOutlined,
  [GiveawayCategoryType.GIVEAWAY_COUPON]: TagsOutlined,
  [GiveawayCategoryType.GIVEAWAY_WHITELIST]: OrderedListOutlined,
  [GiveawayCategoryType.GIVEAWAY_SECRET]: KeyOutlined,
  [GiveawayCategoryType.GIVEAWAY_MERCHANDISE]: SkinOutlined,
  [GiveawayCategoryType.GIVEAWAY_DOTSAMA_TOKEN]: DotsamaSvg,
  [GiveawayCategoryType.GIVEAWAY_DOTSAMA_NFT]: DotsamaSvg,
  [TaskType.MOBILE_APP_INSTALL]: MobileOutlined,
  [TaskType.SECRET_CODE_VALIDATE]: KeyOutlined,
  [TaskType.BLOG_COMMENT]: CommentOutlined,
  [TaskType.BLOG_WRITE]: Article,
  [TaskType.PRODUCTHUNT_UPVOTE]: UpOutlined,
  [TaskType.PRODUCTHUNT_FOLLOW]: UsergroupAddOutlined,
  [AppType.KICKSTARTER]: KickstarterSvg,
  [TaskType.KICKSTARTER_SUPPORT]: KickstarterSvg,
  [IntegrationPlatform.MAILCHIMP]: MailchimpSvg,
  [IntegrationPlatform.CONVERTKIT]: ConvertkitSvg,
  [IntegrationPlatform.KLAVIYO]: KlaviyoSvg,
  [IntegrationPlatform.MAILERLITE]: MailerliteSvg,
  [IntegrationPlatform.MAILJET]: MailjetSvg,
  [IntegrationPlatform.SENDGRID]: SendgridSvg,
  [IntegrationPlatform.SENDINBLUE]: SendinblueSvg,
  [IntegrationPlatform.SHOPIFY]: ShopifySvg,
  [IntegrationPlatform.TELEGRAM_BOT]: TelegramSvg,
  [IntegrationPlatform.ZAPIER]: ZapierSvg,
  [IntegrationPlatform.WEBHOOKS]: WebhooksSvg,
};
interface AppStoreIconProps extends Partial<CustomIconComponentProps> {
  iconKey: AppStoreIconKey;
  url?: string;
}

export default function AppStoreIconRenderer({
  iconKey,
  url,
  ...props
}: AppStoreIconProps) {
  if (url) {
    return <Avatar src={url} size={'small'} {...props} />;
  }

  const icon = AppStoreIconMap[iconKey];

  if (!icon) return <></>;

  return (
    <Icon component={icon as React.ForwardRefExoticComponent<any>} {...props} />
  );
}
