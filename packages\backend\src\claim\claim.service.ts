import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TransactionStatus } from '@root/blockchain/blockchain.constants';
import { BaseCrudService } from '@root/core/base-crud/base-crud.service';
import { EventParticipant } from '@root/event-participation/event-participant.entity';
import { DataSource, EntityManager, In, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { EventReward, RewardStatus } from './event-reward.entity';
import { PaginationInput } from '@root/core/dto/pagination.dto';

@Injectable()
export class ClaimService extends BaseCrudService<EventReward> {
  constructor(
    @InjectRepository(EventReward)
    repository: Repository<EventReward>,

    private dataSource: DataSource,
  ) {
    super(repository);
  }

  createAndBurnFuel(
    projectId: string,
    eventId: string,
    points: number,
    value: QueryDeepPartialEntity<EventReward>,
  ) {
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      this.createAndBurnFuelTx(
        projectId,
        eventId,
        points,
        value,
        transactionalEntityManager,
      );
    });
  }

  async createAndBurnFuelTx(
    projectId: string,
    eventId: string,
    points: number,
    value: QueryDeepPartialEntity<EventReward>,
    transactionalEntityManager: EntityManager,
  ) {
    const { mode, userId } = value.eventParticipant as EventParticipant;

    await transactionalEntityManager
      .createQueryBuilder()
      .insert()
      .into(EventParticipant)
      .values({
        event: { id: eventId },
        mode,
        user: { id: userId },
      })
      .orIgnore()
      .execute();

    const data = await transactionalEntityManager
      .createQueryBuilder()
      .insert()
      .into(EventReward)
      .values(value)
      .orIgnore()
      .returning(['id'])
      .execute();

    if (data.identifiers?.[0]) {
      await transactionalEntityManager.query(
        `
            UPDATE fuel
            SET amount = fuel.amount - $3
            WHERE "userId" = $1
              AND "projectId" = $2
              AND amount >= $3;
          `,
        [userId, projectId, points],
      );
    }
  }

  async createBatchTx(
    values: QueryDeepPartialEntity<EventReward>[],
    transactionalEntityManager: EntityManager,
  ) {
    await transactionalEntityManager
      .createQueryBuilder()
      .insert()
      .into(EventParticipant)
      .values(
        values.map(
          ({ eventParticipant }: { eventParticipant: EventParticipant }) => ({
            event: { id: eventParticipant.eventId },
            mode: eventParticipant.mode,
            user: { id: eventParticipant.userId },
          }),
        ),
      )
      .orIgnore()
      .execute();

    return transactionalEntityManager
      .createQueryBuilder()
      .insert()
      .into(EventReward)
      .values(values)
      .orIgnore()
      .execute();
  }

  createBatch(values: QueryDeepPartialEntity<EventReward>[]) {
    return this.dataSource.transaction((transactionalEntityManager) =>
      this.createBatchTx(values, transactionalEntityManager),
    );
  }

  updateStatusBatch(ids: Array<string>, status: TransactionStatus) {
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      await transactionalEntityManager
        .createQueryBuilder()
        .update(EventReward)
        .set({ status })
        .where({ id: In(ids) })
        .execute();
    });
  }

  updateWallet(
    giveawayId: string,
    eventId: string,
    userId: string,
    wallet: string,
  ) {
    return this.repository
      .createQueryBuilder()
      .update()
      .set({ wallet })
      .where('giveawayId = :giveawayId', { giveawayId })
      .andWhere('eventParticipant.userId = :userId', { userId })
      .andWhere('eventParticipant.eventId = :eventId', { eventId })
      .execute();
  }

  findUserRewardByStatus(
    giveawayId: string,
    userId: string,
    eventId: string,
    status: RewardStatus[],
  ) {
    return this.repository
      .createQueryBuilder('er')
      .where('er.giveawayId = :giveawayId', { giveawayId })
      .andWhere('er.eventParticipant.userId = :userId', { userId })
      .andWhere('er.eventParticipant.eventId = :eventId', { eventId })
      .andWhere('status IN (:...statusIn)', {
        statusIn: status,
      })
      .getMany();
  }

  findUserRewards(userId: string, eventId: string) {
    return this.repository
      .createQueryBuilder('er')
      .where('er.eventParticipant.userId = :userId', { userId })
      .andWhere('er.eventParticipant.eventId = :eventId', { eventId })
      .getMany();
  }

  async getUserRewards(userId: string, pagination: PaginationInput) {
    const q = this.repository
      .createQueryBuilder('er')
      .leftJoinAndSelect('er.giveaway', 'giveaway')
      .leftJoinAndSelect('giveaway.airToken', 'airToken')
      .leftJoinAndSelect('airToken.token', 'token')
      .leftJoinAndSelect('giveaway.airPool', 'airPool')
      .leftJoinAndSelect('airPool.blockchainPool', 'blockchainPool')
      .leftJoinAndSelect('blockchainPool.asset', 'asset')
      .where('er.userId = :userId', { userId })
      .andWhere('er.status = :status', {
        status: RewardStatus.SUCCESS,
      })
      .orderBy('er.createdAt', 'DESC');

    const [data, total] = await q
      .take(pagination.take)
      .skip(pagination.skip)
      .getManyAndCount();
    return { data, total };
  }
}
