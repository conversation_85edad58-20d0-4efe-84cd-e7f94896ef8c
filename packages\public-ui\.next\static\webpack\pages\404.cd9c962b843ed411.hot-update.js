"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useScrollPosition__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useScrollPosition */ \"./hooks/useScrollPosition.ts\");\n/* harmony import */ var _Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @Hooks/useUserDetails */ \"./hooks/useUserDetails.ts\");\n/* harmony import */ var _Root_config_config_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @Root/config/config.json */ \"./config/config.json\");\n/* harmony import */ var _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @Root/context/Actions */ \"./context/Actions.ts\");\n/* harmony import */ var _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @Root/context/AppContext */ \"./context/AppContext.tsx\");\n/* harmony import */ var _Root_utils_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @Root/utils/utils */ \"./utils/utils.ts\");\n/* harmony import */ var _airlyft_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @airlyft/types */ \"../types/dist/index.js\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ClientOnly__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClientOnly */ \"./components/ClientOnly.tsx\");\n/* harmony import */ var _FeatureGuard__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./FeatureGuard */ \"./components/FeatureGuard.tsx\");\n/* harmony import */ var _Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./Loaders/SpinLoader */ \"./components/Loaders/SpinLoader.tsx\");\n/* harmony import */ var _UserNotification_UserNotification__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./UserNotification/UserNotification */ \"./components/UserNotification/UserNotification.tsx\");\n/* harmony import */ var _UserProfile_UserMenu__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./UserProfile/UserMenu */ \"./components/UserProfile/UserMenu.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./ui/button */ \"./components/ui/button.tsx\");\n/* harmony import */ var _Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @Root/utils/string-utils */ \"./utils/string-utils.ts\");\n/* harmony import */ var _GlobalLeaderboard_useGlobalLeaderboard_gql__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./GlobalLeaderboard/useGlobalLeaderboard.gql */ \"./components/GlobalLeaderboard/useGlobalLeaderboard.gql.ts\");\n/* harmony import */ var _Root_helpers__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @Root/helpers */ \"./helpers/index.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ROUTES_CONFIG = _Root_config_config_json__WEBPACK_IMPORTED_MODULE_3__.root.routes;\nconst DesktopNav = (param)=>{\n    let { me, loading } = param;\n    var _leaderboardRankingData_userGlobalRanking;\n    _s();\n    const scrollPosition = (0,_Hooks_useScrollPosition__WEBPACK_IMPORTED_MODULE_1__.useScrollPosition)();\n    const { theme, setTheme, systemTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme)();\n    const { dispatch, state: { isAuthenticated } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    // Inorder to enable Geo Blocking restrictions, make the following change\n    // const isRestrictedPage = router.pathname === restrictedPage;\n    const isRestrictedPage = false;\n    const currentTheme = theme === \"system\" ? systemTheme : theme;\n    const subdomain = (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_6__.getSubdomainClient)();\n    const sorter = {\n        sortKey: _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.GlobalLeaderboardSortKey.TOTAL_XP,\n        direction: _airlyft_types__WEBPACK_IMPORTED_MODULE_7__.SortDirection.DESC\n    };\n    const where = {};\n    const { data: leaderboardRankingData } = (0,_GlobalLeaderboard_useGlobalLeaderboard_gql__WEBPACK_IMPORTED_MODULE_19__.useGlobalLeaderboardRanking)(sorter, where, !(0,_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.isFeatureEnabled)(\"HEADER_POINTS\"));\n    var _leaderboardRankingData_userGlobalRanking_totalXp;\n    const totalXp = (_leaderboardRankingData_userGlobalRanking_totalXp = leaderboardRankingData === null || leaderboardRankingData === void 0 ? void 0 : (_leaderboardRankingData_userGlobalRanking = leaderboardRankingData.userGlobalRanking) === null || _leaderboardRankingData_userGlobalRanking === void 0 ? void 0 : _leaderboardRankingData_userGlobalRanking.totalXp) !== null && _leaderboardRankingData_userGlobalRanking_totalXp !== void 0 ? _leaderboardRankingData_userGlobalRanking_totalXp : 0;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\");\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\", {\n        keyPrefix: \"global\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_Root_utils_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"hidden lg:block\", scrollPosition > 0 ? \"border-b bg-background/80  sticky top-0 z-50 block bg-opacity-70 backdrop-blur-lg\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"hidden lg:flex relative z-50 items-center justify-between p-4 lg:px-8 max-w-7xl mx-auto\",\n            \"aria-label\": \"Global\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex lg:flex-1 lg:gap-x-10 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                            href: \"/\",\n                            className: \"ml-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Airlyft One\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    className: \"h-10 w-10 rounded-full shadow-sm shadow-yellow-400\",\n                                    src: \"https://pbs.twimg.com/profile_images/1494277092590387202/BMoaZmJ5_400x400.jpg\",\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex lg:gap-x-10 text-ch text-sm font-semibold leading-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"EXPLORE\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.explore,\n                                        children: t(\"nav.explore\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"COMMUNITIES\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.communities,\n                                        children: t(\"nav.communities\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"CAMPAIGNS\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.campaigns,\n                                        children: t(\"nav.campaigns\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"GLOBAL_LEADERBOARD\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: ROUTES_CONFIG.leaderboard,\n                                        children: t(\"nav.leaderboard\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                    feature: \"POLKADOT\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        children: !subdomain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                            target: \"_blank\",\n                                            href: \"https://polkadot.airlyft.one\",\n                                            children: t(\"nav.polkadot\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientOnly__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden lg:flex lg:flex-1 lg:justify-end gap-x-6 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                feature: \"POLKADOT\",\n                                children: subdomain && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/ecosystem/\".concat(subdomain, \"-\").concat(currentTheme, \".png\"),\n                                            className: \"h-9\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-r h-6 pl-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loaders_SpinLoader__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, undefined) : isAuthenticated && me ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                        feature: \"CREATE_CAMPAIGN\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                            href: \"https://account.airlyft.one\",\n                                            target: \"_blank\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                                                rounded: \"full\",\n                                                size: \"sm\",\n                                                children: t(\"nav.create\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                        feature: \"HEADER_POINTS\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Sparkle, {\n                                                    size: 24,\n                                                    weight: \"duotone\",\n                                                    className: \"text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-ch\",\n                                                    children: [\n                                                        (0,_Root_helpers__WEBPACK_IMPORTED_MODULE_20__.formatNumber)(totalXp),\n                                                        \" \",\n                                                        (0,_Root_utils_string_utils__WEBPACK_IMPORTED_MODULE_18__.pluralize)(totalXp, globalT(\"projectXp\"), globalT(\"projectXp_many\"))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                        feature: \"NOTIFICATIONS\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserNotification_UserNotification__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            rendererType: \"popover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserProfile_UserMenu__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        me: me\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : !isRestrictedPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                                        href: \"#\",\n                                        className: \"text-sm font-semibold leading-6 text-cs\",\n                                        onClick: ()=>{\n                                            dispatch({\n                                                type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                                                payload: null\n                                            });\n                                        },\n                                        children: t(\"nav.login\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-primary font-medium rounded-full px-4 py-2 text-primary-foreground text-sm\",\n                                        onClick: ()=>{\n                                            dispatch({\n                                                type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                                                payload: null\n                                            });\n                                        },\n                                        children: t(\"nav.signup\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FeatureGuard__WEBPACK_IMPORTED_MODULE_13__.FeatureGuard, {\n                                feature: \"THEME_TOGGLE\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>currentTheme == \"dark\" ? setTheme(\"light\") : setTheme(\"dark\"),\n                                    className: \"flex items-center\",\n                                    children: currentTheme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Sun, {\n                                        size: 28,\n                                        className: \"text-ch hover:cursor-pointer\",\n                                        weight: \"duotone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Moon, {\n                                        size: 28,\n                                        className: \"text-cl hover:cursor-pointer\",\n                                        weight: \"duotone\",\n                                        onClick: ()=>setTheme(\"dark\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DesktopNav, \"ZA6gqLRh+LCjyeb8P53WuI9wKeo=\", false, function() {\n    return [\n        _Hooks_useScrollPosition__WEBPACK_IMPORTED_MODULE_1__.useScrollPosition,\n        next_themes__WEBPACK_IMPORTED_MODULE_9__.useTheme,\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        _GlobalLeaderboard_useGlobalLeaderboard_gql__WEBPACK_IMPORTED_MODULE_19__.useGlobalLeaderboardRanking,\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation,\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c = DesktopNav;\nconst MobileNavItem = (param)=>{\n    let { title, icon, active, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: \"button\",\n        className: \"inline-flex flex-col gap-1 items-center justify-center \".concat(active ? \"text-ch font-bold\" : \"text-cl\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = MobileNavItem;\nconst MobileNav = (param)=>{\n    let { me, loading } = param;\n    _s1();\n    const { dispatch, state: { isAuthenticated } } = (0,_Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    // Inorder to enable Geo Blocking restrictions, make the following change\n    // const isRestrictedPage = router.pathname === restrictedPage;\n    const isRestrictedPage = false;\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation)(\"ssr\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"lg:hidden fixed bottom-0 left-0 z-50 w-full h-16 shadow border border-t shadow-white/10 grid grid-cols-5 items-center place-content-center place-items-center bg-background/80 backdrop-blur-lg\",\n        \"aria-label\": \"Global\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                href: ROUTES_CONFIG.explore,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                    title: t(\"nav.explore\"),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Compass, {\n                        size: 22,\n                        weight: \"duotone\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 17\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserNotification_UserNotification__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                rendererType: \"modal\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_10___default()), {\n                href: \"/\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        className: \"h-12 w-12 rounded-full shadow-sm shadow-yellow-400\",\n                        src: \"https://pbs.twimg.com/profile_images/1494277092590387202/BMoaZmJ5_400x400.jpg\",\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                title: t(\"nav.profile\"),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.UserCircle, {\n                    size: 22,\n                    weight: \"duotone\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 15\n                }, void 0),\n                onClick: ()=>{\n                    var _me_auth;\n                    return isAuthenticated ? router.push(\"/user/\".concat(me === null || me === void 0 ? void 0 : (_me_auth = me.auth) === null || _me_auth === void 0 ? void 0 : _me_auth.username)) : dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNavItem, {\n                title: t(\"nav.settings\"),\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_21__.Gear, {\n                    size: 22,\n                    weight: \"duotone\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 15\n                }, void 0),\n                onClick: ()=>{\n                    if (isRestrictedPage) return;\n                    return isAuthenticated ? router.push({\n                        pathname: router.pathname,\n                        query: {\n                            ...router.query,\n                            ...{\n                                settings: \"all\"\n                            }\n                        }\n                    }, undefined, {\n                        shallow: true\n                    }) : dispatch({\n                        type: _Root_context_Actions__WEBPACK_IMPORTED_MODULE_4__.ActionTypes.TOGGLE_LOGIN_MODAL,\n                        payload: null\n                    });\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(MobileNav, \"hIwJKNGa/HltcXbA4xspnPHm/+U=\", false, function() {\n    return [\n        _Root_context_AppContext__WEBPACK_IMPORTED_MODULE_5__.useAppContext,\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        next_i18next__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c2 = MobileNav;\nfunction Header() {\n    _s2();\n    const { data, loading } = (0,_Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_2__.useUserDetails)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DesktopNav, {\n                me: data === null || data === void 0 ? void 0 : data.me,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MobileNav, {\n                me: data === null || data === void 0 ? void 0 : data.me,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\Header.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s2(Header, \"PLAvDeOs13GsWcIdcOX0j63+zYQ=\", false, function() {\n    return [\n        _Hooks_useUserDetails__WEBPACK_IMPORTED_MODULE_2__.useUserDetails\n    ];\n});\n_c3 = Header;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"DesktopNav\");\n$RefreshReg$(_c1, \"MobileNavItem\");\n$RefreshReg$(_c2, \"MobileNav\");\n$RefreshReg$(_c3, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Header.tsx\n"));

/***/ })

});