import { gql } from '@apollo/client';
import { AIR_BOOSTER_QUERY_FRAGMENT } from './Airboost/airboost.gql';
import { DISCORD_JOIN_QUERY_FRAGMENT } from './Discord/discord.gql';
import { EMAIL_WHITELIST_QUERY_FRAGMENT } from './Email/gql/email-whitelist.gql';
import { EVM_INTERACT_QUERY_FRAGMENT } from './Evm/evm.gql';
import { FAUCET_QUERY_FRAGMENT } from './Faucet/faucet.gql';
import { FLASHCARD_QUERY_FRAGMENT } from './Flashcard/flashcard.gql';
import { FORM_ANSWER_QUERY_FRAGMENT } from './Form/form.gql';
import { LINK_QUERY_FRAGMENT } from './GenericLinkTask/link.gql';
import { MOBILE_APP_QUERY_FRAGMENT } from './MobileApp/mobile-app.gql';
import {
  PRODUCTHUNT_UPVOTE_QUERY_FRAGMENT,
  PRODUCTHUNT_FOLLOW_QUERY_FRAGMENT,
} from './Producthunt/producthunt.gql';
import { QUIZ_TASK_DATA_QUERY_FRAGMENT } from './Quiz/quiz.gql';
import { REST_RAW_QUERY_FRAGMENT } from './Rest/rest.gql';
import { SUBGRAPH_RAW_QUERY_FRAGMENT } from './Subgraph/subgraph.gql';
import { SUBSOCIAL_COMMENT_QUERY_FRAGMENT } from './Subsocial/gql/subsocial-comment.gql';
import { SUBSOCIAL_FOLLOW_QUERY_FRAGMENT } from './Subsocial/gql/subsocial-follow.gql';
import { SUBSOCIAL_POST_QUERY_FRAGMENT } from './Subsocial/gql/subsocial-post.gql';
import { SUBSOCIAL_SHARE_QUERY_FRAGMENT } from './Subsocial/gql/subsocial-share.gql';
import { SUBSOCIAL_UPVOTE_QUERY_FRAGMENT } from './Subsocial/gql/subsocial-upvote.gql';
import { TELEGRAM_JOIN_QUERY_FRAGMENT } from './Telegram/telegram.gql';
import { SIGN_TERMS_QUERY_FRAGMENT } from './Terms/terms.gql';
import { TWITTER_FOLLOW_QUERY_FRAGMENT } from './Twitter/gql/twitter-follow.gql';
import { TWITTER_LIKE_QUERY_FRAGMENT } from './Twitter/gql/twitter-like.gql';
import { TWITTER_POST_QUERY_FRAGMENT } from './Twitter/gql/twitter-post.gql';
import { TWITTER_RETWEET_QUERY_FRAGMENT } from './Twitter/gql/twitter-retweet.gql';
import { TWITTER_UGC_QUERY_FRAGMENT } from './Twitter/gql/twitter-ugc.gql';
import { UPLOAD_QUERY_FRAGMENT } from './Upload/upload.gql';
import { WALLET_ADDRESS_QUERY_FRAGMENT } from './Wallet/wallet.gql';
import { CUSTOM_TASK_QUERY_FRAGMENT } from './CustomApp/custom-app.gql';
import { TWITTER_LIKE_RETWEET_QUERY_FRAGMENT } from './Twitter/gql/twitter-like-retweet.gql';
import { AIRQUEST_FOLLOW_QUERY_FRAGMENT } from './Airquest/airquest.gql';
import { SUBSTRATE_QUERY_QUERY_FRAGMENT } from './Substrate/substrate.gql';
import { LUCKYDRAW_QUERY_FRAGMENT } from './Luckydraw/luckydraw.gql';
import { TWITTER_WHITELIST_QUERY_FRAGMENT } from './Twitter/gql/twitter-whitelist.gql';
import { SECRET_CODE_QUERY_FRAGMENT } from './SecretCode/secret-code.gql';
import { KICKSTARTER_QUERY_FRAGMENT } from './Kickstarter/kickstarter.gql';
import { BLOG_COMMENT_QUERY_FRAGMENT } from './Blog/gql/blog-comment.gql';

const NULLABLE_QUERY_FRAGMENT = gql`
  fragment NullableDataFragment on NullableTaskData {
    isNull
  }
`;

export const TASK_INFO_FRAGMENT = gql`
  ${DISCORD_JOIN_QUERY_FRAGMENT}
  ${TELEGRAM_JOIN_QUERY_FRAGMENT}
  ${LINK_QUERY_FRAGMENT}
  ${EVM_INTERACT_QUERY_FRAGMENT}
  ${SUBSTRATE_QUERY_QUERY_FRAGMENT}
  ${FORM_ANSWER_QUERY_FRAGMENT}
  ${UPLOAD_QUERY_FRAGMENT}
  ${TWITTER_FOLLOW_QUERY_FRAGMENT}
  ${TWITTER_LIKE_QUERY_FRAGMENT}
  ${TWITTER_LIKE_RETWEET_QUERY_FRAGMENT}
  ${TWITTER_POST_QUERY_FRAGMENT}
  ${TWITTER_RETWEET_QUERY_FRAGMENT}
  ${TWITTER_UGC_QUERY_FRAGMENT}
  ${TWITTER_WHITELIST_QUERY_FRAGMENT}
  ${SUBSOCIAL_FOLLOW_QUERY_FRAGMENT}
  ${SUBSOCIAL_POST_QUERY_FRAGMENT}
  ${SUBSOCIAL_COMMENT_QUERY_FRAGMENT}
  ${SUBSOCIAL_SHARE_QUERY_FRAGMENT}
  ${SUBSOCIAL_UPVOTE_QUERY_FRAGMENT}
  ${NULLABLE_QUERY_FRAGMENT}
  ${QUIZ_TASK_DATA_QUERY_FRAGMENT}
  ${WALLET_ADDRESS_QUERY_FRAGMENT}
  ${SUBGRAPH_RAW_QUERY_FRAGMENT}
  ${REST_RAW_QUERY_FRAGMENT}
  ${AIR_BOOSTER_QUERY_FRAGMENT}
  ${EMAIL_WHITELIST_QUERY_FRAGMENT}
  ${SIGN_TERMS_QUERY_FRAGMENT}
  ${FAUCET_QUERY_FRAGMENT}
  ${CUSTOM_TASK_QUERY_FRAGMENT}
  ${AIRQUEST_FOLLOW_QUERY_FRAGMENT}
  ${LUCKYDRAW_QUERY_FRAGMENT}
  ${MOBILE_APP_QUERY_FRAGMENT}
  ${SECRET_CODE_QUERY_FRAGMENT}
  ${PRODUCTHUNT_UPVOTE_QUERY_FRAGMENT}
  ${BLOG_COMMENT_QUERY_FRAGMENT}
  ${PRODUCTHUNT_FOLLOW_QUERY_FRAGMENT}
  ${KICKSTARTER_QUERY_FRAGMENT}
  ${FLASHCARD_QUERY_FRAGMENT}
  fragment TaskInfo on Task {
    info {
      ...DiscordJoinTaskDataFragment
      ...TelegramJoinTaskDataFragment
      ...LinkTaskDataFragment
      ...EvmContractInteractTaskDataFragment
      ...SubstrateQueryTaskDataFragment
      ...FormAnswerTaskDataFragment
      ...UploadTaskDataFragment
      ...TwitterFollowTaskDataFragment
      ...TwitterLikeTaskDataFragment
      ...TwitterLikeRetweetTaskDataFragment
      ...TwitterPostTaskDataFragment
      ...TwitterRetweetTaskDataFragment
      ...TwitterUgcTaskDataFragment
      ...TwitterWhitelistTaskDataFragment
      ...SubsocialFollowTaskDataFragment
      ...SubsocialPostTaskDataFragment
      ...SubsocialCommentTaskDataFragment
      ...SubsocialShareTaskDataFragment
      ...SubsocialUpvoteTaskDataFragment
      ...NullableDataFragment
      ...QuizTaskDataFragment
      ...WalletAddressTaskDataFragment
      ...SubgraphRawTaskDataFragment
      ...RestRawTaskDataFragment
      ...AirboostReferralTaskDataFragment
      ...EmailWhitelistTaskDataFragment
      ...SignTermsTaskDataFragment
      ...FaucetRawTaskDataFragment
      ...CustomTaskDataFragment
      ...AirquestFollowTaskDataFragment
      ...LuckydrawTaskDataFragment
      ...MobileAppTaskDataFragment
      ...SecretCodeTaskDataFragment
      ...ProducthuntUpvoteTaskDataFragment
      ...BlogCommentTaskDataFragment
      ...ProducthuntFollowTaskDataFragment
      ...KickstarterTaskDataFragment
      ...FlashcardTaskDataFragment
    }
  }
`;
