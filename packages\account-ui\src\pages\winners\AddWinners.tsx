import { useEntityFilterSorterPage } from '@Hooks/useEntityFilterSorterPage';
import { NotificationContext } from '@Pages/App';
import { isUUID } from '@Root/utils/stringUtils';
import {
  AirPoolGiveawayData,
  AirTokenGiveawayData,
  Giveaway,
  GiveawayType,
  InputMaybe,
  WinnerInput,
} from '@airlyft/types';
import { UnorderedListOutlined } from '@ant-design/icons';
import { Spin, Typography, Upload } from 'antd';
import { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ThemeContext } from 'styled-components';
import { winnersEntityFilterSorter } from './WinnerTable';
import { useAddWinners } from './hooks/useAddWinners';
import { GET_WINNERS } from './hooks/useGetWinners';
import { GET_GIVEAWAYS } from '@Root/giveaways/hooks/useGetGiveaways';

const { Dragger } = Upload;

export default function AddWinners({
  projectId,
  projectEventId,
  giveaway,
  onCompleted,
}: {
  projectEventId: string;
  projectId: string;
  giveaway: Giveaway;
  onCompleted?: () => void;
}) {
  const { t } = useTranslation();
  const { t: globalT } = useTranslation('translation', {
    keyPrefix: 'general',
  });
  const { api } = useContext(NotificationContext);
  const theme = useContext(ThemeContext);
  const [parsing, setParsing] = useState(false);

  const giveawayAmount = (): { amount?: InputMaybe<string> | undefined } => {
    if (!giveaway) {
      return {};
    }
    switch (giveaway.giveawayType) {
      case GiveawayType.ERC1155_AIR_POOL:
      case GiveawayType.ERC721_AIR_POOL:
      case GiveawayType.ERC20_AIR_POOL:
      case GiveawayType.DOTSAMA_NFT_AIR_POOL:
      case GiveawayType.DOTSAMA_TOKEN_AIR_POOL:
        return {
          amount: (giveaway.info as AirPoolGiveawayData).winnerAmount,
        };
      case GiveawayType.ERC1155_AIR_TOKEN:
      case GiveawayType.ERC721_AIR_TOKEN:
      case GiveawayType.ERC20_AIR_TOKEN:
      case GiveawayType.DOTSAMA_NFT_AIR_TOKEN:
      case GiveawayType.DOTSAMA_TOKEN_AIR_TOKEN:
        return {
          amount: (giveaway.info as AirTokenGiveawayData).winnerAmount,
        };
      default:
        return {};
    }
  };

  const { reset, pagination } = useEntityFilterSorterPage(
    winnersEntityFilterSorter(projectEventId),
  );

  const [addWinners, { loading }] = useAddWinners();

  const handleFileUpload = async (file: File) => {
    setParsing(true);
    try {
      const content = await readFileAsync(file);
      const lines = content
        .split(/[\n\r]/g)
        .map((line) => {
          const [userId] = line.split(',');
          if (isUUID(userId)) {
            return { userId, ...giveawayAmount() } as WinnerInput;
          }
          return null;
        })
        .filter((data) => !!data);

      if (lines.length === 0) {
        api?.error({
          message: t('winners.manual-selection.upload.dragger.no-data-error'),
        });
        return;
      }

      addWinners({
        variables: {
          eventId: projectEventId,
          giveawayId: giveaway.id,
          projectId,
          data: lines as WinnerInput[],
        },
        refetchQueries: [
          {
            query: GET_WINNERS,
            variables: {
              projectId,
              pagination,
              where: {
                eventId: projectEventId,
                giveawayId: giveaway.id,
              },
            },
          },
          {
            query: GET_GIVEAWAYS,
            variables: {
              eventId: projectEventId,
            },
          },
        ],
        onCompleted: (data) => {
          let isAtleastOneSuccess = false;
          data?.addWinners?.identifiers?.forEach((identifier) => {
            if (identifier) {
              isAtleastOneSuccess = true;
            }
          });

          if (isAtleastOneSuccess) {
            api?.success({
              message: t('winners.manual-selection.upload.success'),
            });
          } else {
            api?.error({
              message: t(
                'winners.manual-selection.upload.dragger.no-new-winners',
              ),
            });
          }
          reset({ filters: true, sorters: true, page: true });
          onCompleted?.();
        },
      });
    } catch (e: any) {
      api?.error({ message: e.message });
    } finally {
      setParsing(false);
    }
  };

  const readFileAsync = (file: File) => {
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = () =>
        reject(
          new Error(
            t('winners.manual-selection.upload.dragger.invalid-file') ||
              'invalid-file',
          ),
        );
      reader.readAsText(file);
    });
  };

  return (
    <Spin
      spinning={!!(parsing || loading)}
      tip={
        <Typography.Title level={5} style={{ color: theme.colors.primary }}>
          Uploading
        </Typography.Title>
      }
      size="large"
    >
      <Dragger
        name="file"
        beforeUpload={(file) => {
          handleFileUpload(file);
          return Upload.LIST_IGNORE;
        }}
        accept="text/csv, application/vnd.ms-excel, text/plain"
      >
        <p className="ant-upload-drag-icon">
          <UnorderedListOutlined />
        </p>
        <p className="ant-upload-text">
          {t('winners.manual-selection.upload.dragger.title')}
        </p>
        <p className="ant-upload-hint">
          {t('winners.manual-selection.upload.dragger.subtitle', {
            platform: globalT('platform'),
          })}
        </p>
      </Dragger>
    </Spin>
  );
}
