import { useGetTasks } from '@Pages/events/hooks/useGetTasks';
import { useAppSelector } from '@Root/store.hooks';
import { AppType, Task, TaskType } from '@airlyft/types';
import { Select, Space, Typography } from 'antd';
import { useContext, useMemo } from 'react';
import { selectApps } from '../app-store.helper';
import AppStoreIconRenderer from './AppStoreIconRenderer';
import { AppContext } from '@Root/states/AppContext';

export const OTHER_TASKS = 'OTHER_TASKS';
export const HierarchicalTask = [TaskType.QUIZ_PLAY];

export const isHierarchicalTask = (taskType: TaskType) =>
  HierarchicalTask.includes(taskType);

export function useLabelSelectMap(tasks: Task[], showHidden: boolean) {
  return useMemo(
    () =>
      tasks.reduce((acc, task) => {
        if (task.hidden && !showHidden) {
          return acc;
        }

        if (!isHierarchicalTask(task.taskType) && !acc.has(OTHER_TASKS)) {
          acc.set(OTHER_TASKS, [task]);
          return acc;
        }

        if (!isHierarchicalTask(task.taskType) && acc.has(OTHER_TASKS)) {
          acc.set(OTHER_TASKS, [...acc.get(OTHER_TASKS), task]);
          return acc;
        }

        if (!task.parentId) {
          return acc;
        }

        if (!acc.has(task.parentId)) {
          acc.set(task.parentId, [task]);
          return acc;
        }

        acc.set(task.parentId, [...acc.get(task.parentId), task]);
        return acc;
      }, new Map()),
    [tasks, showHidden],
  );
}

export default function TaskSelect({
  projectEventId,
  disabled,
  onChange,
  value,
  mode = 'multiple',
  size = 'large',
  showHidden = false,
  allowClear = false,
}: {
  projectEventId: string;
  disabled?: boolean;
  onChange?: (value: string | string[]) => void;
  value?: string | string[];
  mode?: 'single' | 'multiple';
  size?: 'large' | 'middle' | 'small';
  showHidden?: boolean;
  allowClear?: boolean;
}) {
  const {
    state: { projectId },
  } = useContext(AppContext);

  const { data: taskData, loading: taskLoading } = useGetTasks(
    projectEventId as string,
    projectId as string,
  );

  const apps = useAppSelector(selectApps);
  const appColor = (appType: AppType) =>
    apps.find((app) => app.appType === appType)?.config?.color;

  const tasks = taskData?.tasks || [];

  const labelOptionMap = useLabelSelectMap(tasks, showHidden);

  return (
    <Select
      mode={mode === 'single' ? undefined : 'multiple'}
      size={size}
      placeholder="Please select tasks"
      onChange={onChange}
      loading={taskLoading}
      style={{ width: '100%' }}
      allowClear={allowClear}
      value={
        Array.isArray(value)
          ? value?.filter(
              (item) => tasks.findIndex((task) => task.id == item) >= 0,
            )
          : value
      }
    >
      {labelOptionMap.size == 1 && !!labelOptionMap.get(OTHER_TASKS)
        ? labelOptionMap.get(OTHER_TASKS).map((item: Task, key: number) => (
            <Select.Option key={key} value={item.id}>
              <Space>
                {item.iconUrl ? (
                  <div
                    style={{
                      width: 16,
                      height: 16,
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <img
                      src={item.iconUrl}
                      alt="Custom Task Icon"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover',
                        borderRadius: '4px',
                      }}
                    />
                  </div>
                ) : (
                  <AppStoreIconRenderer
                    iconKey={item.appType}
                    style={{ color: appColor(item.appType) }}
                  />
                )}
                <Typography.Text
                  ellipsis={true}
                  style={{
                    maxWidth: 200,
                    display: 'block',
                  }}
                >
                  {item.title}
                </Typography.Text>
              </Space>
            </Select.Option>
          ))
        : Array.from(labelOptionMap.keys())?.map((key, index) => (
            <Select.OptGroup
              key={key}
              label={
                key == OTHER_TASKS
                  ? 'Other Tasks'
                  : tasks.find((i) => i.id === key)?.title
              }
            >
              {labelOptionMap.get(key).map((item: Task, key1: number) => (
                <Select.Option key={item.id} value={item.id}>
                  <Space align="center">
                    {item.iconUrl ? (
                      <div
                        style={{
                          width: 16,
                          height: 16,
                          position: 'relative',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <img
                          src={item.iconUrl}
                          alt="Custom Task Icon"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover',
                            borderRadius: '4px',
                          }}
                        />
                      </div>
                    ) : (
                      <AppStoreIconRenderer
                        iconKey={item.appType}
                        style={{ color: appColor(item.appType) }}
                      />
                    )}
                    <Typography.Text
                      ellipsis={true}
                      style={{
                        maxWidth: 200,
                        display: 'block',
                      }}
                    >
                      {item.title}
                    </Typography.Text>
                  </Space>
                </Select.Option>
              ))}
            </Select.OptGroup>
          ))}
    </Select>
  );
}
