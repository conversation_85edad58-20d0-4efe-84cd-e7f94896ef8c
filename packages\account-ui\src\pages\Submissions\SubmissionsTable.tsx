import ExportTable from '@Components/ExportTable';
import Table, { TableWrapper } from '@Components/Table';
import { useEntityFilterSorterPage } from '@Hooks/useEntityFilterSorterPage';
import { useGetParentTasks } from '@Pages/events/hooks/useGetTasks';
import AppStoreIconRenderer from '@Root/apps/components/AppStoreIconRenderer';
import { PAGE_SIZE } from '@Root/constants';
import { AppContext } from '@Root/states/AppContext';
import {
  AccountParticipationStatus,
  AppType,
  Auth,
  EventState,
  ParticipationStatus,
  ProjectEvent,
  Task,
  TaskParticipation,
} from '@airlyft/types';
import { Button, Pagination, Popconfirm, Select, Space } from 'antd';
import moment from 'moment';
import { Fragment, useContext, useState } from 'react';
import { useParams } from 'react-router-dom';
import ImagePreview from './ImagePreview';

import usePageTotal from '@Hooks/usePageTotal';
import ParticipantFilter from '@Pages/participants/ParticipantFilter';
import { selectApps } from '@Root/apps/app-store.helper';
import { useAppSelector } from '@Root/store.hooks';
import { DeleteOutlined, FilterFilled } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import TaskParticipationViewRenderer, {
  isSubmissionRowExpandable,
} from './TaskCellRenderer/TaskParticipationViewRenderer';
import UserDetailsView from './UserDetailsView';
import { useDeleteTaskParticipation } from './hooks/useDeleteTaskParticipation';
import { useDownloadSubmissions } from './hooks/useDownloadSubmissions';
import { useEventSubmissions } from './hooks/useEventSubmissions';
import { useUpdateTaskParticipationStatus } from './hooks/useUpdateTaskParticipationStatus';
import { isFeatureEnabled } from '@Components/FeatureGuard';

export const SUBMISSION_KEY_PREFIX = 'sb_';

const { Option } = Select;

export default function SubmissionsTable({
  event,
  loading,
}: {
  event: ProjectEvent | undefined;
  loading?: boolean;
}) {
  const { t } = useTranslation();
  const {
    state: { projectId },
  } = useContext(AppContext);

  const { eventId, userId } = useParams();
  const entityFilterSorterPageId =
    SUBMISSION_KEY_PREFIX + eventId! + (userId ? userId : '');

  const [expandedKey, setExpandedKey] = useState<string | undefined>();

  const {
    filters,
    initialized,
    page,
    update,
    addFilter,
    pagination,
    formattedFilters,
  } = useEntityFilterSorterPage(entityFilterSorterPageId);

  const where = {
    ...(formattedFilters || {}),
    eventId: eventId!,
    userId: userId || formattedFilters.userId,
  };

  const { data: eventSubmission, loading: isSubmissionLoading } =
    useEventSubmissions(projectId, where, pagination, !initialized);

  const { total } = usePageTotal({
    skip: !eventSubmission?.eventSubmissions,
    total: eventSubmission?.eventSubmissions?.total || 0,
  });

  const { updateTaskParticipationStatus, isStatusUpdatingById } =
    useUpdateTaskParticipationStatus(projectId, where, pagination);

  const { deleteTaskParticipation, isDeletingById } =
    useDeleteTaskParticipation(projectId, where, pagination);

  const { download, loading: downloading } = useDownloadSubmissions({
    where,
    projectId,
    fileName: event?.publicLink,
    includeUser: !!userId,
  });

  const [imagePreviewState, setImagePreviewState] = useState<{
    images: string[];
    current: number;
    visible: boolean;
  }>({ images: [], current: 0, visible: false });

  const { data: taskData, loading: taskLoading } = useGetParentTasks(
    eventId as string,
    projectId as string,
  );

  const isLoading = isSubmissionLoading || loading || taskLoading;

  const apps = useAppSelector(selectApps);
  const appColor = (appType: AppType) =>
    apps.find((app) => app.appType === appType)?.config?.color;

  const columns: any[] = [
    {
      title: 'Participated with',
      dataIndex: ['id'],
      key: 'connection',
      width: 220,
      render: (id: string, record: TaskParticipation) => {
        const auth =
          record.auth?.provider && record.auth?.providerId
            ? record.auth
            : (record.primaryAuth as Auth);

        return (
          <Space>
            <UserDetailsView userId={record.userId} eventConnections={[auth]} />
            {!userId && (
              <Button
                icon={<FilterFilled />}
                size="small"
                type="link"
                onClick={() => {
                  addFilter({
                    key: 'userId',
                    t: 'string',
                    value: record.userId,
                  });
                }}
              />
            )}
          </Space>
        );
      },
    },
    {
      title: 'XP',
      dataIndex: ['xp'],
      key: 'xp',
      width: 55,
      ellipsis: true,
      hidden: !isFeatureEnabled('XP'),
    },
    {
      title: t(`fuel.title`),
      dataIndex: ['points'],
      key: 'points',
      width: 55,
      ellipsis: true,
      hidden: !isFeatureEnabled('POINTS'),
    },
    {
      title: 'Quest',
      dataIndex: ['task'],
      key: 'task',
      width: 160,
      render: (task: Task) => (
        <Space size={'small'}>
          <AppStoreIconRenderer
            style={{ color: appColor(task.appType) }}
            iconKey={task.appType}
          />{' '}
          <div
            title={task.title || ''}
            style={{
              width: 160,
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
            }}
          >
            {task.title}{' '}
          </div>
        </Space>
      ),
    },
    Table.EXPAND_COLUMN,
    {
      title: 'Details',
      dataIndex: ['task'],
      key: 'details',
      width: 180,
      render: (task: Task, record: TaskParticipation) => {
        return (
          <TaskParticipationViewRenderer
            taskType={task.taskType}
            submission={record}
            tasks={taskData || []}
            size="summary"
            onMediaClick={(url: string, index: number, urls: string[]) => {
              setImagePreviewState({
                images: urls,
                current: index,
                visible: true,
              });
            }}
            onClick={() => setExpandedKey(expandedKey ? undefined : record.id)}
          />
        );
      },
    },
    Table.SELECTION_COLUMN,
    {
      title: 'When',
      dataIndex: ['createdAt'],
      key: 'createdAt',
      width: 130,
      ellipsis: true,
      render: (createdAt: Date) =>
        moment(createdAt).format('YYYY-MM-DD HH:mm a'),
    },
    {
      title: 'Status',
      dataIndex: ['status'],
      key: 'status',
      width: 150,
      fixed: 'right',
      render: (status: ParticipationStatus, record: TaskParticipation) => {
        const isLoading =
          isStatusUpdatingById(record.id) || isDeletingById(record.id);
        return (
          <Select
            defaultValue={status}
            style={{ width: 120 }}
            onChange={(value) =>
              updateTaskParticipationStatus(
                record.id,
                value as unknown as AccountParticipationStatus,
              )
            }
            disabled={isLoading || event?.state === EventState.SETTLED}
            loading={isLoading}
          >
            {status === ParticipationStatus.IN_REVIEW && (
              <Option disabled value={ParticipationStatus.IN_REVIEW}>
                Review
              </Option>
            )}
            {status === ParticipationStatus.IN_AI_VERIFICATION && (
              <Option disabled value={ParticipationStatus.IN_AI_VERIFICATION}>
                AI Review
              </Option>
            )}
            <Option value={AccountParticipationStatus.VALID}>Valid</Option>
            <Option value={AccountParticipationStatus.INVALID}>Invalid</Option>
          </Select>
        );
      },
    },
    {
      dataIndex: ['id'],
      key: 'actions',
      width: 20,
      fixed: 'right',
      render: (id: string, record: TaskParticipation) => {
        const isLoading = isStatusUpdatingById(record.id);
        return (
          <Popconfirm
            onConfirm={() => {
              deleteTaskParticipation(id);
            }}
            title={'Delete this submission forever?'}
          >
            <Button
              shape="circle"
              disabled={isLoading}
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <>
      <ImagePreview
        visible={imagePreviewState.visible}
        current={imagePreviewState.current}
        images={imagePreviewState.images}
        onVisibleChange={(isVisible) => {
          let newState = {
            ...imagePreviewState,
            visible: true,
          };

          if (!isVisible) {
            newState = {
              ...newState,
              images: [],
              current: 0,
              visible: false,
            };
          }

          setImagePreviewState(newState);
        }}
      />

      <TableWrapper
        offset={100}
        header={
          <Fragment>
            <div>
              <ParticipantFilter
                projectEventId={eventId!}
                projectId={projectId}
                id={entityFilterSorterPageId}
                isSubmissions={true}
              />
            </div>

            <ExportTable
              isFilteredData={filters.length > 0}
              onClick={download}
              isLoading={downloading}
            />
          </Fragment>
        }
        skeletonProps={{
          loading: isLoading,
          active: isLoading,
          size: 'small',
          columns,
          rowCount: PAGE_SIZE,
        }}
        footer={
          <Fragment>
            <div>Total {total}</div>
            <Pagination
              onChange={(page) => {
                update({ page });
              }}
              total={total}
              current={page}
              pageSize={PAGE_SIZE}
              showSizeChanger={false}
            />
          </Fragment>
        }
      >
        <Table
          columns={columns}
          dataSource={eventSubmission?.eventSubmissions?.data || []}
          size="small"
          pagination={false}
          loading={loading}
          expandable={{
            expandedRowKeys: expandedKey ? [expandedKey] : [],
            expandedRowRender: (record) => {
              const data = record as TaskParticipation;
              const task = data?.task;
              if (!task) return <Fragment />;
              return (
                <TaskParticipationViewRenderer
                  taskType={task.taskType}
                  submission={data}
                  tasks={taskData || []}
                  onMediaClick={(
                    url: string,
                    index: number,
                    urls: string[],
                  ) => {
                    setImagePreviewState({
                      images: urls,
                      current: index,
                      visible: true,
                    });
                  }}
                  size="details"
                />
              );
            },
            expandIcon: ({ expanded, record }) => {
              const data = record as TaskParticipation;

              const isExpandable = isSubmissionRowExpandable(
                data.task?.taskType,
              );

              if (!isExpandable) return <Fragment />;

              return (
                <Button
                  className={`ant-table-row-expand-icon ant-table-row-expand-icon-${
                    expanded ? 'expanded' : 'collapsed'
                  }`}
                  onClick={(e) =>
                    setExpandedKey(expandedKey ? undefined : data.id)
                  }
                ></Button>
              );
            },
          }}
          rowKey={(record: any) => record.id}
          scroll={{
            y: '800px',
            x: 'max-content',
          }}
        />
      </TableWrapper>
    </>
  );
}
