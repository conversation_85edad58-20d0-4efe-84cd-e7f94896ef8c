{"name": "@airlyft/public-ui", "version": "2.0.12", "private": true, "scripts": {"dev": "next dev -p 3030", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc"}, "dependencies": {"@airlyft/web3-evm": "^1.0.0", "@airlyft/web3-evm-hooks": "^1.0.0", "@apollo/client": "^3.6.9", "@ethersproject/abstract-signer": "^5.5.0", "@ethersproject/bignumber": "^5.5.0", "@ethersproject/experimental": "^5.5.0", "@ethersproject/providers": "^5.5.3", "@ethersproject/units": "^5.5.0", "@headlessui/react": "^1.7.15", "@heroicons/react": "^1.0.6", "@next/third-parties": "^15.3.1", "@phosphor-icons/react": "^2.0.8", "@polkadot/extension-dapp": "^0.47.5", "@polkadot/util-crypto": "^12.6.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@sentry/nextjs": "^8.15.0", "@tailwindcss/typography": "^0.5.7", "@tippyjs/react": "^4.2.6", "@types/canvas-confetti": "^1.9.0", "@vercel/og": "^0.5.6", "axios": "^0.24.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "cookie": "^0.4.2", "draft-js": "^0.11.7", "eslint-config-next": "^14.2.4", "ethers": "^5.5.4", "formik": "^2.2.9", "graphql": "^16.6.0", "graphql-ws": "^5.16.0", "i18next": "^23.16.8", "i18next-http-backend": "^3.0.1", "moment": "^2.29.1", "next": "^14.2.25", "next-i18next": "^15.3.1", "next-themes": "^0.2.1", "nprogress": "^0.2.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-content-loader": "^6.1.0", "react-device-detect": "^2.2.2", "react-dom": "^18.3.1", "react-google-recaptcha-v3": "^1.10.1", "react-i18next": "^15.1.1", "react-share": "^4.4.1", "react-toastify": "^9.1.3", "recharts": "^2.1.14", "sharp": "^0.32.1", "swiper": "^9.1.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "telegram-login-button": "^0.0.3", "zustand": "^4.1.1"}, "devDependencies": {"@airlyft/types": "^1.0.0", "@types/cookie": "^0.5.1", "@types/draft-js": "^0.11.9", "@types/lodash": "^4.14.181", "@types/node": "16.11.12", "@types/nprogress": "^0.2.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.0", "eslint": "8.4.1", "postcss": "^8.4.4", "prettier": "^2.5.1", "tailwindcss": "^3.4.3", "typescript": "^5.7.2"}}