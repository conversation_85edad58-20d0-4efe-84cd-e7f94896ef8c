import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Auth } from '@root/auth/auth.entity';
import { ClaimService } from '@root/claim/claim.service';
import { EventReward, RewardStatus } from '@root/claim/event-reward.entity';
import { PaginationInput } from '@root/core/dto/pagination.dto';
import { SortInput } from '@root/core/dto/sort-input.dto';
import {
  Giveaway,
  GiveawayReviewStatus,
} from '@root/giveaway/entities/giveaway.entity';
import { User } from '@root/user/user.entity';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { WinnerInput, WinnerWhereInput, WinnersData } from './winner.dto';
import { GiveawayType } from '@root/giveaway/giveaway.constants';

@Injectable()
export class WinnerService {
  constructor(
    @InjectRepository(EventReward)
    private repository: Repository<EventReward>,

    private claimService: ClaimService,

    private dataSource: DataSource,
  ) {}

  getWinnersQb(where: WinnerWhereInput) {
    const qb = this.repository
      .createQueryBuilder('er')
      .select([
        'er."createdAt"',
        'er."id"',
        'er."ruleId"',
        'er."data"',
        'er."eventId"',
        'er."userId"',
        'er."giveawayId"',
        'er."status"',
        'er."updatedAt"',
      ])
      .leftJoin(User, 'u', 'er."userId" = u.id')
      .leftJoin(
        Auth,
        'auth',
        'er."userId" = auth."userId" AND auth."isPrimary" = true',
      )
      .addSelect('u."firstName"', 'fname')
      .addSelect('u."lastName"', 'lname')
      .addSelect([
        'auth."providerId"',
        'auth."firstName"',
        'auth."lastName"',
        'auth."picture"',
        'auth."username"',
        'auth."provider"',
      ])
      .andWhere('er.eventId = :eventId', {
        eventId: where.eventId,
      })
      .andWhere('er.giveawayId = :giveawayId', {
        giveawayId: where.giveawayId,
      });

    if (where.status?.length > 0) {
      qb.andWhere('er.status IN (:...status)', {
        status: where.status,
      });
    }

    if (where.date?.start && where.date?.end) {
      qb.andWhere('er.updatedAt BETWEEN :start AND :end', {
        start: where.date.start,
        end: where.date.end,
      });
    }

    return qb;
  }

  async getWinners(
    pagination: PaginationInput,
    where: WinnerWhereInput,
    sorter?: SortInput,
  ): Promise<WinnersData> {
    const [winnersQuery, params] =
      this.getWinnersQb(where).getQueryAndParameters();

    const winnersData = await this.dataSource.query(
      `WITH cte AS (
          ${winnersQuery}
        )
        SELECT * FROM (
          SELECT * FROM cte
          ORDER BY "updatedAt" DESC, id ASC
          OFFSET ${pagination.skip} LIMIT ${pagination.take}
        ) result
        RIGHT JOIN (
          SELECT COUNT(*)::numeric FROM cte
        ) c(total) ON TRUE`,
      params,
    );

    const total = winnersData?.[0]?.total || 0;

    const data =
      total === 0
        ? []
        : (winnersData || []).map(
            ({
              fname,
              lname,
              providerId,
              email,
              picture,
              username,
              provider,
              userId,
              data,
              ...rest
            }) => ({
              ...rest,
              amount: data?.amount,
              displayName: `${fname} ${lname}`,
              primaryAuth: {
                providerId,
                email,
                picture,
                username,
                provider,
                userId,
              },
              formAnswers: data?.answers,
              burnPoints: data?.burnPoints,
            }),
          );

    return {
      data,
      total,
    };
  }

  async deleteOne(
    projectId: string,
    eventId: string,
    giveawayId: string,
    rewardId: string,
  ) {
    return this.repository
      .createQueryBuilder()
      .delete()
      .where('eventId = :eventId', { eventId })
      .andWhere('giveawayId = :giveawayId', { giveawayId })
      .andWhere('id = :rewardId', { rewardId })
      .andWhere('mode = :mode', { mode: 1 })
      .andWhere('status = :status', { status: 'DRAFT' })
      .execute();
  }

  async clearAll(projectId: string, eventId: string, giveawayId: string) {
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const [raw, affected] = await transactionalEntityManager.query(
        `
          DELETE FROM event_rewards
          WHERE "eventId" = $1 
            AND "giveawayId" = $2
            AND mode = $3
            AND status = 'DRAFT'
        `,
        [eventId, giveawayId, 1],
      );

      await this.updateGiveawayReviewStatusTx(
        projectId,
        giveawayId,
        GiveawayReviewStatus.NONE,
        transactionalEntityManager,
      );

      return affected;
    });
  }

  async createBulk(
    projectId: string,
    eventId: string,
    giveaway: Giveaway,
    data: Array<WinnerInput>,
  ) {
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const batchResult = await this.claimService.createBatchTx(
        data.map((item) => ({
          claimed: false,
          eventParticipant: {
            mode: 1,
            eventId,
            userId: item.userId,
          },
          status: RewardStatus.DRAFT,
          giveawayId: giveaway.id,
          data: {
            ...(item.amount ? { amount: item.amount } : {}),
          },
          updatedAt: new Date(),
          ...(giveaway.lastSettledTime
            ? { settledTime: giveaway.lastSettledTime }
            : {}),
        })),
        transactionalEntityManager,
      );
      const affectedRows = batchResult?.raw?.length ?? 0;

      const totalWinnersCount = await transactionalEntityManager
        .getRepository(EventReward)
        .createQueryBuilder('reward')
        .where('reward.giveawayId = :giveawayId', { giveawayId: giveaway.id })
        .getCount();

      if (totalWinnersCount > giveaway.winnerCount) {
        throw new BadRequestException(
          `You can select up to ${giveaway.winnerCount} winners.`,
        );
      }

      if (affectedRows > 0) {
        await this.updateGiveawayReviewStatusTx(
          projectId,
          giveaway.id,
          GiveawayReviewStatus.IN_REVIEW,
          transactionalEntityManager,
        );
      }
      return batchResult;
    });
  }

  async settleGiveaway(
    projectId: string,
    eventId: string,
    giveaway: Giveaway,
  ): Promise<boolean> {
    const rewardStatus =
      giveaway.giveawayType === GiveawayType.WHITELIST
        ? RewardStatus.SUCCESS
        : RewardStatus.ISSUED;
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const now = giveaway.lastSettledTime ?? new Date();
      await transactionalEntityManager.query(
        `
        UPDATE event_rewards
        SET status = $4,
           "updatedAt" = NOW(),
           "settledTime" = $5
        WHERE "eventId" = $1
          AND "giveawayId" = $2
          AND mode = $3
          AND status = 'DRAFT';
      `,
        [eventId, giveaway.id, 1, rewardStatus, now],
      );

      await transactionalEntityManager.query(
        `
        UPDATE giveaways
        SET "lastSettledTime" = $3,
           "latestReviewStatus" = 'REVIEWED'
        WHERE id = $1 AND "projectId" = $2
      `,
        [giveaway.id, projectId, now],
      );

      return true;
    });
  }

  private updateGiveawayReviewStatusTx(
    projectId: string,
    giveawayId: string,
    status: GiveawayReviewStatus,
    transactionalEntityManager: EntityManager,
  ) {
    return transactionalEntityManager.query(
      `
        UPDATE giveaways
        SET "latestReviewStatus" = $3
        WHERE id = $1 AND "projectId" = $2
      `,
      [giveawayId, projectId, status],
    );
  }
}
