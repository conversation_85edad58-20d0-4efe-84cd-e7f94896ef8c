{"c": ["webpack"], "r": ["pages/404", "pages/[project]", "components_Giveaways_AirPoolGiveaway_AirPoolDotsamaGiveawayClaim_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolEVMGiveawayClaim_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolFcfsRangeDetails_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolFcfsRangeSummary_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolFcfsTaskIdDetails_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolFcfsTaskIdSummary_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolGiveawayClaim_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolManualDetails_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolManualSummary_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolRandomDetails_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolRandomSummary_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolShopDetails_tsx", "components_Giveaways_AirPoolGiveaway_AirPoolShopSummary_tsx", "components_Giveaways_AirPoolGiveaway_useAirPoolGiveawayClaim_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenDotsamaGiveawayClaim_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenEVMGiveawayClaim_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenFcfsRangeDetails_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenFcfsRangeSummary_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenFcfsTaskIdDetails_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenFcfsTaskIdSummary_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenGiveawayClaim_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenManualDetails_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenManualSummary_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenRandomDetails_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenRandomSummary_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenShopDetails_tsx", "components_Giveaways_AirTokenGiveaway_AirTokenShopSummary_tsx", "components_Giveaways_AirTokenGiveaway_useAirTokenGiveawayClaim_tsx", "components_Giveaways_Distribution_TokenFCFSRangeDistribution_tsx", "components_Giveaways_Distribution_TokenFCFSTaskIdDistribution_tsx", "components_Giveaways_Distribution_TokenManualDistribution_tsx", "components_Giveaways_Distribution_TokenRandomDistribution_tsx", "components_Giveaways_GiveawayRendererWrapper_tsx", "components_Giveaways_GiveawayShopList_tsx", "components_Giveaways_GiveawaySummaryList_tsx", "components_Giveaways_GiveawayTransactionHash_tsx", "components_Giveaways_MerchandiseGiveaway_MerchandiseGiveawayClaim_tsx", "components_Giveaways_MerchandiseGiveaway_MerchandiseShopDetails_tsx", "components_Giveaways_MerchandiseGiveaway_MerchandiseShopSummary_tsx", "components_Giveaways_WhitelistGiveaway_WhitelistManualDetails_tsx", "components_Giveaways_WhitelistGiveaway_WhitelistManualSummary_tsx", "components_Giveaways_WhitelistGiveaway_WhitelistRandomDetails_tsx", "components_Giveaways_WhitelistGiveaway_WhitelistRandomSummary_tsx", "components_Giveaways_WhitelistGiveaway_components_WhitelistGiveawayDetails_tsx", "components_Giveaways_WhitelistGiveaway_components_WhitelistGiveawaySummary_tsx", "components_Giveaways_components_GiveawayFCFSRangeTag_tsx", "components_Giveaways_components_GiveawaySummaryItem_tsx", "components_Giveaways_components_ShopGiveawayDetails_tsx", "components_Giveaways_components_ShopGiveawaySummaryItem_tsx", "components_Giveaways_components_TokenAddress_tsx", "components_Giveaways_components_TokenFCFSUserProgress_tsx", "components_Giveaways_components_TokenGiveawayClaimStats_tsx", "components_Giveaways_components_TokenGiveawayDetails_tsx", "components_Giveaways_components_TokenGiveawayHeader_tsx", "components_Giveaways_components_TokenGiveawaySummaryItem_tsx", "components_Giveaways_hooks_useGetContract_ts", "components_Giveaways_hooks_useGetPoolWindowInfo_ts", "components_Giveaways_hooks_useGetTokenWindowInfo_ts", "components_Tasks_Provider_DotsamaProvider_DotsamaProvider_tsx", "components_Tasks_Provider_DotsamaProvider_useDotsamaConnect_ts", "components_Tasks_Provider_EmailProvider_EmailProvider_tsx", "components_Tasks_Provider_EvmProvider_EvmProvider_tsx", "components_Tasks_Provider_EvmProvider_useEvmConnect_ts", "components_Tasks_Provider_OauthProvider_OauthProvider_tsx", "components_Tasks_Provider_ProviderButton_tsx", "components_Tasks_Provider_ProviderUseConnection_tsx", "components_Tasks_Provider_TelegramProvider_TelegramProvider_tsx", "components_Tasks_Airboost_AirboostReferralBody_tsx", "components_Tasks_Airboost_AirboostReferralHeader_tsx", "components_Tasks_Airboost_AirboostReferralTile_tsx", "components_Tasks_Airquest_AirquestFollowBody_tsx", "components_Tasks_Blog_Comment_BlogCommentBody_tsx", "components_Tasks_Blog_Write_BlogWriteBody_tsx", "components_Tasks_Discord_DiscordJoinBody_tsx", "components_Tasks_Email_Address_EmailAddressBody_tsx", "components_Tasks_Email_Address_email-address_gql_ts", "components_Tasks_Email_Subscribe_EmailSubscribeBody_tsx", "components_Tasks_Email_Subscribe_email-subscribe_gql_ts", "components_Tasks_Email_Whitelist_EmailWhitelistBody_tsx", "components_Tasks_Evm_EvmContractBody_tsx", "components_Tasks_Evm_EvmInstructionsCard_tsx", "components_Tasks_Evm_FormItemRenderer_tsx", "components_Tasks_Faucet_BaseFaucetBody_tsx", "components_Tasks_Faucet_Dot_FaucetDotsamaBody_tsx", "components_Tasks_Faucet_Evm_FaucetEvmBody_tsx", "components_Tasks_Form_FormAnswerBody_tsx", "components_Tasks_Form_FormAnswerForm_tsx", "components_Tasks_Form_FormItemRenderer_tsx", "components_Tasks_Instagram_View_InstagramViewBody_tsx", "components_Tasks_Instagram_Visit_InstagramVisitBody_tsx", "components_Tasks_Kickstarter_KickstarterSupportBody_tsx", "components_Tasks_Luckydraw_LuckydrawBoxBody_tsx", "components_Tasks_Luckydraw_LuckydrawPlayBody_tsx", "components_Tasks_Luckydraw_LuckydrawSlotBody_tsx", "components_Tasks_MobileApp_MobileAppInstallBody_tsx", "components_Tasks_Producthunt_Follow_ProducthuntFollowBody_tsx", "components_Tasks_Producthunt_Upvote_ProducthuntUpvoteBody_tsx", "components_Tasks_Quiz_QuizPlayBody_tsx", "components_Tasks_Quiz_QuizPlayBodyRenderer_tsx", "components_Tasks_Quiz_QuizPlayHeader_tsx", "components_Tasks_Quiz_QuizPlayTile_tsx", "components_Tasks_Quiz_components_Option_tsx", "components_Tasks_Quiz_components_OptionList_tsx", "components_Tasks_Quiz_components_Progress_tsx", "components_Tasks_Quiz_components_Question_tsx", "components_Tasks_Quiz_components_useQuizData_ts", "components_Tasks_Rest_BaseRestBody_tsx", "components_Tasks_Rest_RestDotsamaBody_tsx", "components_Tasks_Rest_RestEvmBody_tsx", "components_Tasks_Rest_RestRawBody_tsx", "components_Tasks_Rest_WalletAddress_tsx", "components_Tasks_SecretCode_SecretCodeValidateBody_tsx", "components_Tasks_SecretCode_secret-code_gql_ts", "components_Tasks_Subgraph_SubgraphRawBody_tsx", "components_Tasks_Subgraph_WalletAddress_tsx", "components_Tasks_Subsocial_SubsocialCommentBody_tsx", "components_Tasks_Subsocial_SubsocialFollowBody_tsx", "components_Tasks_Subsocial_SubsocialPostActionBody_tsx", "components_Tasks_Subsocial_SubsocialPostBody_tsx", "components_Tasks_Subsocial_SubsocialProfileBody_tsx", "components_Tasks_Subsocial_SubsocialShareBody_tsx", "components_Tasks_Subsocial_SubsocialSpaceBody_tsx", "components_Tasks_Subsocial_SubsocialUpvoteBody_tsx", "components_Tasks_Subsocial_gql_subsocial-profile_gql_ts", "components_Tasks_Subsocial_gql_subsocial-space_gql_ts", "components_Tasks_Substrate_SubstrateQueryBody_tsx", "components_Tasks_TaskSummary_tsx", "components_Tasks_Telegram_TelegramJoinBody_tsx", "components_Tasks_Terms_TermsDotsamaBody_tsx", "components_Tasks_Terms_TermsTextBody_tsx", "components_Tasks_Twitter_Follow_TwitterFollowBody_tsx", "components_Tasks_Twitter_Like_TwitterLikeBody_tsx", "components_Tasks_Twitter_LikeRetweet_TwitterLikeRetweetBody_tsx", "components_Tasks_Twitter_Post_TwitterPostBody_tsx", "components_Tasks_Twitter_Retweet_TwitterRetweetBody_tsx", "components_Tasks_Twitter_TweetIntentButton_tsx", "components_Tasks_Twitter_Ugc_TwitterUgcBody_tsx", "components_Tasks_Twitter_Whitelist_TwitterWhitelistBody_tsx", "components_Tasks_Twitter_gql_twitter-whitelist_gql_ts", "components_Tasks_Twitter_twitter-helper_ts", "components_Tasks_Upload_UploadFileBody_tsx", "components_Tasks_Url_Share_UrlShareBody_tsx", "components_Tasks_Url_Visit_UrlVisitBody_tsx", "components_Tasks_Wallet_WalletAddress_tsx", "components_Tasks_Wallet_WalletDotsamaBody_tsx", "components_Tasks_Wallet_WalletEvmBody_tsx", "components_Tasks_Youtube_Visit_YoutubeVisitBody_tsx", "components_Tasks_components_CollapsibleInfo_tsx", "components_Tasks_components_CountDownTimer_tsx", "components_Tasks_components_InstructionCard_tsx", "components_Tasks_components_TaskCompletedCard_tsx", "components_Tasks_components_TaskFrequencyTag_tsx", "components_Tasks_components_VisitLinkTaskBody_tsx", "components_Tasks_hooks_useLinkStorageStep_ts", "components_Web3Wallet_Dotsama_DotsamaAccountList_tsx", "components_Web3Wallet_Dotsama_DotsamaManual_tsx", "components_Web3Wallet_Dotsama_DotsamaNova_tsx", "components_Web3Wallet_Dotsama_DotsamaPolkadotJs_tsx", "components_Web3Wallet_Dotsama_DotsamaRaw_tsx", "components_Web3Wallet_Dotsama_DotsamaSubwallet_tsx", "components_Web3Wallet_Dotsama_DotsamaTalisman_tsx", "components_Web3Wallet_Dotsama_DotsamaWallet_tsx", "components_Web3Wallet_Dotsama_WalletNotFound_tsx", "components_Web3Wallet_Evm_EvmManual_tsx", "components_Web3Wallet_Evm_EvmMetamask_tsx", "components_Web3Wallet_Evm_EvmSubwallet_tsx", "components_Web3Wallet_Evm_EvmTalisman_tsx", "components_Web3Wallet_Evm_EvmWallet_tsx", "components_Web3Wallet_Evm_EvmWalletConnect_tsx", "components_Web3Wallet_Evm_GenericInjectedEvm_tsx"], "m": ["./components/SocialIcons/NotFoundIcon.tsx", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Ckriti%5COneDrive%5CDesktop%5Cweb%20development%5Cairlfyt%5Cairlyft-monorepo%5Cpackages%5Cpublic-ui%5Cpages%5C404.tsx&page=%2F404!", "./pages/404.tsx", "./components/ConditionalRender.tsx", "./components/EventParticipation/EventReferralProvider.tsx", "./components/ExpandableList/ExpandableList.tsx", "./components/ExpandableList/ExpandableListItem.tsx", "./components/ExpandableList/ExpandableModal.tsx", "./components/Footer.tsx", "./components/Giveaways lazy recursive ^\\.\\/.*$", "./components/Giveaways/AirPoolGiveaway/airpool-giveaway.gql.ts", "./components/Giveaways/AirTokenGiveaway/airtoken-giveaway.gql.ts", "./components/Giveaways/MerchandiseGiveaway/merchandise-giveaway.gql.ts", "./components/Giveaways/WhitelistGiveaway/whitelist-giveaway.gql.ts", "./components/Giveaways/components/GiveawayRenderer.tsx", "./components/Giveaways/hooks/useGetGiveaways.ts", "./components/Giveaways/hooks/useGetUserEventRewards.ts", "./components/Loaders/NumberLoader.tsx", "./components/Loaders/ParagraphLoader.tsx", "./components/Loaders/TaskHeaderLoader.tsx", "./components/Project/ProjectBanner.tsx", "./components/Project/ProjectFollow.tsx", "./components/Project/ProjectLayout.tsx", "./components/Project/ProjectLinks.tsx", "./components/Project/ProjectNav.tsx", "./components/Project/ProjectProfile.tsx", "./components/Project/ProjectRightSidebar.tsx", "./components/Project/ProjectStats.tsx", "./components/Project/ProjectUnAuthPrimaryCard.tsx", "./components/Project/ProjectUserPrimaryCard.tsx", "./components/Project/ProjectUserProgress.tsx", "./components/Project/useProjectFollow.gql.ts", "./components/Project/useProjectUserInfo.gql.ts", "./components/Tasks lazy recursive ^\\.\\/.*$", "./components/Tasks/Airboost/airboost.app.ts", "./components/Tasks/Airboost/airboost.gql.ts", "./components/Tasks/Airquest/airquest.app.ts", "./components/Tasks/Airquest/airquest.gql.ts", "./components/Tasks/Blog/Comment/blog-comment.gql.ts", "./components/Tasks/Blog/Write/blog-write.gql.ts", "./components/Tasks/Blog/blog.app.ts", "./components/Tasks/Checkin/UserCheckin.tsx", "./components/Tasks/Checkin/checkin.app.ts", "./components/Tasks/Checkin/checkin.gql.ts", "./components/Tasks/Claim/ClaimRewardBody.tsx", "./components/Tasks/Claim/ClaimRewardTile.tsx", "./components/Tasks/Claim/claim.app.ts", "./components/Tasks/Discord/discord.app.ts", "./components/Tasks/Discord/discord.gql.ts", "./components/Tasks/Email/Whitelist/email-whitelist.gql.ts", "./components/Tasks/Email/email.app.ts", "./components/Tasks/Evm/contract.app.ts", "./components/Tasks/Evm/contract.gql.ts", "./components/Tasks/Faucet/Faucet.app.ts", "./components/Tasks/Faucet/faucet.gql.ts", "./components/Tasks/Form/form.app.ts", "./components/Tasks/Form/form.gql.ts", "./components/Tasks/Instagram/instagram.app.ts", "./components/Tasks/Kickstarter/kickstarter.app.ts", "./components/Tasks/Kickstarter/kickstarter.gql.ts", "./components/Tasks/Luckydraw/luckydraw.app.ts", "./components/Tasks/Luckydraw/luckydraw.cache.ts", "./components/Tasks/Luckydraw/luckydraw.gql.ts", "./components/Tasks/MobileApp/mobile-app.app.ts", "./components/Tasks/MobileApp/mobile-app.gql.ts", "./components/Tasks/Producthunt/producthunt.app.ts", "./components/Tasks/Producthunt/producthunt.gql.ts", "./components/Tasks/Provider lazy recursive ^\\.\\/.*$", "./components/Tasks/Provider/ProviderCard.tsx", "./components/Tasks/Provider/ProviderRenderer.tsx", "./components/Tasks/Quiz/quiz.app.ts", "./components/Tasks/Quiz/quiz.cache.ts", "./components/Tasks/Quiz/quiz.gql.ts", "./components/Tasks/Rest/rest.app.ts", "./components/Tasks/Rest/rest.gql.ts", "./components/Tasks/SecretCode/secret-code.app.ts", "./components/Tasks/Subgraph/subgraph.app.ts", "./components/Tasks/Subgraph/subgraph.gql.ts", "./components/Tasks/Subsocial/gql/subsocial-comment.gql.ts", "./components/Tasks/Subsocial/gql/subsocial-follow.gql.ts", "./components/Tasks/Subsocial/gql/subsocial-post.gql.ts", "./components/Tasks/Subsocial/gql/subsocial-share.gql.ts", "./components/Tasks/Subsocial/gql/subsocial-upvote.gql.ts", "./components/Tasks/Subsocial/subsocial.app.ts", "./components/Tasks/Substrate/substrate.app.ts", "./components/Tasks/Substrate/substrate.gql.ts", "./components/Tasks/TaskList.tsx", "./components/Tasks/TaskListItemExpanded.tsx", "./components/Tasks/TaskListItemTile.tsx", "./components/Tasks/Telegram/telegram.app.ts", "./components/Tasks/Telegram/telegram.gql.ts", "./components/Tasks/Terms/terms.app.ts", "./components/Tasks/Terms/terms.gql.ts", "./components/Tasks/Twitter/gql/twitter-follow.gql.ts", "./components/Tasks/Twitter/gql/twitter-like-retweet.gql.ts", "./components/Tasks/Twitter/gql/twitter-like.gql.ts", "./components/Tasks/Twitter/gql/twitter-post.gql.ts", "./components/Tasks/Twitter/gql/twitter-retweet.gql.ts", "./components/Tasks/Twitter/gql/twitter-ugc.gql.ts", "./components/Tasks/Twitter/twitter.app.ts", "./components/Tasks/Upload/upload.app.ts", "./components/Tasks/Upload/upload.gql.ts", "./components/Tasks/Url/Share/SocialShare.tsx", "./components/Tasks/Url/url.app.ts", "./components/Tasks/Wallet/wallet.app.ts", "./components/Tasks/Wallet/wallet.gql.ts", "./components/Tasks/Youtube/youtube.app.ts", "./components/Tasks/app-store.gql.ts", "./components/Tasks/app-store.helper.ts", "./components/Tasks/app-store.ts", "./components/Tasks/components/SocialShareModal.tsx", "./components/Tasks/components/TaskBody.tsx", "./components/Tasks/components/TaskHeader.tsx", "./components/Tasks/components/TaskLockCard.tsx", "./components/Tasks/components/TaskRenderer.tsx", "./components/Tasks/components/TaskRulesList.tsx", "./components/Tasks/components/TaskStatus.tsx", "./components/Tasks/components/TaskTile.tsx", "./components/Tasks/components/TaskTileLoader.tsx", "./components/Tasks/hooks/useGetTasks.ts", "./components/Tasks/hooks/useIsTaskLocked.ts", "./components/Tasks/hooks/usePreferredEventConnection.ts", "./components/Tasks/link.gql.ts", "./components/Tasks/task-info.fragment.ts", "./components/TwoColumnLayout.tsx", "./graphql/cache.ts", "./graphql/participation-info.fragment.ts", "./helpers/frequency.ts", "./helpers/giveaway.ts", "./hooks/useAnalytics.ts", "./hooks/useHover.ts", "./hooks/useProjectEvent.ts", "./hooks/useTaskParticipation.ts", "./hooks/useUserEventConnection.ts", "./node_modules/jsonp/index.js", "./node_modules/jsonp/node_modules/debug/src/browser.js", "./node_modules/jsonp/node_modules/debug/src/debug.js", "./node_modules/jsonp/node_modules/ms/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Ckriti%5COneDrive%5CDesktop%5Cweb%20development%5Cairlfyt%5Cairlyft-monorepo%5Cpackages%5Cpublic-ui%5Cpages%5C%5Bproject%5D%5Cindex.tsx&page=%2F%5Bproject%5D!", "./node_modules/react-share/es/EmailIcon.js", "./node_modules/react-share/es/EmailShareButton.js", "./node_modules/react-share/es/FacebookIcon.js", "./node_modules/react-share/es/FacebookMessengerIcon.js", "./node_modules/react-share/es/FacebookMessengerShareButton.js", "./node_modules/react-share/es/FacebookShareButton.js", "./node_modules/react-share/es/FacebookShareCount.js", "./node_modules/react-share/es/HatenaIcon.js", "./node_modules/react-share/es/HatenaShareButton.js", "./node_modules/react-share/es/HatenaShareCount.js", "./node_modules/react-share/es/InstapaperIcon.js", "./node_modules/react-share/es/InstapaperShareButton.js", "./node_modules/react-share/es/LineIcon.js", "./node_modules/react-share/es/LineShareButton.js", "./node_modules/react-share/es/LinkedinIcon.js", "./node_modules/react-share/es/LinkedinShareButton.js", "./node_modules/react-share/es/LivejournalIcon.js", "./node_modules/react-share/es/LivejournalShareButton.js", "./node_modules/react-share/es/MailruIcon.js", "./node_modules/react-share/es/MailruShareButton.js", "./node_modules/react-share/es/OKIcon.js", "./node_modules/react-share/es/OKShareButton.js", "./node_modules/react-share/es/OKShareCount.js", "./node_modules/react-share/es/PinterestIcon.js", "./node_modules/react-share/es/PinterestShareButton.js", "./node_modules/react-share/es/PinterestShareCount.js", "./node_modules/react-share/es/PocketIcon.js", "./node_modules/react-share/es/PocketShareButton.js", "./node_modules/react-share/es/RedditIcon.js", "./node_modules/react-share/es/RedditShareButton.js", "./node_modules/react-share/es/RedditShareCount.js", "./node_modules/react-share/es/ShareButton.js", "./node_modules/react-share/es/TelegramIcon.js", "./node_modules/react-share/es/TelegramShareButton.js", "./node_modules/react-share/es/TumblrIcon.js", "./node_modules/react-share/es/TumblrShareButton.js", "./node_modules/react-share/es/TumblrShareCount.js", "./node_modules/react-share/es/TwitterIcon.js", "./node_modules/react-share/es/TwitterShareButton.js", "./node_modules/react-share/es/VKIcon.js", "./node_modules/react-share/es/VKShareButton.js", "./node_modules/react-share/es/VKShareCount.js", "./node_modules/react-share/es/ViberIcon.js", "./node_modules/react-share/es/ViberShareButton.js", "./node_modules/react-share/es/WeiboIcon.js", "./node_modules/react-share/es/WeiboShareButton.js", "./node_modules/react-share/es/WhatsappIcon.js", "./node_modules/react-share/es/WhatsappShareButton.js", "./node_modules/react-share/es/WorkplaceIcon.js", "./node_modules/react-share/es/WorkplaceShareButton.js", "./node_modules/react-share/es/hocs/createIcon.js", "./node_modules/react-share/es/hocs/createShareButton.js", "./node_modules/react-share/es/hocs/createShareCount.js", "./node_modules/react-share/es/index.js", "./node_modules/react-share/es/utils/assert.js", "./node_modules/react-share/es/utils/objectToGetParams.js", "./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "./node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "./node_modules/use-sync-external-store/shim/index.js", "./node_modules/use-sync-external-store/shim/with-selector.js", "./node_modules/zustand/esm/index.mjs", "./node_modules/zustand/esm/vanilla.mjs", "./pages/[project]/index.tsx", "./services/events.ts", "./components/AlertBox.tsx", "./components/BlockExplorerLink.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolDotsamaGiveawayClaim.tsx", "./components/Giveaways/AirPoolGiveaway/useAirPoolGiveawayClaim.tsx", "./components/Giveaways/GiveawayTransactionHash.tsx", "./components/Giveaways/hooks/useGetContract.ts", "./components/RecaptchaDeclaration.tsx", "./components/TransactionResult.tsx", "./components/Web3Wallet lazy recursive ^\\.\\/.*$", "./components/Web3Wallet/Dotsama/DotsamaWallet.tsx", "./components/Web3Wallet/Web3WalletRenderer.tsx", "./hooks/useGiveawayTxHash.ts", "./node_modules/@polkadot/extension-dapp/bundle.js", "./node_modules/@polkadot/extension-dapp/index.js", "./node_modules/@polkadot/extension-dapp/packageInfo.js", "./node_modules/@polkadot/extension-dapp/util.js", "./node_modules/@polkadot/extension-dapp/wrapBytes.js", "./components/Giveaways/AirPoolGiveaway/AirPoolEVMGiveawayClaim.tsx", "./components/Giveaways/hooks/useGetPoolWindowInfo.ts", "./components/Web3Wallet/Evm/EvmWallet.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolFcfsRangeDetails.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolGiveawayClaim.tsx", "./components/Giveaways/Distribution/TokenFCFSRangeDistribution.tsx", "./components/Giveaways/components/GiveawayFCFSRangeTag.tsx", "./components/Giveaways/components/GiveawaySummaryItem.tsx", "./components/Giveaways/components/TokenAddress.tsx", "./components/Giveaways/components/TokenFCFSUserProgress.tsx", "./components/Giveaways/components/TokenGiveawayClaimStats.tsx", "./components/Giveaways/components/TokenGiveawayDetails.tsx", "./components/Panel.tsx", "./components/Paragraph.tsx", "./hooks/useGetBlockchain.ts", "./components/Giveaways/AirPoolGiveaway/AirPoolFcfsRangeSummary.tsx", "./components/Giveaways/components/TokenGiveawaySummaryItem.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolFcfsTaskIdDetails.tsx", "./components/Giveaways/Distribution/TokenFCFSTaskIdDistribution.tsx", "./components/Tasks/TaskSummary.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolFcfsTaskIdSummary.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolManualDetails.tsx", "./components/Giveaways/Distribution/TokenManualDistribution.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolManualSummary.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolRandomDetails.tsx", "./components/Giveaways/Distribution/TokenRandomDistribution.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolRandomSummary.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolShopDetails.tsx", "./components/Giveaways/components/ShopGiveawayDetails.tsx", "./components/Giveaways/AirPoolGiveaway/AirPoolShopSummary.tsx", "./components/Giveaways/components/ShopGiveawaySummaryItem.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenDotsamaGiveawayClaim.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenEVMGiveawayClaim.tsx", "./components/Giveaways/AirTokenGiveaway/useAirTokenGiveawayClaim.tsx", "./components/Giveaways/hooks/useGetTokenWindowInfo.ts", "./components/Giveaways/AirTokenGiveaway/AirTokenFcfsRangeDetails.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenGiveawayClaim.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenFcfsRangeSummary.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenFcfsTaskIdDetails.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenFcfsTaskIdSummary.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenManualDetails.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenManualSummary.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenRandomDetails.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenRandomSummary.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenShopDetails.tsx", "./components/Giveaways/AirTokenGiveaway/AirTokenShopSummary.tsx", "./components/Giveaways/GiveawayRendererWrapper.tsx", "./components/EmptyData/EmptyDataSubscribe.tsx", "./components/Giveaways/GiveawayShopList.tsx", "./components/Loaders/ListItemSkeleton.tsx", "./components/Loaders/ListSkeleton.tsx", "./components/Giveaways/GiveawaySummaryList.tsx", "./components/Giveaways/MerchandiseGiveaway/MerchandiseGiveawayClaim.tsx", "./components/SelectField.tsx", "./components/Tasks/Form/FormAnswerForm.tsx", "./components/Tasks/Form/FormItemRenderer.tsx", "./components/TextField.tsx", "./components/Giveaways/MerchandiseGiveaway/MerchandiseShopDetails.tsx", "./components/TextEditor.tsx", "./node_modules/draft-js/dist/Draft.css", "./node_modules/draft-js/lib/AtomicBlockUtils.js", "./node_modules/draft-js/lib/BlockMapBuilder.js", "./node_modules/draft-js/lib/BlockTree.js", "./node_modules/draft-js/lib/CharacterMetadata.js", "./node_modules/draft-js/lib/CompositeDraftDecorator.js", "./node_modules/draft-js/lib/ContentBlock.js", "./node_modules/draft-js/lib/ContentBlockNode.js", "./node_modules/draft-js/lib/ContentState.js", "./node_modules/draft-js/lib/ContentStateInlineStyle.js", "./node_modules/draft-js/lib/DOMObserver.js", "./node_modules/draft-js/lib/DefaultDraftBlockRenderMap.js", "./node_modules/draft-js/lib/DefaultDraftInlineStyle.js", "./node_modules/draft-js/lib/Draft.js", "./node_modules/draft-js/lib/DraftEditor.react.js", "./node_modules/draft-js/lib/DraftEditorBlock.react.js", "./node_modules/draft-js/lib/DraftEditorBlockNode.react.js", "./node_modules/draft-js/lib/DraftEditorCompositionHandler.js", "./node_modules/draft-js/lib/DraftEditorContents-core.react.js", "./node_modules/draft-js/lib/DraftEditorContents.react.js", "./node_modules/draft-js/lib/DraftEditorContentsExperimental.react.js", "./node_modules/draft-js/lib/DraftEditorDecoratedLeaves.react.js", "./node_modules/draft-js/lib/DraftEditorDragHandler.js", "./node_modules/draft-js/lib/DraftEditorEditHandler.js", "./node_modules/draft-js/lib/DraftEditorFlushControlled.js", "./node_modules/draft-js/lib/DraftEditorLeaf.react.js", "./node_modules/draft-js/lib/DraftEditorNode.react.js", "./node_modules/draft-js/lib/DraftEditorPlaceholder.react.js", "./node_modules/draft-js/lib/DraftEditorTextNode.react.js", "./node_modules/draft-js/lib/DraftEffects.js", "./node_modules/draft-js/lib/DraftEntity.js", "./node_modules/draft-js/lib/DraftEntityInstance.js", "./node_modules/draft-js/lib/DraftEntitySegments.js", "./node_modules/draft-js/lib/DraftJsDebugLogging.js", "./node_modules/draft-js/lib/DraftModifier.js", "./node_modules/draft-js/lib/DraftOffsetKey.js", "./node_modules/draft-js/lib/DraftPasteProcessor.js", "./node_modules/draft-js/lib/DraftRemovableWord.js", "./node_modules/draft-js/lib/DraftStringKey.js", "./node_modules/draft-js/lib/DraftTreeAdapter.js", "./node_modules/draft-js/lib/DraftTreeInvariants.js", "./node_modules/draft-js/lib/EditorBidiService.js", "./node_modules/draft-js/lib/EditorState.js", "./node_modules/draft-js/lib/KeyBindingUtil.js", "./node_modules/draft-js/lib/RawDraftContentState.js", "./node_modules/draft-js/lib/RichTextEditorUtil.js", "./node_modules/draft-js/lib/SecondaryClipboard.js", "./node_modules/draft-js/lib/SelectionState.js", "./node_modules/draft-js/lib/adjustBlockDepthForContentState.js", "./node_modules/draft-js/lib/applyEntityToContentBlock.js", "./node_modules/draft-js/lib/applyEntityToContentState.js", "./node_modules/draft-js/lib/convertFromDraftStateToRaw.js", "./node_modules/draft-js/lib/convertFromHTMLToContentBlocks.js", "./node_modules/draft-js/lib/convertFromRawToDraftState.js", "./node_modules/draft-js/lib/createCharacterList.js", "./node_modules/draft-js/lib/decodeEntityRanges.js", "./node_modules/draft-js/lib/decodeInlineStyleRanges.js", "./node_modules/draft-js/lib/draftKeyUtils.js", "./node_modules/draft-js/lib/editOnBeforeInput.js", "./node_modules/draft-js/lib/editOnBlur.js", "./node_modules/draft-js/lib/editOnCompositionStart.js", "./node_modules/draft-js/lib/editOnCopy.js", "./node_modules/draft-js/lib/editOnCut.js", "./node_modules/draft-js/lib/editOnDragOver.js", "./node_modules/draft-js/lib/editOnDragStart.js", "./node_modules/draft-js/lib/editOnFocus.js", "./node_modules/draft-js/lib/editOnInput.js", "./node_modules/draft-js/lib/editOnKeyDown.js", "./node_modules/draft-js/lib/editOnPaste.js", "./node_modules/draft-js/lib/editOnSelect.js", "./node_modules/draft-js/lib/encodeEntityRanges.js", "./node_modules/draft-js/lib/encodeInlineStyleRanges.js", "./node_modules/draft-js/lib/expandRangeToStartOfLine.js", "./node_modules/draft-js/lib/findAncestorOffsetKey.js", "./node_modules/draft-js/lib/findRangesImmutable.js", "./node_modules/draft-js/lib/generateRandomKey.js", "./node_modules/draft-js/lib/getCharacterRemovalRange.js", "./node_modules/draft-js/lib/getContentEditableContainer.js", "./node_modules/draft-js/lib/getContentStateFragment.js", "./node_modules/draft-js/lib/getCorrectDocumentFromNode.js", "./node_modules/draft-js/lib/getDefaultKeyBinding.js", "./node_modules/draft-js/lib/getDraftEditorSelection.js", "./node_modules/draft-js/lib/getDraftEditorSelectionWithNodes.js", "./node_modules/draft-js/lib/getEntityKeyForSelection.js", "./node_modules/draft-js/lib/getFragmentFromSelection.js", "./node_modules/draft-js/lib/getNextDelimiterBlockKey.js", "./node_modules/draft-js/lib/getOwnObjectValues.js", "./node_modules/draft-js/lib/getRangeBoundingClientRect.js", "./node_modules/draft-js/lib/getRangeClientRects.js", "./node_modules/draft-js/lib/getRangesForDraftEntity.js", "./node_modules/draft-js/lib/getSafeBodyFromHTML.js", "./node_modules/draft-js/lib/getSelectionOffsetKeyForNode.js", "./node_modules/draft-js/lib/getTextContentFromFiles.js", "./node_modules/draft-js/lib/getUpdatedSelectionState.js", "./node_modules/draft-js/lib/getVisibleSelectionRect.js", "./node_modules/draft-js/lib/getWindowForNode.js", "./node_modules/draft-js/lib/gkx.js", "./node_modules/draft-js/lib/insertFragmentIntoContentState.js", "./node_modules/draft-js/lib/insertIntoList.js", "./node_modules/draft-js/lib/insertTextIntoContentState.js", "./node_modules/draft-js/lib/isElement.js", "./node_modules/draft-js/lib/isEventHandled.js", "./node_modules/draft-js/lib/isHTMLAnchorElement.js", "./node_modules/draft-js/lib/isHTMLBRElement.js", "./node_modules/draft-js/lib/isHTMLElement.js", "./node_modules/draft-js/lib/isHTMLImageElement.js", "./node_modules/draft-js/lib/isInstanceOfNode.js", "./node_modules/draft-js/lib/isSelectionAtLeafStart.js", "./node_modules/draft-js/lib/isSoftNewlineEvent.js", "./node_modules/draft-js/lib/keyCommandBackspaceToStartOfLine.js", "./node_modules/draft-js/lib/keyCommandBackspaceWord.js", "./node_modules/draft-js/lib/keyCommandDeleteWord.js", "./node_modules/draft-js/lib/keyCommandInsertNewline.js", "./node_modules/draft-js/lib/keyCommandMoveSelectionToEndOfBlock.js", "./node_modules/draft-js/lib/keyCommandMoveSelectionToStartOfBlock.js", "./node_modules/draft-js/lib/keyCommandPlainBackspace.js", "./node_modules/draft-js/lib/keyCommandPlainDelete.js", "./node_modules/draft-js/lib/keyCommandTransposeCharacters.js", "./node_modules/draft-js/lib/keyCommandUndo.js", "./node_modules/draft-js/lib/modifyBlockForContentState.js", "./node_modules/draft-js/lib/moveBlockInContentState.js", "./node_modules/draft-js/lib/moveSelectionBackward.js", "./node_modules/draft-js/lib/moveSelectionForward.js", "./node_modules/draft-js/lib/randomizeBlockMapKeys.js", "./node_modules/draft-js/lib/removeEntitiesAtEdges.js", "./node_modules/draft-js/lib/removeRangeFromContentState.js", "./node_modules/draft-js/lib/removeTextWithStrategy.js", "./node_modules/draft-js/lib/sanitizeDraftText.js", "./node_modules/draft-js/lib/setDraftEditorSelection.js", "./node_modules/draft-js/lib/splitBlockInContentState.js", "./node_modules/draft-js/lib/splitTextIntoTextBlocks.js", "./node_modules/draft-js/lib/uuid.js", "./node_modules/fbjs/lib/DataTransfer.js", "./node_modules/fbjs/lib/Keys.js", "./node_modules/fbjs/lib/PhotosMimeType.js", "./node_modules/fbjs/lib/Scroll.js", "./node_modules/fbjs/lib/Style.js", "./node_modules/fbjs/lib/TokenizeUtil.js", "./node_modules/fbjs/lib/URI.js", "./node_modules/fbjs/lib/UnicodeBidi.js", "./node_modules/fbjs/lib/UnicodeBidiDirection.js", "./node_modules/fbjs/lib/UnicodeBidiService.js", "./node_modules/fbjs/lib/UnicodeUtils.js", "./node_modules/fbjs/lib/UserAgent.js", "./node_modules/fbjs/lib/UserAgentData.js", "./node_modules/fbjs/lib/VersionRange.js", "./node_modules/fbjs/lib/camelize.js", "./node_modules/fbjs/lib/containsNode.js", "./node_modules/fbjs/lib/createArrayFromMixed.js", "./node_modules/fbjs/lib/cx.js", "./node_modules/fbjs/lib/emptyFunction.js", "./node_modules/fbjs/lib/getActiveElement.js", "./node_modules/fbjs/lib/getDocumentScrollElement.js", "./node_modules/fbjs/lib/getElementPosition.js", "./node_modules/fbjs/lib/getElementRect.js", "./node_modules/fbjs/lib/getScrollPosition.js", "./node_modules/fbjs/lib/getStyleProperty.js", "./node_modules/fbjs/lib/getUnboundedScrollPosition.js", "./node_modules/fbjs/lib/getViewportDimensions.js", "./node_modules/fbjs/lib/hyphenate.js", "./node_modules/fbjs/lib/invariant.js", "./node_modules/fbjs/lib/isNode.js", "./node_modules/fbjs/lib/isTextNode.js", "./node_modules/fbjs/lib/joinClasses.js", "./node_modules/fbjs/lib/mapObject.js", "./node_modules/fbjs/lib/memoizeStringOnly.js", "./node_modules/fbjs/lib/nullthrows.js", "./node_modules/fbjs/lib/setImmediate.js", "./node_modules/fbjs/lib/warning.js", "./node_modules/immutable/dist/immutable.js", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[12].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[12].use[2]!./node_modules/draft-js/dist/Draft.css", "./node_modules/next/dist/compiled/setimmediate/setImmediate.js", "./node_modules/ua-parser-js/src/ua-parser.js", "./components/Giveaways/MerchandiseGiveaway/MerchandiseShopSummary.tsx", "./components/EventParticipation/PrimaryInfoCard/UserParticipationSummaryCard.tsx", "./components/Giveaways/WhitelistGiveaway/WhitelistManualDetails.tsx", "./components/Giveaways/WhitelistGiveaway/components/WhitelistGiveawayDetails.tsx", "./hooks/useFindUserRewardByStatus.ts", "./components/Giveaways/WhitelistGiveaway/WhitelistManualSummary.tsx", "./components/Giveaways/WhitelistGiveaway/components/WhitelistGiveawaySummary.tsx", "./components/Giveaways/WhitelistGiveaway/WhitelistRandomDetails.tsx", "./components/Giveaways/WhitelistGiveaway/WhitelistRandomSummary.tsx", "./components/Giveaways/components/TokenGiveawayHeader.tsx", "./components/Tasks/Provider/DotsamaProvider/DotsamaProvider.tsx", "./components/Tasks/Provider/DotsamaProvider/useDotsamaConnect.ts", "./components/Tasks/Provider/ProviderUseConnection.tsx", "./components/Tasks/Provider/EmailProvider/EmailProvider.tsx", "./components/Tasks/Provider/EvmProvider/EvmProvider.tsx", "./components/Tasks/Provider/EvmProvider/useEvmConnect.ts", "./components/Tasks/Provider/OauthProvider/OauthProvider.tsx", "./components/Tasks/Provider/ProviderButton.tsx", "./components/Tasks/Provider/TelegramProvider/TelegramProvider.tsx", "./hooks/useTelegramConnect.ts", "./components/DescriptionList.tsx", "./components/Tasks/Airboost/AirboostReferralBody.tsx", "./components/TimeLine/TimeLineItem.tsx", "./components/TimeLine/index.tsx", "./components/Tasks/Airboost/AirboostReferralHeader.tsx", "./components/Tasks/Airboost/AirboostReferralTile.tsx", "./components/Tasks/Airquest/AirquestFollowBody.tsx", "./components/Tasks/components/CountDownTimer.tsx", "./components/Tasks/components/TaskCompletedCard.tsx", "./components/Tasks/Blog/Comment/BlogCommentBody.tsx", "./components/Tasks/hooks/useLinkStorageStep.ts", "./components/Tasks/Blog/Write/BlogWriteBody.tsx", "./components/Tasks/Discord/DiscordJoinBody.tsx", "./components/Tasks/Email/Address/EmailAddressBody.tsx", "./components/Tasks/Email/Address/email-address.gql.ts", "./components/Tasks/Email/Subscribe/EmailSubscribeBody.tsx", "./components/Tasks/Email/Subscribe/email-subscribe.gql.ts", "./components/Tasks/Email/Whitelist/EmailWhitelistBody.tsx", "./components/Tasks/Evm/EvmContractBody.tsx", "./components/Tasks/Evm/EvmInstructionsCard.tsx", "./components/Tasks/Evm/FormItemRenderer.tsx", "./components/Tasks/components/CollapsibleInfo.tsx", "./components/Tasks/components/InstructionCard.tsx", "./components/Tasks/Faucet/BaseFaucetBody.tsx", "./hooks/useValueByKeyGroup.ts", "./components/Tasks/Faucet/Dot/FaucetDotsamaBody.tsx", "./components/Tasks/Faucet/Evm/FaucetEvmBody.tsx", "./components/Tasks/Form/FormAnswerBody.tsx", "./components/Tasks/Instagram/View/InstagramViewBody.tsx", "./components/SocialIcons/Instagram.tsx", "./components/Tasks/Instagram/Visit/InstagramVisitBody.tsx", "./components/Tasks/components/VisitLinkTaskBody.tsx", "./components/Tasks/Kickstarter/KickstarterSupportBody.tsx", "./components/MysteryBox.tsx", "./components/Tasks/Luckydraw/LuckydrawBoxBody.tsx", "./node_modules/canvas-confetti/dist/confetti.module.mjs", "./components/Spinwheel.tsx", "./components/Tasks/Luckydraw/LuckydrawPlayBody.tsx", "./components/SlotMachine.tsx", "./components/Tasks/Luckydraw/LuckydrawSlotBody.tsx", "./components/Tasks/MobileApp/MobileAppInstallBody.tsx", "./components/Tasks/Producthunt/Follow/ProducthuntFollowBody.tsx", "./components/Tasks/Producthunt/Upvote/ProducthuntUpvoteBody.tsx", "./components/Patterns/PatternSquareDot.tsx", "./components/Tasks/Quiz/QuizPlayBody.tsx", "./components/Tasks/Quiz/QuizPlayBodyRenderer.tsx", "./components/Tasks/Quiz/components/Option.tsx", "./components/Tasks/Quiz/components/OptionList.tsx", "./components/Tasks/Quiz/components/Question.tsx", "./components/Tasks/Quiz/components/useQuizData.ts", "./components/Tasks/Quiz/QuizPlayHeader.tsx", "./components/Tasks/Quiz/QuizPlayTile.tsx", "./components/Tasks/Quiz/components/Progress.tsx", "./components/Tasks/Rest/BaseRestBody.tsx", "./components/Tasks/Rest/RestDotsamaBody.tsx", "./components/Tasks/Rest/RestEvmBody.tsx", "./components/Tasks/Rest/RestRawBody.tsx", "./components/Tasks/Rest/WalletAddress.tsx", "./components/Tasks/SecretCode/SecretCodeValidateBody.tsx", "./components/Tasks/SecretCode/secret-code.gql.ts", "./components/Tasks/Subgraph/SubgraphRawBody.tsx", "./components/Tasks/Subgraph/WalletAddress.tsx", "./components/Tasks/Subsocial/SubsocialCommentBody.tsx", "./components/Tasks/Subsocial/SubsocialPostActionBody.tsx", "./components/Tasks/Subsocial/SubsocialFollowBody.tsx", "./components/Tasks/Subsocial/SubsocialPostBody.tsx", "./components/Tasks/Subsocial/SubsocialProfileBody.tsx", "./components/Tasks/Subsocial/gql/subsocial-profile.gql.ts", "./components/Tasks/Subsocial/SubsocialShareBody.tsx", "./components/Tasks/Subsocial/SubsocialSpaceBody.tsx", "./components/Tasks/Subsocial/gql/subsocial-space.gql.ts", "./components/Tasks/Subsocial/SubsocialUpvoteBody.tsx", "./components/Tasks/Substrate/SubstrateQueryBody.tsx", "./components/Tasks/Telegram/TelegramJoinBody.tsx", "./components/Tasks/Terms/TermsDotsamaBody.tsx", "./components/Tasks/Wallet/WalletAddress.tsx", "./components/Tasks/Terms/TermsTextBody.tsx", "./components/Tasks/Twitter/Follow/TwitterFollowBody.tsx", "./components/Tasks/Twitter/TweetIntentButton.tsx", "./components/Tasks/Twitter/twitter-helper.ts", "./components/Loaders/SocialPostLoader.tsx", "./components/Tasks/Twitter/Like/TwitterLikeBody.tsx", "./components/TweetEmbed.tsx", "./components/Tasks/Twitter/LikeRetweet/TwitterLikeRetweetBody.tsx", "./components/Tasks/Twitter/Post/TwitterPostBody.tsx", "./components/Tasks/Twitter/Retweet/TwitterRetweetBody.tsx", "./components/Tasks/Twitter/Ugc/TwitterUgcBody.tsx", "./components/Tasks/Twitter/Whitelist/TwitterWhitelistBody.tsx", "./components/Tasks/Twitter/gql/twitter-whitelist.gql.ts", "./components/Tasks/Upload/UploadFileBody.tsx", "./components/Tasks/Url/Share/UrlShareBody.tsx", "./components/Tasks/Url/Visit/UrlVisitBody.tsx", "./components/Tasks/Wallet/WalletDotsamaBody.tsx", "./components/Tasks/Wallet/WalletEvmBody.tsx", "./components/Tasks/Youtube/Visit/YoutubeVisitBody.tsx", "./components/Tasks/components/TaskFrequencyTag.tsx", "./components/Web3Wallet/Dotsama/DotsamaAccountList.tsx", "./components/Web3Wallet/Dotsama/DotsamaManual.tsx", "./components/ExternalLink.tsx", "./components/Web3Wallet/Dotsama/DotsamaNova.tsx", "./components/Web3Wallet/Dotsama/DotsamaRaw.tsx", "./components/Web3Wallet/Dotsama/WalletNotFound.tsx", "./components/Web3Wallet/Dotsama/DotsamaPolkadotJs.tsx", "./components/Web3Wallet/Dotsama/DotsamaSubwallet.tsx", "./components/Web3Wallet/Dotsama/DotsamaTalisman.tsx", "./components/Web3Wallet/Evm/EvmManual.tsx", "./node_modules/ethers/lib/utils.js", "./components/Web3Wallet/Evm/EvmMetamask.tsx", "./components/Web3Wallet/Evm/GenericInjectedEvm.tsx", "./components/Web3Wallet/Evm/EvmSubwallet.tsx", "./components/Web3Wallet/Evm/EvmTalisman.tsx", "./components/Web3Wallet/Evm/EvmWalletConnect.tsx"]}