import { OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Hash } from '@polkadot/types/interfaces';
import {
  AssetType,
  BlockchainAsset,
} from '@root/blockchain/blockchain-asset.entity';
import { TransactionStatus } from '@root/blockchain/blockchain.constants';
import { DotsamaNft, NFTStatus } from '@root/blockchain/dotsama-nft.entity';
import { ClaimService } from '@root/claim/claim.service';
import { EventReward, RewardStatus } from '@root/claim/event-reward.entity';
import { PubSubService } from '@root/core/services/pub-sub.service';
import { DotsamaAssetsService } from '@root/dotsama/dotsama-assets.service';
import { DotsamaNFTService } from '@root/dotsama/dotsama-nft.service';
import { DotsamaService } from '@root/dotsama/dotsama.service';
import { UserNotificationType } from '@root/notification/user-notification.entity';
import { Job } from 'bull';
import { DataSource, EntityManager, In } from 'typeorm';
import { GiveawayService } from '../giveaway/giveaway.service';

export const CLAIM_DOTSAMA_QUEUE = 'claim-dotsama';

export enum ClaimDotsamaOperationType {
  TRANSFER,
  MINT,
}

export type ClaimDotsamaQueueData = {
  tokenId: string;
  giveawayId: string;
  eventId: string;
  userId: string;
  blockchainId: string;
  projectId: string;
  address: string;
  assetType: AssetType;
  operationType: ClaimDotsamaOperationType;
  capped: boolean;
  poolId?: string;
};

@Processor(CLAIM_DOTSAMA_QUEUE)
export class ClaimDotsamaConsumer {
  protected readonly logger = new Logger(this.constructor.name);
  constructor(
    private readonly dataSource: DataSource,
    private readonly claimService: ClaimService,
    private readonly dotsamaService: DotsamaService,
    private readonly dotsamaAssetsService: DotsamaAssetsService,
    private readonly giveawayService: GiveawayService,
    private readonly dotsamaNFTService: DotsamaNFTService,
    private readonly pubSubService: PubSubService,
  ) {}

  @OnQueueFailed()
  handler(job: Job, error: Error) {
    this.logger.error(error);
  }

  protected updateSuccess = async (
    claimUuids: string[],
    data: ClaimDotsamaQueueData,
    hash: string,
  ) => {
    try {
      const { userId, projectId, eventId } = data;

      await this.dataSource
        .createQueryBuilder()
        .update(EventReward)
        .set({
          txHash: hash,
        })
        .where({ id: In(claimUuids) })
        .execute();

      await this.pubSubService.publishRewardClaim({
        notificationType: UserNotificationType.CLAIM_SUCCESS,
        userId,
        projectId,
        eventId,
      });
    } catch (error) {
      this.logger.error(error);
      //Fail silently as we do not want to rollback incase this update fails
    }
  };

  protected updateFailure = async (
    data: ClaimDotsamaQueueData,
    message?: string,
  ) => {
    const { userId, projectId, eventId } = data;

    await this.pubSubService.publishRewardClaim({
      notificationType: UserNotificationType.CLAIM_FAILED,
      userId,
      projectId,
      eventId,
      message,
    });
  };

  /**
   * Note: This cannot be processed in parallel. Only one job at a time.
   */
  @Process()
  async transcode(job: Job<ClaimDotsamaQueueData>) {
    const {
      blockchainId,
      projectId,
      address,
      userId,
      giveawayId,
      eventId,
      tokenId,
      operationType,
      assetType,
      capped,
      poolId,
    } = job.data;

    //Try to acquire lock, or return
    try {
      await this.giveawayService.claimLock().acquire(userId, giveawayId);
    } catch (error) {
      return {};
    }

    let claimUuids = [];
    let claimUuidsAmount = [];

    try {
      //Find the rewards under processing and calculate amount
      const userEventRewards = await this.claimService.findUserRewardByStatus(
        giveawayId,
        userId,
        eventId,
        [RewardStatus.PROCESSING],
      );

      if (userEventRewards.length < 1) {
        throw new Error('Could not find any reward to claim');
      }

      const totalAmount = userEventRewards.reduce<bigint>(
        (acc, item) => acc + BigInt(item.data.amount),
        BigInt('0'),
      );

      claimUuids = (userEventRewards || []).map((item) => item.id);
      claimUuidsAmount = (userEventRewards || []).map((item) => {
        return { id: item.id, amount: item.data.amount };
      });

      let hash: Hash;

      await this.dataSource.transaction(async (transactionalEntityManager) => {
        await this.updateClaimed(
          transactionalEntityManager,
          giveawayId,
          totalAmount,
          capped,
        );

        await transactionalEntityManager
          .createQueryBuilder()
          .update(EventReward)
          .set({ status: TransactionStatus.SUCCESS })
          .where({ id: In(claimUuids) })
          .execute();

        if (operationType === ClaimDotsamaOperationType.MINT) {
          if (assetType === AssetType.DOTSAMA_NFT) {
            hash = await this.dotsamaNFTService.mint({
              blockchainId,
              userId: projectId,
              toAddress: address,
              amount: totalAmount,
              tokenId,
            });
          } else {
            hash = await this.dotsamaAssetsService.mint({
              blockchainId,
              userId: projectId,
              toAddress: address,
              amount: totalAmount,
              tokenId,
            });
          }
        } else {
          if (tokenId === '-1') {
            hash = await this.dotsamaService.transfer(
              blockchainId,
              projectId,
              address,
              totalAmount.toString(),
            );
          } else {
            if (assetType === AssetType.DOTSAMA_NFT) {
              const items = await this.getAndUpdateRandomNFTs(
                transactionalEntityManager,
                tokenId,
                poolId,
                Number(totalAmount),
              );
              hash = await this.dotsamaNFTService.transfer({
                blockchainId,
                tokenId,
                toAddress: address,
                items,
                userId: projectId,
              });

              await this.updateNFTSStatus(transactionalEntityManager, items);
            } else {
              hash = await this.dotsamaAssetsService.transferAsset(
                blockchainId,
                projectId,
                address,
                totalAmount.toString(),
                tokenId,
              );
            }
          }
        }
      });
      await this.updateSuccess(claimUuids, job.data, hash?.toString());
    } catch (error) {
      if (claimUuids && claimUuids.length > 0) {
        await this.giveawayService.updateEventRewardStatus(
          claimUuids,
          TransactionStatus.FAILED,
        );
      }
      if (claimUuidsAmount && claimUuidsAmount.length > 0) {
        for (const update of claimUuidsAmount) {
          await this.dataSource.createQueryRunner().query(
            `
              UPDATE giveaways
              SET data = jsonb_set(
                data,
                '{claimedAmount}',
                to_jsonb((data->>'claimedAmount')::numeric - $2::numeric)
              )
              WHERE id = $1
            `,
            [update.id, update.amount],
          );
        }
      }
      await this.updateFailure(job.data, error?.message);
      this.logger.error(error?.message);
    } finally {
      //Release lock whether it was a failure or success
      await this.giveawayService.claimLock().release(userId, giveawayId);
    }

    return {};
  }

  private updateNFTSStatus(
    transactionalEntityManager: EntityManager,
    itemIds: Array<string>,
    status = NFTStatus.DISTRIBUTED,
  ) {
    return transactionalEntityManager
      .createQueryBuilder()
      .update(DotsamaNft)
      .set({ status })
      .where({ itemId: In(itemIds) })
      .execute();
  }

  private async getAndUpdateRandomNFTs(
    transactionalEntityManager: EntityManager,
    tokenId: string,
    poolId: string,
    amount: number,
  ): Promise<Array<string>> {
    const data = await transactionalEntityManager.query(
      `
      WITH selected_nfts AS (
        SELECT dn."itemId", dn."blockchainAssetId"
        FROM dotsama_nfts dn
        JOIN blockchain_assets ba ON dn."blockchainAssetId" = ba.id
        WHERE ba."tokenId" = $1
          AND dn."blockchainPoolId" = $2
          AND (dn.status IS NULL OR dn.status = $4)
        ORDER BY RANDOM()
        LIMIT $3
        FOR UPDATE SKIP LOCKED
      )
      UPDATE dotsama_nfts dn
      SET status = 'LOCKED'
      FROM selected_nfts sn
      WHERE dn."itemId" = sn."itemId" AND dn."blockchainAssetId" = sn."blockchainAssetId"
      RETURNING dn."itemId", dn."blockchainAssetId"
    `,
      [tokenId, poolId, amount, NFTStatus.AVAILABLE],
    );

    if (data?.[0].length < amount) {
      throw new Error(
        `Insufficient NFTs available. Requested: ${amount}, Available: ${data.length}`,
      );
    }

    return data[0].map((item) => item.itemId);
  }

  private async updateClaimed(
    transactionalEntityManager: EntityManager,
    giveawayId: string,
    totalAmount: bigint,
    capped: boolean,
  ) {
    const data = await transactionalEntityManager.query(
      `
      WITH updated_giveaway AS (
        UPDATE "giveaways"
        SET data = jsonb_set(
            data,
            '{claimedAmount}',
            (
                COALESCE((data->>'claimedAmount')::numeric, 0) + 
                $2::numeric
            )::text::jsonb
        )
        WHERE id = $1
        ${
          capped
            ? "AND ($2::numeric <= (data->>'amount')::numeric - COALESCE((data->>'claimedAmount')::numeric, 0))"
            : ''
        }
        RETURNING id
      )
      SELECT
        updated_giveaway.*,
        COALESCE(giveaway_pool."blockchainPoolId", giveaway_token."tokenId") as "poolOrTokenId",
        CASE 
          WHEN giveaway_pool."blockchainPoolId" IS NOT NULL THEN true
          ELSE false
        END as "isBlockchainPool"
      FROM
        updated_giveaway
      LEFT JOIN giveaway_pools giveaway_pool ON
        updated_giveaway.id = giveaway_pool."giveawayId"
      LEFT JOIN giveaway_tokens giveaway_token ON
        updated_giveaway.id = giveaway_token."giveawayId"
      `,
      [giveawayId, totalAmount],
    );

    if (data?.length === 0) {
      throw new Error('The giveaway has no remaining funds.');
    }

    if (!data?.[0]?.poolOrTokenId) {
      throw new Error('The giveaway has no associated pool or token');
    }

    const { poolOrTokenId, isBlockchainPool } = data[0];

    if (isBlockchainPool) {
      const result = await transactionalEntityManager.query(
        `
        UPDATE "blockchain_pools"
        SET amount = ((amount::numeric) - ($2::numeric))::text
        WHERE id = $1 AND ($2::numeric <= amount::numeric)
        RETURNING id
        `,
        [poolOrTokenId, totalAmount],
      );

      if (result?.[1] !== 1) {
        throw new Error('The pool has no remaining funds.');
      }
    }
  }
}
