import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import '@polkadot/api-augment';
import { Keyring } from '@polkadot/keyring';
import { u8aToHex } from '@polkadot/util';
import {
  blake2AsHex,
  hdEthereum,
  mnemonicToLegacySeed,
} from '@polkadot/util-crypto';
import { KeypairType } from '@polkadot/util-crypto/types';
import { ProjectService } from '@root/project/project.service';
import * as crypto from 'crypto';

import { BlockchainType } from '@root/blockchain/blockchain.entity';
import { BlockchainService } from '@root/blockchain/blockchain.service';

@Injectable()
export class DotsamaAccountService {
  private mnemonic: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly blockchainService: BlockchainService,
    private readonly projectService: ProjectService,
  ) {
    const encryptionKey = this.configService.get('ENCRYPT_KEY');
    const iv = this.configService.get('ENCRYPT_IV');
    const dotsamaMn = this.configService.get('DOTSAMA_MN');

    if (
      !encryptionKey ||
      !iv ||
      !dotsamaMn ||
      !this.configService.get('DOTSAMA_SALT')
    )
      throw new Error('Invalid dotsama config');

    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(encryptionKey, 'base64'),
      Buffer.from(iv, 'base64'),
    );

    let decryptedData = decipher.update(dotsamaMn, 'base64', 'utf-8');
    decryptedData += decipher.final('utf-8');

    this.mnemonic = decryptedData;
  }

  private generateKeyPairHash(userId: string) {
    const length = userId.length;
    const halfLength = Math.floor(length / 2);

    const firstHalf = userId.slice(0, halfLength);
    const secondHalf = userId.slice(halfLength);

    const reversedFirstHalf = firstHalf.split('').reverse().join('');
    const reversedSecondHalf = secondHalf.split('').reverse().join('');

    const combinedReversed =
      reversedSecondHalf +
      this.configService.get('DOTSAMA_SALT') +
      reversedFirstHalf;

    return blake2AsHex(combinedReversed.toUpperCase());
  }

  generateKeyPair(
    userId: string,
    ss58Format: number,
    keyringType: KeypairType = 'sr25519',
  ) {
    if (!userId || userId.length < 4) throw new Error('Invalid userId');
    const keyring = new Keyring({ type: keyringType, ss58Format });

    return keyring.addFromUri(
      `${this.mnemonic}//${userId}///${this.generateKeyPairHash(userId)}`,
      { name: 'sr25519' },
    );
  }

  async getAddress(blockchainId: string, userId: string) {
    const blockchain = await this.blockchainService.findById(blockchainId, true);

    if (!blockchain || blockchain.type !== BlockchainType.DOTSAMA)
      throw new BadRequestException(
        'The requested blockchain is not supported.',
      );

    return this.generateKeyPair(
      userId,
      blockchain.chainId,
      blockchain.keyPairType || 'sr25519',
    ).address;
  }

  async getEVMAccount(projectId: string) {
    const project = await this.projectService.findOne({
      select: ['nonce'],
      where: {
        id: projectId,
      },
    });
    if (!project) throw new BadRequestException('Project not found');

    const keyring = new Keyring({ type: 'ethereum' });
    const ethDerPath = "m/44'/60'/0'/0/" + project.nonce;

    const keyringPair = keyring.addFromUri(`${this.mnemonic}/${ethDerPath}`);
    const privateKey = u8aToHex(
      hdEthereum(mnemonicToLegacySeed(this.mnemonic, '', false, 64), ethDerPath)
        .secretKey,
    );

    return {
      privateKey,
      address: keyringPair.address,
    };
  }

  async getEvmAddress(projectId: string) {
    const { address } = await this.getEVMAccount(projectId);
    return address;
  }
}
