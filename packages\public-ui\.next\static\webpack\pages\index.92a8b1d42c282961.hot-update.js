"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/UserProfile/UserMenu.tsx":
/*!*********************************************!*\
  !*** ./components/UserProfile/UserMenu.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @Hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @phosphor-icons/react */ \"./node_modules/@phosphor-icons/react/dist/index.es.js\");\n/* harmony import */ var next_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-i18next */ \"./node_modules/next-i18next/dist/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _PopoverMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PopoverMenu */ \"./components/PopoverMenu.tsx\");\n/* harmony import */ var _UserProfile_UserAvatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../UserProfile/UserAvatar */ \"./components/UserProfile/UserAvatar.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction UserMenu(param) {\n    let { me } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { t } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"translation\", {\n        keyPrefix: \"user-menu\"\n    });\n    const { t: globalT } = (0,next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(\"translation\", {\n        keyPrefix: \"global\"\n    });\n    const MENU_ITEMS = [\n        {\n            name: t(\"profile\"),\n            href: \"/\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                lineNumber: 26,\n                columnNumber: 13\n            }, this),\n            userRelative: true\n        },\n        {\n            name: t(\"participated-events\", {\n                events: globalT(\"event_many\")\n            }),\n            href: \"/campaigns\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Flask, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, this),\n            userRelative: true\n        },\n        {\n            name: t(\"followed-projects\", {\n                projects: globalT(\"project_many\")\n            }),\n            href: \"/\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Fire, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                lineNumber: 38,\n                columnNumber: 13\n            }, this),\n            userRelative: true\n        },\n        {\n            name: t(\"all-rewards\"),\n            href: \"/rewards\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Gift, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, this),\n            userRelative: true\n        },\n        {\n            name: t(\"settings\"),\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.Gear, {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                lineNumber: 49,\n                columnNumber: 13\n            }, this),\n            query: {\n                settings: \"all\"\n            }\n        }\n    ];\n    const extraTpl = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: \"https://docs.airlyft.one/\",\n            target: \"_blank\",\n            className: \"flow-root px-2 py-2 transition duration-150 ease-in-out rounded-md hover:component-bg focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-base font-medium text-ch\",\n                        children: t(\"tpl.title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"block text-sm text-cl\",\n                    children: t(\"tpl.subtitle\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n            lineNumber: 57,\n            columnNumber: 5\n        }, this);\n    const menuItemsTpl = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                MENU_ITEMS.map((param, key)=>{\n                    let { name, icon, href, userRelative, query } = param;\n                    if (Object.keys(query || {}).length > 0) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PopoverMenu__WEBPACK_IMPORTED_MODULE_5__.MenuItem, {\n                            name: name,\n                            icon: icon,\n                            onClick: ()=>{\n                                router.push({\n                                    pathname: href || router.pathname,\n                                    query: {\n                                        ...router.query,\n                                        ...query\n                                    }\n                                }, undefined, {\n                                    shallow: true\n                                });\n                            }\n                        }, key, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this);\n                    }\n                    const cHref = userRelative ? \"/user/\".concat(me.username).concat(href) : href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: cHref,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PopoverMenu__WEBPACK_IMPORTED_MODULE_5__.MenuItem, {\n                            name: name,\n                            icon: icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this)\n                    }, key, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this);\n                }),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PopoverMenu__WEBPACK_IMPORTED_MODULE_5__.MenuItem, {\n                    name: t(\"logout\"),\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phosphor_icons_react__WEBPACK_IMPORTED_MODULE_7__.SignOut, {\n                        className: \"w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 15\n                    }, void 0),\n                    onClick: _Hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.logout\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true);\n    const menuButtonTpl = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserProfile_UserAvatar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            size: \"small\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n            lineNumber: 111,\n            columnNumber: 31\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PopoverMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        menuButton: menuButtonTpl(),\n        menuItems: menuItemsTpl(),\n        extra: extraTpl()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\web development\\\\airlfyt\\\\airlyft-monorepo\\\\packages\\\\public-ui\\\\components\\\\UserProfile\\\\UserMenu.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n_s(UserMenu, \"3Qu2zTrYEubDP1VVAhjtSkPwaSs=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = UserMenu;\nvar _c;\n$RefreshReg$(_c, \"UserMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL1VzZXJQcm9maWxlL1VzZXJNZW51LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQVNUO0FBQ2U7QUFDakI7QUFDVztBQUNlO0FBQ0o7QUFFcEMsU0FBU2MsU0FBUyxLQUFvQjtRQUFwQixFQUFFQyxFQUFFLEVBQWdCLEdBQXBCOztJQUMvQixNQUFNQyxTQUFTTixzREFBU0E7SUFDeEIsTUFBTSxFQUFFTyxDQUFDLEVBQUUsR0FBR1QsNERBQWNBLENBQUMsZUFBZTtRQUFFVSxXQUFXO0lBQVk7SUFDckUsTUFBTSxFQUFFRCxHQUFHRSxPQUFPLEVBQUUsR0FBR1gsNERBQWNBLENBQUMsZUFBZTtRQUFFVSxXQUFXO0lBQVM7SUFFM0UsTUFBTUUsYUFBYTtRQUNqQjtZQUNFQyxNQUFNSixFQUFFO1lBQ1JLLE1BQU07WUFDTkMsb0JBQU0sOERBQUNoQix1REFBUUE7Z0JBQUNpQixXQUFVOzs7Ozs7WUFDMUJDLGNBQWM7UUFDaEI7UUFDQTtZQUNFSixNQUFNSixFQUFFLHVCQUF1QjtnQkFBRVMsUUFBUVAsUUFBUTtZQUFjO1lBQy9ERyxNQUFNO1lBQ05DLG9CQUFNLDhEQUFDckIsd0RBQUtBO2dCQUFDc0IsV0FBVTs7Ozs7O1lBQ3ZCQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRUosTUFBTUosRUFBRSxxQkFBcUI7Z0JBQUVVLFVBQVVSLFFBQVE7WUFBZ0I7WUFDakVHLE1BQU07WUFDTkMsb0JBQU0sOERBQUN0Qix1REFBSUE7Z0JBQUN1QixXQUFVOzs7Ozs7WUFDdEJDLGNBQWM7UUFDaEI7UUFDQTtZQUNFSixNQUFNSixFQUFFO1lBQ1JLLE1BQU07WUFDTkMsb0JBQU0sOERBQUNuQix1REFBSUE7Z0JBQUNvQixXQUFVOzs7Ozs7WUFDdEJDLGNBQWM7UUFDaEI7UUFDQTtZQUNFSixNQUFNSixFQUFFO1lBQ1JNLG9CQUFNLDhEQUFDcEIsdURBQUlBO2dCQUFDcUIsV0FBVTs7Ozs7O1lBQ3RCSSxPQUFPO2dCQUNMQyxVQUFVO1lBQ1o7UUFDRjtLQUNEO0lBRUQsTUFBTUMsV0FBVyxrQkFDZiw4REFBQ0M7WUFDQ1QsTUFBSztZQUNMVSxRQUFPO1lBQ1BSLFdBQVU7OzhCQUVWLDhEQUFDUztvQkFBS1QsV0FBVTs4QkFDZCw0RUFBQ1M7d0JBQUtULFdBQVU7a0NBQWlDUCxFQUFFOzs7Ozs7Ozs7Ozs4QkFFckQsOERBQUNnQjtvQkFBS1QsV0FBVTs4QkFBeUJQLEVBQUU7Ozs7Ozs7Ozs7OztJQUkvQyxNQUFNaUIsZUFBZSxrQkFDbkI7O2dCQUNHZCxXQUFXZSxHQUFHLENBQUMsUUFBNENDO3dCQUEzQyxFQUFFZixJQUFJLEVBQUVFLElBQUksRUFBRUQsSUFBSSxFQUFFRyxZQUFZLEVBQUVHLEtBQUssRUFBRTtvQkFDeEQsSUFBSVMsT0FBT0MsSUFBSSxDQUFDVixTQUFTLENBQUMsR0FBR1csTUFBTSxHQUFHLEdBQUc7d0JBQ3ZDLHFCQUNFLDhEQUFDM0Isa0RBQVFBOzRCQUNQUyxNQUFNQTs0QkFDTkUsTUFBTUE7NEJBRU5pQixTQUFTO2dDQUNQeEIsT0FBT3lCLElBQUksQ0FDVDtvQ0FDRUMsVUFBVXBCLFFBQVFOLE9BQU8wQixRQUFRO29DQUNqQ2QsT0FBTzt3Q0FDTCxHQUFHWixPQUFPWSxLQUFLO3dDQUNmLEdBQUdBLEtBQUs7b0NBQ1Y7Z0NBQ0YsR0FDQWUsV0FDQTtvQ0FBRUMsU0FBUztnQ0FBSzs0QkFFcEI7MkJBYktSOzs7OztvQkFnQlg7b0JBRUEsTUFBTVMsUUFBUXBCLGVBQWUsU0FBdUJILE9BQWRQLEdBQUcrQixRQUFRLEVBQVEsT0FBTHhCLFFBQVNBO29CQUM3RCxxQkFDRSw4REFBQ2Isa0RBQUlBO3dCQUFDYSxNQUFNdUI7a0NBQ1YsNEVBQUNqQyxrREFBUUE7NEJBQUNTLE1BQU1BOzRCQUFNRSxNQUFNQTs7Ozs7O3VCQUROYTs7Ozs7Z0JBSTVCOzhCQUVBLDhEQUFDeEIsa0RBQVFBO29CQUNQUyxNQUFNSixFQUFFO29CQUNSTSxvQkFBTSw4REFBQ2xCLDBEQUFPQTt3QkFBQ21CLFdBQVU7Ozs7OztvQkFDekJnQixTQUFTeEMsa0RBQU1BOzs7Ozs7OztJQUtyQixNQUFNK0MsZ0JBQWdCLGtCQUFNLDhEQUFDbEMsK0RBQVVBO1lBQUNtQyxNQUFLOzs7Ozs7SUFFN0MscUJBQ0UsOERBQUNyQyxvREFBV0E7UUFDVnNDLFlBQVlGO1FBQ1pHLFdBQVdoQjtRQUNYaUIsT0FBT3JCOzs7Ozs7QUFHYjtHQXZHd0JoQjs7UUFDUEosa0RBQVNBO1FBQ1ZGLHdEQUFjQTtRQUNMQSx3REFBY0E7OztLQUhmTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL1VzZXJQcm9maWxlL1VzZXJNZW51LnRzeD8wM2IyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGxvZ291dCB9IGZyb20gJ0BIb29rcy91c2VBdXRoJztcclxuaW1wb3J0IHsgVXNlciB9IGZyb20gJ0BhaXJseWZ0L3R5cGVzJztcclxuaW1wb3J0IHtcclxuICBGaXJlLFxyXG4gIEZsYXNrLFxyXG4gIEdlYXIsXHJcbiAgR2lmdCxcclxuICBTaWduT3V0LFxyXG4gIFVzZXIgYXMgVXNlckljb24sXHJcbn0gZnJvbSAnQHBob3NwaG9yLWljb25zL3JlYWN0JztcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICduZXh0LWkxOG5leHQnO1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCBQb3BvdmVyTWVudSwgeyBNZW51SXRlbSB9IGZyb20gJy4uL1BvcG92ZXJNZW51JztcclxuaW1wb3J0IFVzZXJBdmF0YXIgZnJvbSAnLi4vVXNlclByb2ZpbGUvVXNlckF2YXRhcic7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBVc2VyTWVudSh7IG1lIH06IHsgbWU6IFVzZXIgfSkge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oJ3RyYW5zbGF0aW9uJywgeyBrZXlQcmVmaXg6ICd1c2VyLW1lbnUnIH0pO1xyXG4gIGNvbnN0IHsgdDogZ2xvYmFsVCB9ID0gdXNlVHJhbnNsYXRpb24oJ3RyYW5zbGF0aW9uJywgeyBrZXlQcmVmaXg6ICdnbG9iYWwnIH0pO1xyXG5cclxuICBjb25zdCBNRU5VX0lURU1TID0gW1xyXG4gICAge1xyXG4gICAgICBuYW1lOiB0KCdwcm9maWxlJyksXHJcbiAgICAgIGhyZWY6ICcvJyxcclxuICAgICAgaWNvbjogPFVzZXJJY29uIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPixcclxuICAgICAgdXNlclJlbGF0aXZlOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogdCgncGFydGljaXBhdGVkLWV2ZW50cycsIHsgZXZlbnRzOiBnbG9iYWxUKCdldmVudF9tYW55JykgfSksXHJcbiAgICAgIGhyZWY6ICcvY2FtcGFpZ25zJyxcclxuICAgICAgaWNvbjogPEZsYXNrIGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPixcclxuICAgICAgdXNlclJlbGF0aXZlOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogdCgnZm9sbG93ZWQtcHJvamVjdHMnLCB7IHByb2plY3RzOiBnbG9iYWxUKCdwcm9qZWN0X21hbnknKSB9KSxcclxuICAgICAgaHJlZjogJy8nLFxyXG4gICAgICBpY29uOiA8RmlyZSBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz4sXHJcbiAgICAgIHVzZXJSZWxhdGl2ZTogdHJ1ZSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIG5hbWU6IHQoJ2FsbC1yZXdhcmRzJyksXHJcbiAgICAgIGhyZWY6ICcvcmV3YXJkcycsXHJcbiAgICAgIGljb246IDxHaWZ0IGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPixcclxuICAgICAgdXNlclJlbGF0aXZlOiB0cnVlLFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbmFtZTogdCgnc2V0dGluZ3MnKSxcclxuICAgICAgaWNvbjogPEdlYXIgY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+LFxyXG4gICAgICBxdWVyeToge1xyXG4gICAgICAgIHNldHRpbmdzOiAnYWxsJyxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgZXh0cmFUcGwgPSAoKSA9PiAoXHJcbiAgICA8YVxyXG4gICAgICBocmVmPVwiaHR0cHM6Ly9kb2NzLmFpcmx5ZnQub25lL1wiXHJcbiAgICAgIHRhcmdldD1cIl9ibGFua1wiXHJcbiAgICAgIGNsYXNzTmFtZT1cImZsb3ctcm9vdCBweC0yIHB5LTIgdHJhbnNpdGlvbiBkdXJhdGlvbi0xNTAgZWFzZS1pbi1vdXQgcm91bmRlZC1tZCBob3Zlcjpjb21wb25lbnQtYmcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZyBmb2N1cy12aXNpYmxlOnJpbmctb3JhbmdlLTUwMCBmb2N1cy12aXNpYmxlOnJpbmctb3BhY2l0eS01MFwiXHJcbiAgICA+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1iYXNlIGZvbnQtbWVkaXVtIHRleHQtY2hcIj57dCgndHBsLnRpdGxlJyl9PC9zcGFuPlxyXG4gICAgICA8L3NwYW4+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gdGV4dC1jbFwiPnt0KCd0cGwuc3VidGl0bGUnKX08L3NwYW4+XHJcbiAgICA8L2E+XHJcbiAgKTtcclxuXHJcbiAgY29uc3QgbWVudUl0ZW1zVHBsID0gKCkgPT4gKFxyXG4gICAgPD5cclxuICAgICAge01FTlVfSVRFTVMubWFwKCh7IG5hbWUsIGljb24sIGhyZWYsIHVzZXJSZWxhdGl2ZSwgcXVlcnkgfSwga2V5KSA9PiB7XHJcbiAgICAgICAgaWYgKE9iamVjdC5rZXlzKHF1ZXJ5IHx8IHt9KS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8TWVudUl0ZW1cclxuICAgICAgICAgICAgICBuYW1lPXtuYW1lfVxyXG4gICAgICAgICAgICAgIGljb249e2ljb259XHJcbiAgICAgICAgICAgICAga2V5PXtrZXl9XHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgcm91dGVyLnB1c2goXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICBwYXRobmFtZTogaHJlZiB8fCByb3V0ZXIucGF0aG5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHtcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnJvdXRlci5xdWVyeSxcclxuICAgICAgICAgICAgICAgICAgICAgIC4uLnF1ZXJ5LFxyXG4gICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIHVuZGVmaW5lZCxcclxuICAgICAgICAgICAgICAgICAgeyBzaGFsbG93OiB0cnVlIH0sXHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICApO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgY0hyZWYgPSB1c2VyUmVsYXRpdmUgPyBgL3VzZXIvJHttZS51c2VybmFtZX0ke2hyZWZ9YCA6IGhyZWYhO1xyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICA8TGluayBocmVmPXtjSHJlZn0ga2V5PXtrZXl9PlxyXG4gICAgICAgICAgICA8TWVudUl0ZW0gbmFtZT17bmFtZX0gaWNvbj17aWNvbn0gLz5cclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICApO1xyXG4gICAgICB9KX1cclxuXHJcbiAgICAgIDxNZW51SXRlbVxyXG4gICAgICAgIG5hbWU9e3QoJ2xvZ291dCcpfVxyXG4gICAgICAgIGljb249ezxTaWduT3V0IGNsYXNzTmFtZT1cInctNiBoLTZcIiAvPn1cclxuICAgICAgICBvbkNsaWNrPXtsb2dvdXR9XHJcbiAgICAgIC8+XHJcbiAgICA8Lz5cclxuICApO1xyXG5cclxuICBjb25zdCBtZW51QnV0dG9uVHBsID0gKCkgPT4gPFVzZXJBdmF0YXIgc2l6ZT1cInNtYWxsXCIgLz47XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8UG9wb3Zlck1lbnVcclxuICAgICAgbWVudUJ1dHRvbj17bWVudUJ1dHRvblRwbCgpfVxyXG4gICAgICBtZW51SXRlbXM9e21lbnVJdGVtc1RwbCgpfVxyXG4gICAgICBleHRyYT17ZXh0cmFUcGwoKX1cclxuICAgIC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsibG9nb3V0IiwiRmlyZSIsIkZsYXNrIiwiR2VhciIsIkdpZnQiLCJTaWduT3V0IiwiVXNlciIsIlVzZXJJY29uIiwidXNlVHJhbnNsYXRpb24iLCJMaW5rIiwidXNlUm91dGVyIiwiUG9wb3Zlck1lbnUiLCJNZW51SXRlbSIsIlVzZXJBdmF0YXIiLCJVc2VyTWVudSIsIm1lIiwicm91dGVyIiwidCIsImtleVByZWZpeCIsImdsb2JhbFQiLCJNRU5VX0lURU1TIiwibmFtZSIsImhyZWYiLCJpY29uIiwiY2xhc3NOYW1lIiwidXNlclJlbGF0aXZlIiwiZXZlbnRzIiwicHJvamVjdHMiLCJxdWVyeSIsInNldHRpbmdzIiwiZXh0cmFUcGwiLCJhIiwidGFyZ2V0Iiwic3BhbiIsIm1lbnVJdGVtc1RwbCIsIm1hcCIsImtleSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJvbkNsaWNrIiwicHVzaCIsInBhdGhuYW1lIiwidW5kZWZpbmVkIiwic2hhbGxvdyIsImNIcmVmIiwidXNlcm5hbWUiLCJtZW51QnV0dG9uVHBsIiwic2l6ZSIsIm1lbnVCdXR0b24iLCJtZW51SXRlbXMiLCJleHRyYSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/UserProfile/UserMenu.tsx\n"));

/***/ })

});