import { gql, useQuery } from '@apollo/client';
import { User } from '@airlyft/types';

export const GET_ME = gql`
  query GetMe {
    me {
      id
      firstName
      lastName
      email
      onboarded
      username
      auth {
        providerId
        firstName
        lastName
        picture
        username
        provider
        isPrimary
      }
    }
  }
`;

export function useUserDetails() {
  return useQuery<{ me: User }>(GET_ME);
}
