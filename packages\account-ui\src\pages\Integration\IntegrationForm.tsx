import React, { useContext, useState } from 'react';
import { Col, Form, Input, Row } from 'antd';
import { NotificationContext } from '@Pages/App';
import {
  Integration,
  IntegrationPlatformConfig,
  IntegrationType,
  PlatformField,
} from '@airlyft/types';
import { Rule } from 'antd/es/form';
import StyledButton from '@Components/StyledButton';
import { firstOrNull } from '@Root/utils/arrayUtils';
import {
  useCreateApiKeyIntegration,
  useUpdateApiKeyIntegration,
} from './gql/api-key-integrations.gql';
import IntegrationDisplay from './IntegrationDisplay';

interface IntegrationFormProps {
  platform: IntegrationPlatformConfig;
  integrationData: Integration | null;
  projectId: string;
  onCancel: () => void;
  onMutate?: (id: string) => void;
}

const IntegrationForm: React.FC<IntegrationFormProps> = ({
  platform,
  projectId,
  integrationData,
  onCancel,
  onMutate,
}) => {
  const [form] = Form.useForm();
  const { api } = useContext(NotificationContext);
  const [isEditing, setIsEditing] = useState(false);
  const [createApiKeyIntegration, { loading: creating }] =
    useCreateApiKeyIntegration();
  const [updateApiKeyIntegration, { loading: updating }] =
    useUpdateApiKeyIntegration();

  const inputArgs = (values: any) => {
    const args: string[] = [];
    platform.fields?.forEach((field) => {
      args.push(values[field.name]);
    });
    return args;
  };

  const create = (values: any) => {
    createApiKeyIntegration({
      variables: {
        projectId,
        integrationName: platform.platformType,
        displayName: values.displayName,
        data: {
          name: platform.platformType,
          type: IntegrationType.API_KEY_INTEGRATION,
          data: {
            args: inputArgs(values),
          },
        },
      },
      onCompleted: ({ createApiKeyIntegration }) => {
        const id = firstOrNull(createApiKeyIntegration?.identifiers)?.id;
        if (id) {
          api?.success({
            message: 'Success',
            description: 'Integration created successfully',
          });
          onCancel();
          onMutate?.(id);
        } else {
          api?.error({
            message: 'Failed',
            description: 'Could not create the api key',
          });
        }
      },
      onError: (error) => {
        api?.error({
          message: 'Failed',
          description: 'Could not create the api key',
        });
      },
    });
  };

  const update = (values: any) => {
    if (!integrationData?.id) return;
    updateApiKeyIntegration({
      variables: {
        integrationName: platform.platformType,
        displayName: values.displayName,
        integrationId: integrationData?.id,
        projectId,
        data: {
          data: {
            args: inputArgs(values),
          },
        },
      },
      onCompleted: () => {
        api?.success({
          message: 'Success',
          description: 'Updated the api key',
        });
        onCancel();
      },
      onError: (error) => {
        api?.error({
          message: 'Failed',
          description: 'Could not update the api key',
        });
      },
    });
  };

  const onFinish = (values: any) => {
    integrationData?.id ? update(values) : create(values);
  };

  return (
    <Form
      layout="vertical"
      onFinish={onFinish}
      form={form}
      initialValues={{
        displayName: integrationData?.displayName,
      }}
    >
      {integrationData ? (
        isEditing ? (
          <FormInputs platform={platform} />
        ) : (
          <IntegrationDisplay
            handleEditIntegration={() => setIsEditing(true)}
          />
        )
      ) : (
        <FormInputs platform={platform} />
      )}

      <Form.Item>
        <Row justify="end" gutter={[16, 16]}>
          <Col>
            <StyledButton type="default" size="large" onClick={onCancel}>
              Cancel
            </StyledButton>
          </Col>
          {(!integrationData || isEditing) && (
            <Col>
              <StyledButton
                loading={creating || updating}
                disabled={creating || updating}
                type="primary"
                htmlType="submit"
                size="large"
              >
                {integrationData?.id ? 'Update' : 'Create'}
              </StyledButton>
            </Col>
          )}
        </Row>
      </Form.Item>
    </Form>
  );
};

const FormInputs = ({ platform }: { platform: IntegrationPlatformConfig }) => {
  return (
    <>
      <Form.Item
        key="displayName"
        style={{ marginTop: '10px' }}
        label="Integration Name"
        name="displayName"
        rules={[{ required: true, message: 'Please provide a name' }]}
      >
        <Input size="large" />
      </Form.Item>
      {platform?.fields?.map((field: PlatformField) => {
        const rules: Rule[] = [
          {
            required: true,
            message: `Please provide ${field.label}`,
          },
        ];

        if (field.regex && field.regex.trim()) {
          const regexPattern = field.regex.replace(/^\/|\/$/g, '');
          rules.push({
            pattern: new RegExp(regexPattern),
            message: `Invalid ${field.label} format`,
          });
        }

        return (
          <Form.Item
            key={field.name}
            style={{ marginTop: '10px' }}
            label={field.label}
            name={field.name}
            rules={rules}
          >
            <Input
              {...(field.placeholder && { placeholder: field.placeholder })}
              size="large"
            />
          </Form.Item>
        );
      })}
    </>
  );
};

export default IntegrationForm;
