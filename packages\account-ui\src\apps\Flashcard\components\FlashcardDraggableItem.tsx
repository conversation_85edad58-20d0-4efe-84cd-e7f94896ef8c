import Box from '@Components/Box';
import { Droppable } from '@Components/Droppable';
import { FlashcardSection } from '@airlyft/types';
import { DeleteFilled, DragOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Empty, List, Popconfirm, Switch, Typography } from 'antd';
import { DragDropContext, Draggable } from 'react-beautiful-dnd';
import styled from 'styled-components';
import FlashcardSectionItem from './FlashcardSectionItem';

// Extended FlashcardSection interface with hidden property
interface ExtendedFlashcardSection extends FlashcardSection {
  hidden?: boolean;
}

type DraggableListProps = {
  items: Array<ExtendedFlashcardSection>;
  onEdit: (item: ExtendedFlashcardSection) => void;
  onOrderChange: (items: Array<ExtendedFlashcardSection>) => void;
  onRemove: (item: ExtendedFlashcardSection) => void;
  onHiddenToggle: (item: ExtendedFlashcardSection, status: boolean) => void;
  disabled?: boolean;
  dbItems?: Array<ExtendedFlashcardSection>;
};

const ListWrapper = styled.div`
  .ant-list-empty-text {
    display: none;
  }
`;

function FlashcardDraggableItem(props: DraggableListProps) {
  const reorder = (list: Array<any>, startIndex: number, endIndex: number) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };

  const onDragEnd = (result: any) => {
    // Don't do anything if dropped outside the list
    if (!result.destination) {
      return;
    }

    // Don't do anything if dropped at the same position
    if (result.destination.index === result.source.index) {
      return;
    }

    const newOrder = reorder(
      props.items,
      result.source.index,
      result.destination.index,
    );

    props.onOrderChange(newOrder);
  };

  const isDisabled = (item: ExtendedFlashcardSection) => {
    return !!props.dbItems?.find((i) => i.id == item.id);
  };

  if (props.items?.length == 0) {
    return (
      <Empty
        imageStyle={{ height: 60 }}
        description={
          <Typography.Text type="secondary">
            This flashcard does not have any sections yet!
          </Typography.Text>
        }
      />
    );
  }

  const safeString = (value: any): string | undefined => {
    if (value === null || value === undefined) return undefined;
    return value;
  };

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="list">
        {(provided) => (
          <div ref={provided.innerRef} {...provided.droppableProps}>
            <ListWrapper>
              <List
                dataSource={props.items || []}
                renderItem={(item) => (
                  <Draggable
                    draggableId={item.id}
                    index={item.order}
                    key={item.id}
                    isDragDisabled={props.disabled}
                  >
                    {(provided) => (
                      <Box
                        bordered
                        radius="md"
                        px={3}
                        py={2}
                        mb={2}
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <List.Item
                          style={{
                            paddingTop: 0,
                          }}
                          actions={
                            props.disabled
                              ? []
                              : [
                                  <Popconfirm
                                    title="Are you sure delete this section?"
                                    onConfirm={() => props.onRemove(item)}
                                    okText="Yes"
                                    cancelText="No"
                                    disabled={isDisabled(item)}
                                    okButtonProps={{
                                      disabled: false,
                                    }}
                                    cancelButtonProps={{
                                      disabled: false,
                                    }}
                                    key="delete"
                                  >
                                    <Button
                                      icon={<DeleteFilled />}
                                      type="link"
                                      disabled={isDisabled(item)}
                                      size="small"
                                    />
                                  </Popconfirm>,
                                  <Switch
                                    disabled={false}
                                    size="small"
                                    value={!(item.hidden || false)}
                                    onChange={(e) =>
                                      props.onHiddenToggle(item, e)
                                    }
                                  />,
                                  <Button
                                    onClick={() => props.onEdit(item)}
                                    icon={<EditOutlined />}
                                    type="link"
                                    disabled={false}
                                    key="edit"
                                    size="small"
                                  />,
                                  <DragOutlined key="drag" />,
                                ]
                          }
                        >
                          <FlashcardSectionItem
                            item={{
                              id: item.id,
                              order: item.order,
                              content: safeString(item.content),
                              hidden: item.hidden,
                            }}
                            style={{
                              margin: 0,
                              paddingRight: '5px',
                              maxHeight: '200px',
                              overflowY: 'auto',
                              scrollbarWidth: 'thin',
                              scrollbarColor: 'darkgray transparent',
                            }}
                          />
                        </List.Item>
                      </Box>
                    )}
                  </Draggable>
                )}
              />
            </ListWrapper>
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}

export default FlashcardDraggableItem;
