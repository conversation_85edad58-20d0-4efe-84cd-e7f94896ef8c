import MetamaskIcon from '@Components/SocialIcons/Metamask';
import PolkadotJsIcon from '@Components/SocialIcons/Polkadotjs';
import PolkadotIcon from '@Components/SocialIcons/Polkadot';
import SubsocialIcon from '@Components/SocialIcons/Subsocial';
import SubWalletIcon from '@Components/SocialIcons/Subwallet';
import Twitter<PERSON>ogo from '@Components/SocialIcons/Twitter';
import TalismanWalletIcon from '@Components/SocialIcons/Talisman';
import WalletConnectIcon from '@Components/SocialIcons/WalletConnect';
import NovaWalletIcon from '@Components/SocialIcons/NovaWallet';
import EthIcon from '@Components/SocialIcons/Eth';
import LuckydrawIcon from '@Components/SocialIcons/Luckydraw';
import ProducthuntIcon from '@Components/SocialIcons/Producthunt';
import SlotMachineIcon from '@Components/SocialIcons/SlotMachine';
import MysteryBoxIcon from '@Components/SocialIcons/MysteryBox';
import KickstarterIcon from '@Components/SocialIcons/Kickstarter';
import {
  AppType,
  AuthProvider,
  TaskType,
  Web3WalletType,
} from '@airlyft/types';
import { MailIcon } from '@heroicons/react/solid';
import {
  CheckSquareOffset,
  CodesandboxLogo,
  DiscordLogo,
  EnvelopeSimple,
  Gift,
  GlobeHemisphereEast,
  Icon,
  InstagramLogo,
  NotePencil,
  PaperPlaneRight,
  Plugs,
  PuzzlePiece,
  RocketLaunch,
  ShareNetwork,
  StarFour,
  TelegramLogo,
  DeviceMobile,
  UploadSimple,
  Wallet,
  YoutubeLogo,
  Key,
  Article,
  CalendarCheck,
} from '@phosphor-icons/react';
import React, { SVGProps } from 'react';
import { ExternalAppType, ExternalTaskType } from '../app-store.types';

export type AppStoreIconKey =
  | keyof typeof AuthProvider
  | keyof typeof AppType
  | keyof typeof TaskType
  | keyof typeof Web3WalletType
  | keyof typeof ExternalAppType
  | keyof typeof ExternalTaskType;

const AppStoreIconMap: Partial<{
  [k in AppStoreIconKey]:
    | React.ReactNode
    | Icon
    | ((props: SVGProps<SVGSVGElement>) => JSX.Element);
}> = {
  [AppType.TWITTER]: TwitterLogo,
  [AppType.UPLOAD]: UploadSimple,
  [AppType.DISCORD]: DiscordLogo,
  [AppType.TELEGRAM]: TelegramLogo,
  [AppType.INSTAGRAM]: InstagramLogo,
  [AppType.SUBSOCIAL]: SubsocialIcon,
  [AppType.YOUTUBE]: YoutubeLogo,
  [AppType.EVM]: CodesandboxLogo,
  [AppType.FORM]: NotePencil,
  [AppType.QUIZ]: PuzzlePiece,
  [AppType.SUBGRAPH]: StarFour,
  [AppType.REST]: Plugs,
  [AppType.AIRBOOST]: PaperPlaneRight,
  [AppType.EMAIL]: EnvelopeSimple,
  [AppType.TERMS]: CheckSquareOffset,
  [TaskType.QUIZ_PLAY]: PuzzlePiece,
  [TaskType.EVM_CONTRACT]: CodesandboxLogo,
  [TaskType.UPLOAD_FILE]: UploadSimple,
  [TaskType.FORM_ANSWER]: NotePencil,
  [TaskType.DISCORD_JOIN]: DiscordLogo,
  [TaskType.TELEGRAM_JOIN]: TelegramLogo,
  [TaskType.INSTAGRAM_VISIT]: InstagramLogo,
  [TaskType.INSTAGRAM_VIEW]: InstagramLogo,
  [TaskType.URL_SHARE]: ShareNetwork,
  [TaskType.URL_VISIT]: GlobeHemisphereEast,
  [TaskType.TWITTER_FOLLOW]: TwitterLogo,
  [TaskType.TWITTER_LIKE]: TwitterLogo,
  [TaskType.TWITTER_LIKE_RETWEET]: TwitterLogo,
  [TaskType.TWITTER_POST]: TwitterLogo,
  [TaskType.TWITTER_RETWEET]: TwitterLogo,
  [TaskType.TWITTER_UGC]: TwitterLogo,
  [TaskType.TWITTER_WHITELIST]: TwitterLogo,
  [TaskType.YOUTUBE_VISIT]: YoutubeLogo,
  [TaskType.YOUTUBE_SHARE]: YoutubeLogo,
  [TaskType.SUBSOCIAL_COMMENT]: SubsocialIcon,
  [TaskType.SUBSOCIAL_FOLLOW]: SubsocialIcon,
  [TaskType.SUBSOCIAL_POST]: SubsocialIcon,
  [TaskType.SUBSOCIAL_PROFILE]: SubsocialIcon,
  [TaskType.SUBSOCIAL_SHARE]: SubsocialIcon,
  [TaskType.SUBSOCIAL_SPACE]: SubsocialIcon,
  [TaskType.SUBSOCIAL_UPVOTE]: SubsocialIcon,
  [TaskType.TERMS_TEXT]: CheckSquareOffset,
  [TaskType.TERMS_EVM]: CheckSquareOffset,
  [TaskType.TERMS_DOTSAMA]: CheckSquareOffset,
  [AuthProvider.EVM_BLOCKCHAIN]: MetamaskIcon,
  [AuthProvider.MAGIC_LINK]: MailIcon,
  [Web3WalletType.EVM_METAMASK]: MetamaskIcon,
  [Web3WalletType.EVM_SUBWALLET]: SubWalletIcon,
  [Web3WalletType.EVM_TALISMAN]: TalismanWalletIcon,
  [Web3WalletType.EVM_WALLET_CONNECT]: WalletConnectIcon,
  [Web3WalletType.EVM_MANUAL]: NotePencil,
  [Web3WalletType.DOTSAMA_POLKADOT_JS]: PolkadotJsIcon,
  [Web3WalletType.DOTSAMA_TALISMAN]: TalismanWalletIcon,
  [Web3WalletType.DOTSAMA_SUBWALLET]: SubWalletIcon,
  [Web3WalletType.DOTSAMA_NOVA]: NovaWalletIcon,
  [Web3WalletType.DOTSAMA_MANUAL]: NotePencil,
  [TaskType.WALLET_EVM]: Wallet,
  [TaskType.WALLET_DOTSAMA]: Wallet,
  [TaskType.SUBGRAPH_RAW]: StarFour,
  [TaskType.REST_RAW]: Plugs,
  [TaskType.REST_EVM]: Plugs,
  [TaskType.REST_DOTSAMA]: Plugs,
  [ExternalAppType.CLAIM]: Gift,
  [ExternalTaskType.CLAIM_REWARD]: Gift,
  [TaskType.AIRBOOST_REFERRAL]: PaperPlaneRight,
  [TaskType.EMAIL_ADDRESS]: EnvelopeSimple,
  [TaskType.EMAIL_WHITELIST]: EnvelopeSimple,
  [TaskType.EMAIL_SUBSCRIBE]: EnvelopeSimple,
  [AppType.FAUCET]: PolkadotIcon,
  [TaskType.FAUCET_DOTSAMA]: PolkadotIcon,
  [TaskType.FAUCET_EVM]: EthIcon,
  [AppType.SUBSTRATE]: PolkadotIcon,
  [TaskType.SUBSTRATE_QUERY]: PolkadotIcon,
  [TaskType.SUBSTRATE_ASSET_BALANCE]: PolkadotIcon,
  [TaskType.SUBSTRATE_HOLD_NFT]: PolkadotIcon,
  [TaskType.SUBSTRATE_BALANCE]: PolkadotIcon,
  [TaskType.SUBSTRATE_STAKE]: PolkadotIcon,
  [TaskType.AIRQUEST_FOLLOW]: RocketLaunch,
  [TaskType.LUCKYDRAW_PLAY]: LuckydrawIcon,
  [TaskType.LUCKYDRAW_SLOT]: SlotMachineIcon,
  [TaskType.LUCKYDRAW_BOX]: MysteryBoxIcon,
  [AppType.MOBILE_APP]: DeviceMobile,
  [TaskType.MOBILE_APP_INSTALL]: DeviceMobile,
  [AppType.SECRET_CODE]: Key,
  [TaskType.SECRET_CODE_VALIDATE]: Key,
  [AppType.PRODUCTHUNT]: ProducthuntIcon,
  [TaskType.PRODUCTHUNT_UPVOTE]: ProducthuntIcon,
  [AppType.BLOG]: NotePencil,
  [TaskType.BLOG_COMMENT]: NotePencil,
  [TaskType.PRODUCTHUNT_FOLLOW]: ProducthuntIcon,
  [AppType.KICKSTARTER]: KickstarterIcon,
  [TaskType.KICKSTARTER_SUPPORT]: KickstarterIcon,
  [TaskType.BLOG_WRITE]: Article,
  [TaskType.CHECKIN_DAILY]: CalendarCheck,
};

interface IconProps {
  className?: string;
  style?: React.CSSProperties;
  color?: string;
}

interface AppStoreIconProps extends IconProps {
  iconKey: AppStoreIconKey;
  url?: string;
  weight?: string;
}

export default function AppStoreIconRenderer({
  iconKey,
  url,
  ...props
}: AppStoreIconProps) {
  if (url) {
    return <img src={url} className={props.className || 'h-6 w-6'} />;
  }

  const Icon = AppStoreIconMap[iconKey] as any;

  if (!Icon) return <></>;

  return <Icon {...props} weight="regular" />;
}
