import gqlClient from '@Graphql/graphql-client';
import { PublicUser, Query_userArgs } from '@airlyft/types';
import { gql } from '@apollo/client';

export const GET_USER_BY_USERNAME = gql`
  query GetUserUserByUsername($username: String!) {
    user(username: $username) {
      id
      createdAt
      firstName
      lastName
      username
      rep
    }
  }
`;

export function getUserById(userId: string) {
  return gqlClient.query<{ user: PublicUser }, Query_userArgs>({
    query: GET_USER_BY_USERNAME,
    variables: {
      userId,
    },
  });
}
