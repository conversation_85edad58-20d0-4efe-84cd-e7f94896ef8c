import { Blockchain, BlockchainAsset, Web3WalletType } from '@airlyft/types';
import { formatAmount, formatAmountToBase } from '@airlyft/web3-evm';
import {
  DotsamaConnectorData,
  ExtensionInterface,
} from '@airlyft/web3-evm-hooks';
import { EditOutlined } from '@ant-design/icons';
import StyledButton from '@Components/StyledButton';
import DotsamaWallet from '@Components/Web3Wallet/Dotsama/DotsamaWallet';
import useSubstrateApi from '@Root/apps/Substrate/useSubstrateApi';
import { Flex, Form, FormInstance, Input, Skeleton } from 'antd';
import { useState } from 'react';
import { AirPoolTx } from '../airpools.slice';
import AmountInput from '../components/AmountInput';
import { BigNumber } from 'ethers';

export default function WalletAsset({
  selectedBlockchain,
  selectedAsset,
  dotsamaWalletAddress,
  form,
  submitButtonText,
  poolId,
  handleSubmit,
  onCompleted,
}: Readonly<{
  selectedBlockchain: Blockchain;
  selectedAsset: BlockchainAsset;
  dotsamaWalletAddress: string;
  form: FormInstance<any>;
  submitButtonText: string;
  poolId?: string;
  handleSubmit?: () => Promise<void>;
  onCompleted?: (data: AirPoolTx) => void;
}>) {
  const amount = Form.useWatch('amount', form);
  const [balance, setBalance] = useState<string>();
  const [loadingBalance, setLoadingBalance] = useState(false);
  const [account, setAccount] = useState<string>();
  const [connector, setConnector] = useState<ExtensionInterface>();
  const { api: substrateApi, loading: substarteApiLoading } = useSubstrateApi(
    selectedBlockchain?.rpcUrls,
  );
  const [editAccount, setEditAccount] = useState(true);
  const getBalance = async (connectorData: DotsamaConnectorData) => {
    const { account, connector } = connectorData;
    if (!substrateApi || !account || !selectedAsset) return;
    setAccount(account);
    setConnector(connector);
    setLoadingBalance(true);
    try {
      if (selectedAsset?.tokenId === '-1') {
        const { data: balance } = await substrateApi.query.system.account(
          account,
        );
        const existentialDeposit = await substrateApi.consts.balances
          .existentialDeposit;
        const maxTemp = balance?.free.sub(existentialDeposit);
        const tx = substrateApi.tx.balances.transferKeepAlive(
          dotsamaWalletAddress,
          formatAmountToBase(maxTemp.toString(), selectedAsset.decimals),
        );
        const info = await tx.paymentInfo(dotsamaWalletAddress);
        setBalance(maxTemp.sub(info.partialFee).toString());
      } else {
        const data = await substrateApi.query.assets.account(
          selectedAsset.tokenId,
          account,
        );
        const metadata = await substrateApi.query.assets.asset(
          selectedAsset.tokenId,
        );
        const assetDetails = metadata.unwrap();
        const minBalance = assetDetails.minBalance;
        const bnExistentail = BigNumber.from(minBalance.toString()).mul(
          BigNumber.from(Math.pow(10, selectedAsset.decimals).toString()),
        );
        const max = BigNumber.from(data?.value?.balance?.toString() ?? '0').sub(
          bnExistentail,
        );
        setBalance(max.toString());
      }
      setLoadingBalance(false);
      setEditAccount(false);
    } catch (err) {
      setLoadingBalance(false);
      setEditAccount(false);
    }
  };
  const sendAndDeposit = async () => {
    if (
      !selectedAsset ||
      !substrateApi ||
      !dotsamaWalletAddress ||
      !account ||
      !connector
    )
      return;
    await form.validateFields();
    let transfer;
    if (selectedAsset.tokenId === '-1') {
      transfer = substrateApi.tx.balances.transferKeepAlive(
        dotsamaWalletAddress,
        formatAmountToBase(amount, selectedAsset.decimals),
      );
    } else {
      transfer = substrateApi.tx.assets.transferKeepAlive(
        selectedAsset.tokenId,
        dotsamaWalletAddress,
        formatAmountToBase(amount, selectedAsset.decimals),
      );
    }
    const nonce = await substrateApi.rpc.system.accountNextIndex(account);
    await handleSubmit?.();
    const unsub = await transfer.signAndSend(
      account,
      { nonce, signer: connector.signer },
      ({ status, txHash }) => {
        onCompleted?.({
          txDotsama: { status, txHash, unsub },
          blockchain: selectedBlockchain,
          poolId: poolId || '',
          id: poolId || '',
          tx: undefined,
        });
      },
    );
  };
  return (
    <>
      {substarteApiLoading ? (
        <Skeleton
          style={{ marginBottom: 0, marginLeft: 4 }}
          active={true}
          paragraph={{ rows: 2 }}
        />
      ) : (
        selectedBlockchain && (
          <Form.Item
            label="Wallet"
            required
            tooltip="Select the wallet that has funds to deposit."
            name="wallet"
            rules={[{ required: true }]}
          >
            {editAccount ? (
              <DotsamaWallet
                blockchain={selectedBlockchain!}
                excludedWallets={[Web3WalletType.DOTSAMA_MANUAL]}
                button={{
                  confirm: {
                    enable: true,
                    text: 'get balance',
                    loading: loadingBalance,
                  },
                }}
                onSuccess={(item) => getBalance(item)}
              />
            ) : (
              <Flex gap={20}>
                <Input size="large" value={account} disabled={true} />
                <EditOutlined onClick={() => setEditAccount(true)} />
              </Flex>
            )}
          </Form.Item>
        )
      )}
      {balance && (
        <>
          <AmountInput
            availableAmount={balance}
            asset={selectedAsset}
            itemProps={{
              label: 'Amount of tokens to deposit in the pool',
              name: 'amount',
            }}
            inputProps={{
              placeholder: 'Total Amount',
            }}
          />
          <Form.Item>
            <StyledButton
              disabled={!amount}
              size="large"
              htmlType="submit"
              type="primary"
              block
              shape="round"
              onClick={sendAndDeposit}
            >
              {submitButtonText}
            </StyledButton>
          </Form.Item>
        </>
      )}
    </>
  );
}
