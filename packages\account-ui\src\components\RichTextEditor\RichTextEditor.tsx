import {
  LinkOutlined,
  PictureOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons';
import { Space } from 'antd';
import {
  CompositeDecorator,
  ContentState,
  Editor,
  EditorState,
  RichUtils,
  SelectionState,
  convertFromRaw,
  convertToRaw,
  getDefaultKeyBinding,
} from 'draft-js';
import 'draft-js/dist/Draft.css';
import { useRef, useState } from 'react';
import styled from 'styled-components';
import AlignmentControls, { toggleTextAlignment } from './AlignmentControls';
import BlockStyleControls from './BlockStyleControls';
import ControlButton from './ControlButton';
import InlineStyleControls from './InlineStyleControls';
import { LinkDecorator, LinkStyleControls } from './LinkStyleControls';
import { mediaBlockRendererFn } from './MediaComponent';
import { MediaStyleControls } from './MediaStyleControls';
import StateControls from './StateControls';

const EditorRoot = styled.div<{ $focus: boolean; $disabled?: boolean }>`
  width: 100%;
  border: 1px solid ${({ theme }) => theme.colors.borderFormElements};
  background-color: ${({ theme }) => theme.colors.componentBackground};
  border-radius: 6px;
  ${({ theme, $focus }) =>
    $focus && `border: 1px solid ${theme.colors.primary}`}
  ${({ theme, $disabled }) =>
    $disabled &&
    `
    color: ${theme.colors.textCd};
    background-color: ${theme.colors.componentBackgroundDisabled};
    box-shadow: none;
    cursor: not-allowed;
    opacity: 1;
  `}
`;

const EditorHeader = styled.div`
  padding: 8px;
  overflow-x: auto;
`;

const EditorWrapper = styled.div<{ minHeight?: number }>`
  padding: 12px 15px;
  padding-top: 0;
  font-size: 16px;

  .DraftEditor-editorContainer,
  .DraftEditor-root,
  .public-DraftEditor-content {
    min-height: ${({ minHeight }) => minHeight || 50}px !important;
  }

  .public-DraftEditor-content .RichEditor-text-align-left,
  .public-DraftEditor-content .RichEditor-text-align-left > div {
    text-align: left !important;
  }

  .public-DraftEditor-content .RichEditor-text-align-center,
  .public-DraftEditor-content .RichEditor-text-align-center > div {
    text-align: center !important;
  }

  .public-DraftEditor-content .RichEditor-text-align-right,
  .public-DraftEditor-content .RichEditor-text-align-right > div {
    text-align: right !important;
  }
`;

export default function RichTextEditor({
  placeholder,
  disabled,
  defaultValue,
  minHeight = 50,
  onChange,
}: {
  onChange?: (value: string, editorState: EditorState) => void;
  placeholder?: string;
  disabled?: boolean;
  defaultValue?: string;
  minHeight?: number;
}) {
  const inputRef = useRef<Editor>(null);
  const [isFocused, setIsFocused] = useState(false);

  const decorator = new CompositeDecorator([LinkDecorator]);

  const [editorState, setEditorState] = useState<EditorState>(() => {
    if (!defaultValue) return EditorState.createEmpty(decorator);
    try {
      const parsedJson = JSON.parse(defaultValue);
      return EditorState.createWithContent(
        convertFromRaw(parsedJson),
        decorator,
      );
    } catch (err) {
      return EditorState.createWithContent(
        ContentState.createFromText(defaultValue),
        decorator,
      );
    }
  });

  const handleOnChange = (_editorState: EditorState) => {
    if (disabled) return;
    setEditorState(_editorState);
    const contentState = _editorState?.getCurrentContent();

    const stringContent = contentState
      ? JSON.stringify(convertToRaw(contentState))
      : '';
    onChange?.(stringContent, _editorState);
  };

  const [linkControlVisible, setLinkControlVisible] = useState(false);
  const [photoControlVisible, setPhotoControlVisible] = useState(false);
  const [videoControlVisible, setVideoControlVisible] = useState(false);

  return (
    <EditorRoot $focus={isFocused} $disabled={disabled}>
      {linkControlVisible && (
        <LinkStyleControls
          editorState={editorState}
          disabled={disabled}
          onFinish={(
            editorState: EditorState,
            targetSelection: SelectionState,
            entityKey: string | null,
          ) => {
            handleOnChange(
              RichUtils.toggleLink(editorState, targetSelection, entityKey),
            );
            setLinkControlVisible(false);
          }}
        />
      )}
      {photoControlVisible && (
        <MediaStyleControls
          editorState={editorState}
          disabled={disabled}
          mediaType="image"
          onFinish={(newEditorState: EditorState) => {
            handleOnChange(newEditorState);
            setPhotoControlVisible(false);
          }}
        />
      )}
      {videoControlVisible && (
        <MediaStyleControls
          editorState={editorState}
          disabled={disabled}
          mediaType="video"
          onFinish={(newEditorState: EditorState) => {
            handleOnChange(newEditorState);
            setVideoControlVisible(false);
          }}
        />
      )}
      <EditorHeader>
        <Space size="large">
          <StateControls
            onUndo={() => handleOnChange(EditorState.undo(editorState))}
            onRedo={() => handleOnChange(EditorState.redo(editorState))}
            disabled={disabled}
          />
          <InlineStyleControls
            editorState={editorState}
            onToggle={(inlineStyle: string) => {
              handleOnChange(
                RichUtils.toggleInlineStyle(editorState, inlineStyle),
              );
            }}
            disabled={disabled}
          />
          <BlockStyleControls
            editorState={editorState}
            onToggle={(blockType: string) => {
              handleOnChange(RichUtils.toggleBlockType(editorState, blockType));
            }}
            disabled={disabled}
          />
          <AlignmentControls
            editorState={editorState}
            onToggle={(alignment: string) => {
              handleOnChange(toggleTextAlignment(editorState, alignment));
            }}
            disabled={disabled}
          />
          <ControlButton
            $disabled={disabled}
            onMouseDown={() => setLinkControlVisible(!linkControlVisible)}
          >
            <LinkOutlined />
          </ControlButton>
          <ControlButton
            $disabled={disabled}
            onMouseDown={() => setPhotoControlVisible(!photoControlVisible)}
          >
            <PictureOutlined />
          </ControlButton>
          <ControlButton
            $disabled={disabled}
            onMouseDown={() => setVideoControlVisible(!videoControlVisible)}
          >
            <VideoCameraOutlined />
          </ControlButton>
        </Space>
      </EditorHeader>

      <EditorWrapper
        onClick={() => {
          inputRef?.current?.focus();
        }}
        minHeight={minHeight}
      >
        <Editor
          blockStyleFn={(block) => {
            const blockData = block.getData();
            const textAlign = blockData.get('textAlign') || 'left';
            let className = '';

            switch (block.getType()) {
              case 'blockquote':
                className = 'RichEditor-blockquote';
                break;
              default:
                className = '';
            }

            className += ` RichEditor-text-align-${textAlign}`;

            return className.trim();
          }}
          blockRendererFn={mediaBlockRendererFn}
          customStyleMap={{
            CODE: {
              backgroundColor: 'rgba(0, 0, 0, 0.05)',
              fontFamily: '"Inconsolata", "Menlo", "Consolas", monospace',
              fontSize: 16,
              padding: 2,
            },
          }}
          editorState={editorState}
          onChange={handleOnChange}
          placeholder={placeholder || 'Type ...'}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          keyBindingFn={(e) => getDefaultKeyBinding(e)}
          handleKeyCommand={(command, editorState) => {
            const newState = RichUtils.handleKeyCommand(editorState, command);
            if (newState) {
              handleOnChange(newState);
              return 'handled';
            }
            return 'not-handled';
          }}
          spellCheck={true}
          ref={inputRef}
          readOnly={disabled}
        />
      </EditorWrapper>
    </EditorRoot>
  );
}
