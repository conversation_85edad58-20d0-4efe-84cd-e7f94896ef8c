import { registerEnumType } from '@nestjs/graphql';

export enum BlockchainIntegrationStatus {
  UPCOMING = 'UPCOMING',
  VERIFIED = 'VERIFIED',
  UNVERIFIED = 'UNVERIFIED',
  PENDING = 'PENDING',
  AIR_TOKEN = 'AIR_TOKEN',
}

export enum TransactionStatus {
  ISSUED = 'ISSUED',
  PROCESSING = 'PROCESSING',
  FAILED = 'FAILED',
  SUCCESS = 'SUCCESS',
}

// List of private RPC URLs that should only be used backend-side
export const PRIVATE_RPC_URLS: string[] = [
  'https://api-base-mainnet-archive.n.dwellir.com/81fabb09-7830-4df2-b522-ee9d1ae135d7',
  'https://base-mainnet.blastapi.io/27a20fa2-ff68-40bb-8396-1404d4803a4b',
];

registerEnumType(TransactionStatus, { name: 'TransactionStatus' });

// Register enum
registerEnumType(BlockchainIntegrationStatus, {
  name: 'BlockchainIntegrationStatus',
});
