import { BadRequestException, Injectable } from '@nestjs/common';
import { BlockchainService } from '@root/blockchain/blockchain.service';
import { ethers } from 'ethers';

@Injectable()
export class EVMProviderService {
  constructor(private readonly blockchainService: BlockchainService) {}

  getProviderInfo(chainId: number) {
    return {} as any;
  }

  async getContractAddress(chainId: string, contractType: string) {
    return {} as any;
  }

  async getJsonRpcProvider(blockchainId: string) {
    const blockchain = await this.blockchainService.findById(blockchainId, true);
    if (!blockchain)
      throw new BadRequestException('Invalid blockchain address');
    if (blockchain.rpcUrls.length === 0)
      throw new BadRequestException('No RPC url found');
    return new ethers.JsonRpcProvider(blockchain.rpcUrls[0]);
  }

  async getTransaction(blockchainId: string, transactionHash: string) {
    const provider = await this.getJsonRpcProvider(blockchainId);
    return provider.getTransaction(transactionHash);
  }

  async getTransactionReceipt(blockchainId: string, transactionHash: string) {
    const provider = await this.getJsonRpcProvider(blockchainId);
    return provider.getTransactionReceipt(transactionHash);
  }
}
