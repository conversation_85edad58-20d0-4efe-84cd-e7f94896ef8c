import { AssetType, Blockchain, ContractType } from '@airlyft/types';
import { EvmConnectorData } from '@airlyft/web3-evm-hooks';
import EvmWallet from '@Components/Web3Wallet/Evm/EvmWallet';
import useGetContract from '@Hooks/useGetContract';
import BlockchainSelect from '@Pages/airpools/components/BlockchainSelect';
import ImageDragger from '@Pages/project/components/ImageDragger';
import { Form, Input, Typography } from 'antd';
import { useContext, useState } from 'react';
import { AirTokenProps } from '../components/AirTokenRenderer';
import NftAdvancedFormItem from '../components/NftAdvancedFormItem';
import {
  createERC721AirToken,
  useCreateAirToken,
} from '../hooks/useCreateAirToken';
import { FormHorizontalLayout } from '@Root/theme/layout';
import { NotificationContext } from '@Pages/App';
import {
  isAirTokenTransferable,
  isSoleBoundSupported,
} from '../airbase.helper';
import { UploadType } from '@Root/types';
const { Text } = Typography;

export default function CreateERC721AirToken({
  onCompleted,
  projectId,
}: AirTokenProps) {
  const { api } = useContext(NotificationContext);
  const [form] = Form.useForm();
  const [selectedBlockchain, setSelectedBlockchain] = useState<Blockchain>();
  const { data: contractData, loading: contractLoading } = useGetContract(
    selectedBlockchain?.id,
    ContractType.ERC721_AIRBASE,
  );
  const icon = Form.useWatch(['icon'], form);

  const contractAddress = contractData?.contract?.address;

  const { create, loading: creating } = useCreateAirToken(
    projectId,
    contractAddress,
    selectedBlockchain,
    createERC721AirToken,
  );

  const handleRemove = () => {
    form.setFieldsValue({ icon: null });
  };

  const updateSelectedFile = (url: string) => {
    url = url.split('?')[0];
    form.setFieldsValue({ icon: url });
  };

  const handleSubmit = async (connectorData: EvmConnectorData) => {
    try {
      form.submit();
      const values = await form.validateFields();

      const { provider, account } = connectorData;

      if (!provider || !account || !contractAddress) return;

      const transferable = isAirTokenTransferable(
        selectedBlockchain?.version,
        values.transferable,
      );

      create({
        connectorData,
        values: {
          ...values,
          baseURI: values.baseURI || '',
          assetType: AssetType.ERC721,
          creatorBlockchainAddress: connectorData.account,
          contractAddress,
          transferable,
        },
        onError: (err) => {
          api?.error({
            message: err?.toString() || 'Something went wrong',
          });
        },
        onSuccess: (data) => {
          onCompleted?.({ ...data, blockchain: selectedBlockchain });
        },
      });
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        transferable: true,
        tokenBaseUriType: 'default',
      }}
    >
      <FormHorizontalLayout>
        <Form.Item
          label="Token Logo"
          name="icon"
          required
          rules={[{ required: true, message: 'Logo is required.' }]}
        >
          <ImageDragger
            handleRemove={handleRemove}
            updateSelectedFile={updateSelectedFile}
            selectedFile={icon}
            handleDelete={handleRemove}
            aspect="square"
            hint="Recommended 1:1 aspect-ratio"
            height={320}
            width={320}
            uploadType={UploadType.ASSET_LOGO}
            acceptVideo={true}
          />
        </Form.Item>
        <div
          style={{
            width: '100%',
          }}
        >
          <Form.Item
            label="Blockchain"
            required
            tooltip="Select the blockchain on which the reward distribution will take place."
            name="blockchainId"
            rules={[{ required: true }]}
          >
            <BlockchainSelect
              value={selectedBlockchain?.id}
              contractType={ContractType.ERC721_AIRBASE}
              onBlockchain={(value) => {
                setSelectedBlockchain(value);
              }}
            />
          </Form.Item>

          <Form.Item
            label="Token name"
            tooltip="This is a required field"
            name="name"
            rules={[{ required: true }]}
          >
            <Input placeholder="Token name" size="large" />
          </Form.Item>

          <Form.Item label="Token Description" name="description">
            <Input.TextArea
              autoSize={{ minRows: 2 }}
              placeholder="Token Description"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="Token Ticker / Symbol"
            tooltip="This is a required field"
            name="ticker"
            rules={[{ required: true }]}
          >
            <Input placeholder="Token Ticker / Symbol" size="large" />
          </Form.Item>

          <NftAdvancedFormItem
            showTransferable={isSoleBoundSupported(selectedBlockchain?.version)}
          />

          {selectedBlockchain && (
            <Form.Item label="Wallet" required tooltip={`Wallet`}>
              <EvmWallet
                blockchain={selectedBlockchain}
                button={{
                  confirm: {
                    enable: true,
                    loading: creating,
                    text: 'Create using ',
                  },
                }}
                onSuccess={(item) => handleSubmit(item)}
              />
            </Form.Item>
          )}
        </div>
      </FormHorizontalLayout>
    </Form>
  );
}
