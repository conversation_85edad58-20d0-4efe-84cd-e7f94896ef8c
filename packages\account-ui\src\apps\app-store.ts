import { registerToAppStore } from './app-store.helper';
import { DiscordApp } from './Discord/discord.app';
import { FlashcardApp } from './Flashcard/flashcard.app';
import { FormApp } from './Form/form.app';
import { InstagramApp } from './Instagram/instagram.app';
import { UploadApp } from './Upload/upload.app';
import { EvmApp } from './Evm/evm.app';
import { SubsocialApp } from './Subsocial/subsocial.app';
import { TelegramApp } from './Telegram/telegram.app';
import { TwitterApp } from './Twitter/twitter.app';
import { UrlApp } from './Url/url.app';
import { YoutubeApp } from './Youtube/youtube.app';
import { QuizApp } from './Quiz/quiz.app';
import { WalletApp } from './Wallet/wallet.app';
import { SubgraphApp } from './Subgraph/subgraph.app';
import { AirBoostApp } from './Airboost/airboost.app';
import { RestApp } from './Rest/rest.app';
import { EmailApp } from './Email/email.app';
import { TermsApp } from './Terms/terms.app';
import { CheckinApp } from './Checkin/checkin.app';
import { FaucetApp } from './Faucet/faucet.app';
import { AirquestApp } from './Airquest/airquest.app';
import { SubstrateApp } from './Substrate/substrate.app';
import { LuckydrawApp } from './Luckydraw/luckydraw.app';
import { MobileAppApp } from './MobileApp/mobile-app.app';
import { SecretCodeApp } from './SecretCode/secret-code.app';
import { ProducthuntApp } from './Producthunt/producthunt.app';
import { BlogApp } from './Blog/blog.app';
import { KickstarterApp } from './Kickstarter/kickstarter.app';
import { isFeatureEnabled } from '@Components/FeatureGuard';

export const initAppStore = () =>
  registerToAppStore([
    TwitterApp,
    UploadApp,
    MobileAppApp,
    TelegramApp,
    DiscordApp,
    WalletApp,
    SubstrateApp,
    FormApp,
    QuizApp,
    EmailApp,
    UrlApp,
    AirBoostApp,
    InstagramApp,
    YoutubeApp,
    EvmApp,
    SubgraphApp,
    RestApp,
    FaucetApp,
    SubsocialApp,
    TermsApp,
    CheckinApp,
    AirquestApp,
    LuckydrawApp,
    SecretCodeApp,
    ProducthuntApp,
    BlogApp,
    KickstarterApp,
    ...(isFeatureEnabled('FLASHCARD') ? [FlashcardApp] : []),
  ]);
