import { CoreEntity } from '@models/core';
import {
  Field,
  GraphQLISODateTime,
  Int,
  ObjectType,
  registerEnumType,
} from '@nestjs/graphql';
import { EventConnection } from '@root/event-participation/event-connection.entity';
import { EventParticipant } from '@root/event-participation/event-participant.entity';
import { EventTemplate } from '@root/event-template/event-template.entity';
import { GiveawayEvent } from '@root/giveaway/entities/giveaway-event.entity';
import { GiveawayType } from '@root/giveaway/giveaway.constants';
import { IntegrationEvent } from '@root/integration/integration-event/integration-event.entity';
import { UserNotification } from '@root/notification/user-notification.entity';
import { Project } from '@root/project/project.entity';
import { Task } from '@root/task/task.entity';
import {
  AfterLoad,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  Unique,
} from 'typeorm';
import { ProjectEventSummary } from './project-event-summary.entity';
import { Season } from '@root/season/season.entity';

export enum EventState {
  DRAFT = 'DRAFT',
  COMMITTED = 'COMMITTED',
  SCHEDULED = 'SCHEDULED',
  ONGOING = 'ONGOING',
  READY_TO_SETTLE = 'READY_TO_SETTLE', //derived state from ongoing
  SETTLED = 'SETTLED',
}

export enum EventVisibility {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC',
  PUBLIC_EXPLORABLE = 'PUBLIC_EXPLORABLE',
}

export enum AccountEventVisibility {
  PRIVATE = 'PRIVATE',
  PUBLIC = 'PUBLIC',
}
export enum PublicEventVisibility {
  PUBLIC = 'PUBLIC',
  PUBLIC_EXPLORABLE = 'PUBLIC_EXPLORABLE',
}

export enum PublicEventState {
  SCHEDULED = 'SCHEDULED',
  ONGOING = 'ONGOING',
  READY_TO_SETTLE = 'READY_TO_SETTLE', //derived state from ongoing
  SETTLED = 'SETTLED',
}

export enum EventType {
  DEFAULT_QUEST = 'DEFAULT_QUEST',
  CAMPAIGN = 'CAMPAIGN',
  E_LEARNING = 'E_LEARNING',
}

export enum LeaderboardType {
  NONE = 'NONE',
  EVENT = 'EVENT',
  SEASON = 'SEASON',
}

// Register enums
registerEnumType(EventType, { name: 'EventType' });
registerEnumType(EventState, { name: 'EventState' });
registerEnumType(PublicEventState, { name: 'PublicEventState' });
registerEnumType(EventVisibility, { name: 'EventVisibility' });
registerEnumType(AccountEventVisibility, { name: 'AccountEventVisibility' });
registerEnumType(PublicEventVisibility, { name: 'PublicEventVisibility' });
registerEnumType(LeaderboardType, { name: 'LeaderboardType' });

@ObjectType()
export class GiveawaySummary {
  @Field({ nullable: true })
  title: string;

  @Field(() => GiveawayType)
  giveawayType: GiveawayType;

  @Field({ nullable: true })
  icon?: string;

  @Field({ nullable: true })
  blockchainId?: string;
}

@ObjectType()
@Entity('project_events')
@Unique(['projectId', 'publicLink'])
export class ProjectEvent extends CoreEntity {
  @Field()
  @Column()
  title: string;

  @Field(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  startTime?: Date;

  @Field()
  @Column()
  publicLink: string;

  @Field(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  endTime?: Date;

  @Field({ nullable: true })
  @Column({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  bannerUrl?: string;

  @Field(() => EventState)
  @Column({
    default: EventState.DRAFT,
    enum: EventState,
    type: 'enum',
  })
  state: EventState;

  @Field(() => [String], { nullable: 'itemsAndList' })
  @Column('simple-array', { nullable: true })
  settlementFiles: string[];

  @Field(() => GraphQLISODateTime, { nullable: true })
  @Column({ type: 'timestamptz', nullable: true })
  settledAt?: Date;

  @Field(() => EventType)
  @Column({
    default: EventType.CAMPAIGN,
    type: 'enum',
    enum: EventType,
  })
  eventType: EventType;

  @Field(() => EventVisibility)
  @Column({
    default: EventVisibility.PUBLIC,
    nullable: true,
    type: 'enum',
    enum: EventVisibility,
  })
  visibility?: EventVisibility;

  @Field(() => Int, { nullable: true })
  @Column({ nullable: true })
  mode: number;

  // Event Participants
  @OneToMany(() => EventParticipant, (participant) => participant.event)
  participants?: EventParticipant[];

  // Event Connections
  @OneToMany(() => EventConnection, (connection) => connection.event)
  eventConnections?: EventConnection[];

  @ManyToOne(() => Project, (project) => project.projectEvents, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  project: Project;

  @Column({ nullable: true })
  projectId?: string;

  @OneToMany(() => Task, (eventTask) => eventTask.event)
  taskList?: Task[];

  @OneToMany(() => GiveawayEvent, (ge) => ge.event)
  giveawayEvents?: GiveawayEvent[];

  @Field(() => ProjectEventSummary, { nullable: true })
  @OneToOne(() => ProjectEventSummary, (summary) => summary.event)
  summary?: ProjectEventSummary;

  @OneToOne(() => EventTemplate, (et) => et.event)
  eventTemplate: EventTemplate;

  @ManyToOne(() => EventTemplate, (et) => et.generatedEvents)
  generatorTemplate: EventTemplate;

  @Column({ nullable: true })
  generatorTemplateId: string;

  @OneToMany(() => IntegrationEvent, (i) => i.event)
  integrations?: IntegrationEvent[];

  //Title of the claim reward tile
  @Field({ nullable: true })
  @Column({ nullable: true })
  rewardTitle?: string;

  //Subtitle of the claim reward tile
  @Field({ nullable: true })
  @Column({ nullable: true })
  rewardSubtitle?: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  ipProtect?: boolean;

  @Field(() => LeaderboardType)
  @Column({
    default: LeaderboardType.NONE,
    nullable: true,
    type: 'enum',
    enum: LeaderboardType,
  })
  leaderboard?: LeaderboardType;

  @ManyToOne(() => Season, (season) => season.events)
  season: Season;

  @Field({ nullable: true })
  @Column({ nullable: true })
  seasonId?: string;

  @AfterLoad()
  updateState() {
    if (
      this.state === EventState.ONGOING &&
      this.endTime &&
      this.endTime < new Date()
    ) {
      this.state = EventState.READY_TO_SETTLE;
    }
  }

  @OneToMany(() => UserNotification, (notification) => notification.event)
  userNotifications?: UserNotification[];

  @Field(() => [String], { nullable: 'itemsAndList' })
  @Column('varchar', {
    array: true,
    nullable: true,
  })
  tags?: string[];
}
