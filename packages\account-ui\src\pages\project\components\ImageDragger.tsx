import { useUploadSignedUrl } from '@Hooks/mutation';
import { IStorage, UploadType, UPLOAD_CONFIGS } from '@Root/types';
import { InboxOutlined } from '@ant-design/icons';
import { Upload } from 'antd';
import { RcFile } from 'antd/lib/upload/interface';
import axios from 'axios';
import { CSSProperties, useContext, useState } from 'react';
import styled from 'styled-components';
import ImageBadge from './ImageBadge';
import { NotificationContext } from '@Pages/App';
import ImgCrop from 'antd-img-crop';
import { StyledVideo } from '@Components/Image';

const { Dragger } = Upload;

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const ParentDiv = styled.div<{ height?: number; width?: number }>`
  display: flex;
  flex-direction: column;
  gap: 16px;

  .square-uploader,
  .square-uploader > .ant-upload {
    width: ${({ height }) => height || 230}px;
    height: ${({ width }) => width || 230}px;
  }

  .ant-upload-list.ant-upload-list-text {
    max-width: ${({ width }) => width + 'px' || 100 + '%'} !important;
    padding-bottom: 10px;
  }

  .ant-upload-wrapper {
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 30px;
  }
`;

const VideoPreview = styled.div<{
  aspectRatio: string;
  height?: number;
  width?: number;
}>`
  position: relative;
  max-width: ${({ width }) => width || 230}px;
  max-height: ${({ height }) => height || 230}px;

  video {
    width: 100%;
    height: 100%;
    aspect-ratio: ${({ aspectRatio }) => aspectRatio};
    object-fit: cover;
  }

  .delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
`;

export default function ImageDragger({
  updateSelectedFile,
  handleRemove,
  selectedFile,
  handleDelete,
  disabled,
  hint,
  style,
  height,
  width,
  aspect,
  editor = true,
  uploadType = UploadType.PROJECT_LOGO,
  hideHint = false,
  acceptVideo = false,
}: {
  updateSelectedFile: (url: string) => void;
  handleRemove: (value: any) => void;
  selectedFile: string;
  handleDelete: () => void;
  aspect?: 'square' | 'video' | 'community-banner';
  disabled?: boolean;
  hint?: string;
  style?: CSSProperties | undefined;
  height?: number;
  width?: number;
  editor?: boolean;
  uploadType?: UploadType;
  hideHint?: boolean;
  acceptVideo?: boolean;
}) {
  const { api } = useContext(NotificationContext);
  const selectedFileUploaded =
    !!selectedFile && !selectedFile.includes('fakepath');
  const uploadSignedUrl = useUploadSignedUrl();
  const config = UPLOAD_CONFIGS[uploadType];

  const aspectNum = config.aspectRatio?.width || 1;
  const aspectDenom = config.aspectRatio?.height || 1;

  const handleFileRemove = (file: any) => {
    handleRemove(file);
    updateSelectedFile('');
    handleDelete();
  };

  const isVideo = (file: RcFile) => {
    return file.type.startsWith('video/');
  };

  const isImage = (file: RcFile) => {
    return file.type.startsWith('image/');
  };

  const validateFile = (file: RcFile) => {
    const fileSizeMB = file.size / (1024 * 1024);
    const maxSizeMB = config.maxSize / (1024 * 1024);
    const maxSizeByType =
      config.maxSizeByType?.[file.type.split('/')[0]] || maxSizeMB;

    if (fileSizeMB > maxSizeByType) {
      api?.error({
        message: isVideo(file)
          ? 'Video file too large'
          : 'Image file too large',
        description: `File size (${fileSizeMB.toFixed(
          2,
        )}MB) exceeds the limit. Maximum allowed size is ${maxSizeByType}MB`,
      });
      return false;
    }

    if (acceptVideo && isVideo(file)) {
      return true;
    } else if (isImage(file)) {
      return true;
    } else {
      api?.error({
        message: 'Invalid file type',
        description: `Only ${
          acceptVideo ? 'images and videos' : 'images'
        } are allowed.`,
      });
      return false;
    }
  };

  const getAcceptTypes = () => {
    let types = 'image/*';
    if (acceptVideo) {
      types += ',video/*';
    }
    return types;
  };

  const tpl = () => (
    <Dragger
      beforeUpload={(file) => {
        if (!validateFile(file)) {
          return Upload.LIST_IGNORE;
        }
        return true;
      }}
      multiple={false}
      disabled={disabled}
      className={aspectNum === aspectDenom ? 'square-uploader' : ''}
      onRemove={handleFileRemove}
      accept={getAcceptTypes()}
      showUploadList={{
        showPreviewIcon: false,
        showRemoveIcon: true,
        showDownloadIcon: false,
      }}
      customRequest={async ({ file, onError, onSuccess, onProgress }) => {
        const uploadedFile = file as RcFile;

        if (!validateFile(uploadedFile)) {
          onError && onError(new Error('Invalid file'));
          return;
        }

        const filename = `${Date.now()}.${uploadedFile.name.split('.').pop()}`;
        const storageObj = {
          action: 'putObject',
          key: filename,
          size: uploadedFile.size,
          uploadType,
        } as IStorage;
        try {
          await uploadSignedUrl.mutateAsync(storageObj, {
            onSuccess: (data: any) => {
              const formData = new FormData();
              Object.entries(data.fields).forEach(([key, value]) => {
                formData.append(key, value as string);
              });
              formData.append('file', file);
              axios
                .post(data.url, formData, {
                  onUploadProgress: (progressEvent) => {
                    const percent = Math.round(
                      (progressEvent.loaded * 100) / (progressEvent.total || 1),
                    );
                    onProgress && onProgress({ percent });
                  },
                })
                .then((result: any) => {
                  onSuccess && onSuccess('Ok');
                  // Construct the full URL to the uploaded file
                  const bucket = data.url.split('/')[2].split('.')[0];
                  const region = data.url.split('.')[2];
                  const fileUrl = `https://${bucket}.s3.${region}.amazonaws.com/${data.fields.key}`;
                  updateSelectedFile(fileUrl);
                  api?.success({
                    message: 'Successfully uploaded!',
                  });
                })
                .catch((error: any) => {
                  onError && onError(error);
                });
            },
          });
        } catch (error: any) {
          onError && onError(error);
        }
      }}
    >
      <p className="ant-upload-drag-icon">
        <InboxOutlined />
      </p>
      {!hideHint && (
        <>
          <p className="ant-upload-text">
            Click or drag file to this area to upload
          </p>
          <p className="ant-upload-hint">
            {hint ? `${hint} • ` : ''}
            {acceptVideo ? 'Upload image or video • ' : ''}
            Maximum file size for{' '}
            {uploadType === UploadType.PROJECT_LOGO
              ? 'Logo'
              : config.displayName}
            :{' '}
            {acceptVideo
              ? `Image: ${formatFileSize(
                  config.maxSizeByType?.['image'] || config.maxSize,
                )} | Video: ${formatFileSize(
                  config.maxSizeByType?.['video'] || config.maxSize,
                )}`
              : formatFileSize(
                  config.maxSizeByType?.['image'] || config.maxSize,
                )}
          </p>
        </>
      )}
    </Dragger>
  );

  // Determine if the current file is a video based on extension
  const isVideoFile =
    selectedFileUploaded &&
    (selectedFile.toLowerCase().endsWith('.mp4') ||
      selectedFile.toLowerCase().endsWith('.mov') ||
      selectedFile.toLowerCase().endsWith('.webm') ||
      selectedFile.toLowerCase().endsWith('.avi'));

  return (
    <ParentDiv style={style} height={height} width={width}>
      {!selectedFileUploaded && (
        <>
          {editor && !isVideo && !acceptVideo ? (
            <ImgCrop
              aspect={aspectNum / aspectDenom}
              fillColor="transparent"
              rotationSlider
              beforeCrop={(file) => {
                if (
                  file.type.startsWith('image/gif') ||
                  file.type.startsWith('image/webp')
                )
                  return Promise.reject();
                return true;
              }}
            >
              {tpl()}
            </ImgCrop>
          ) : (
            tpl()
          )}
        </>
      )}
      {selectedFileUploaded && !isVideoFile && (
        <ImageBadge
          url={selectedFile.split('?')[0]}
          handleDelete={() => handleFileRemove({ name: selectedFile })}
          disabled={disabled}
          aspectRatio={`${aspectNum}/${aspectDenom}`}
          height={height}
          width={width}
        />
      )}
      {selectedFileUploaded && isVideoFile && (
        <VideoPreview
          aspectRatio={`${aspectNum}/${aspectDenom}`}
          height={height}
          width={width}
        >
          <StyledVideo
            src={selectedFile.split('?')[0]}
            preload="metadata"
            autoPlay
            muted
            loop
          />
          {!disabled && (
            <button
              className="delete-button"
              onClick={() => handleFileRemove({ name: selectedFile })}
            >
              ✕
            </button>
          )}
        </VideoPreview>
      )}
    </ParentDiv>
  );
}
