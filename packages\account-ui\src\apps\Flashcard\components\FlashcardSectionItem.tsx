import SimpleEditor from '@Components/RichTextEditor/SimpleEditor';
import { Typography } from 'antd';
import React from 'react';

const { Paragraph } = Typography;

interface FlashcardSectionItemProps {
  item: {
    id: string;
    order: number;
    content?: string;
    hidden?: boolean;
  };
  style?: React.CSSProperties;
}

const FlashcardSectionItem: React.FC<FlashcardSectionItemProps> = ({
  item,
  style,
}) => {
  const renderContent = () => (
    <Paragraph ellipsis={{ rows: 2 }}>
      <SimpleEditor defaultValue={item.content || ''} />
    </Paragraph>
  );

  return (
    <div style={style}>
      <div style={{ marginTop: 8 }}>{renderContent()}</div>
    </div>
  );
};

export default FlashcardSectionItem;
