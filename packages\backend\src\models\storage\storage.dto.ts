import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum } from 'class-validator';

export enum UploadType {
  TASK_ICON = 'TASK_ICON',
  PROJECT_LOGO = 'PROJECT_LOGO',
  BLOCKCHAIN_LOGO = 'BLOCKCHAIN_LOGO',
  ASSET_LOGO = 'ASSET_LOGO',
  BANNER = 'BANNER',
  USER_IMAGE = 'USER_IMAGE',
  PUBLIC_TASK_IMAGE = 'PUBLIC_TASK_IMAGE',
  RICH_TEXT_MEDIA = 'RICH_TEXT_MEDIA',
}

export class StorageDto {
  @IsString()
  @ApiProperty()
  action: string;

  @IsString()
  @ApiProperty()
  key: string;

  @IsNumber()
  @ApiProperty()
  size: number;

  @IsEnum(UploadType)
  @ApiProperty({
    enum: UploadType,
  })
  uploadType: UploadType;
}
