# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Dot<PERSON><PERSON><PERSON>ft {
  createdAt: DateTime!
  updatedAt: DateTime!
  itemId: Int!
  blockchainAssetId: String!
  blockchainPoolId: String!
  status: NFTStatus!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum NFTStatus {
  AVAILABLE
  LOCKED
  DISTRIBUTED
}

type Blockchain {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  chainId: Int!
  name: String!
  type: BlockchainType!
  blockExplorerUrls: [String!]!
  nativeCurrency: String!
  decimals: Int!
  rpcUrls: [String!]!
  tokenUrls: [String]
  icon: String!
  color: String
  website: String
  version: Float
  apiUrl: String
  status: BlockchainIntegrationStatus!
  order: Int!
  assets: [BlockchainAsset!]
}

enum BlockchainType {
  EVM
  DOTSAMA
}

enum BlockchainIntegrationStatus {
  UPCOMING
  VERIFIED
  UNVERIFIED
  PENDING
  AIR_TOKEN
}

type Authorization {
  projectId: String!
  userId: String!
  role: Role!
  user: User!
}

enum Role {
  OWNER
  ADMIN
  REVIEWER
}

type Billing {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  gateway: PaymentGateway!
  txInfo: String
  status: BillingStatus!
  startDate: DateTime
  currentPeriodStart: DateTime
  currentPeriodEnd: DateTime
  currency: String
  amount: Int
  isCurrent: Boolean!
  plan: BillingPlan
  planId: String!
}

enum PaymentGateway {
  STRIPE
  AIRLYFT_MANUAL
  AIRLYFT_CRYPTO
}

"""Billing Payment status"""
enum BillingStatus {
  ACTIVE
  CANCELED
  INCOMPLETE
  INCOMPLETE_EXPIRED
  PAST_DUE
  PAUSED
  TRIALING
  UNPAID
  PAYMENT_PROCESSING
}

type BillingDetails {
  maxEntries: Float!
  maxAiVerificationEntries: Float!
  entryResetDate: DateTime
  plan: PaymentPlan!
  renewalDate: DateTime
  billing: Billing
}

enum PaymentPlan {
  TRIAL
  FREE
  STARTUP
  STARTUP_BIANNUAL
  PROFESSIONAL
  PROFESSIONAL_BIANNUAL
  ENTERPRISE
  ENTERPRISE_TRIAL
}

type BillingPlan {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  plan: PaymentPlan!
  price: String!
  originalPrice: Float!
  currency: String
  billings: [Billing!]
}

type ProjectTemplateOnboardings {
  createdAt: DateTime!
  updatedAt: DateTime!
  projectId: String!
  eventTemplateId: String!
}

type ProjectData {
  total: Float!
  data: [Project]
}

type ProjectUserStats {
  projectId: ID!
  totalTasks: Float!
  totalCompleted: Float!
}

type EventTemplate {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  name: String
  description: String
  bannerUrl: String
  tags: [TemplateTags!]
  order: Int
  ecosystem: Ecosystem
  displayType: TemplateView!
  eventId: String
  onboarding: [ProjectTemplateOnboardings!]
}

enum TemplateTags {
  COMING_SOON
  POPULAR
  TWITTER
  TELEGRAM
  WEEK_1
  WEEK_2
  WEEK_3
  WEEK_4
  WEEK_5
  WEEK_6
  WEEK_7
}

enum Ecosystem {
  ARBITRUM
  AVAX
  BNB
  CRAB
  ETH
  EVMOS
  IOTEX
  MOONBEAM
  MOONRIVER
  TELOS
  POLYGON
  DOGECHAIN
  POLKADOT
  ASTAR
  POLIMEC
  SHARDEUM
  SONEIUM
}

enum TemplateView {
  DEFAULT
  LARGE
}

type EventReward {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  ruleId: String!
  giveawayId: String!
  wallet: String
  status: RewardStatus!
  hashedIp: String
  txHash: String
  info: GiveawayInfoUnion!
}

enum RewardStatus {
  DRAFT
  ISSUED
  PROCESSING
  FAILED
  SUCCESS
}

union GiveawayInfoUnion = AirTokenGiveawayData | WhitelistGiveawayData | AirPoolGiveawayData | SecretGiveawayData | MerchandiseGiveawayData

type AirTokenGiveawayData {
  airToken: BlockchainAsset!
  rules: [GiveawayRule]

  """Cap"""
  amount: String
  capped: Boolean!

  """amount per winner"""
  winnerAmount: String
  shopConfig: ShopConfig
  guardConfig: TaskGuard
}

type WhitelistGiveawayData {
  reward: String!
  rewardAmount: String
  distributionMsg: String
}

type AirPoolGiveawayData {
  airPool: BlockchainPool!
  rules: [GiveawayRule]

  """Cap"""
  amount: String

  """amount per winner"""
  winnerAmount: String
  capped: Boolean!
  shopConfig: ShopConfig
  guardConfig: TaskGuard
}

type SecretGiveawayData {
  title: String!
  winnerCount: Float
  displayType: DisplayType!
  deliveryType: DeliveryType!
  distributionMsg: String
}

enum DisplayType {
  PLAIN_TEXT
  QR_CODE
}

enum DeliveryType {
  APP_ONLY
  APP_EMAIL
}

type MerchandiseGiveawayData {
  reward: String!
  rewardAmount: Float!
  claimed: Float
  description: String
  shopConfig: ShopConfig
  guardConfig: TaskGuard
  formItems: [FormAnswerTaskData!]!
}

type TaskRule {
  dateValue: DateTime
  intValue: Int
  stringValue: String
  operator: RuleOperator!
  ruleType: TaskRuleType!
}

enum RuleOperator {
  LT
  GT
  EQ
  LTE
  GTE
  NE
}

enum TaskRuleType {
  DATE
  TASK_ID
  XP
  DISCORD_ROLE
  MAX_PARTICIPANTS
}

type TaskGuard {
  condition: GiveawayRuleCondition
  rules: [TaskRule]
}

enum GiveawayRuleCondition {
  AND
  OR
}

type FormAnswerTaskData {
  id: String!
  order: Int!
  title: String!
  widget: FormWidgetType!
  required: Boolean!
  hidden: Boolean
  values: [FormDataValue!]
}

enum FormWidgetType {
  RADIO
  SELECT
  CHECKBOX
  TEXT
  INPUT
  WEBSITE
  EMAIL
  NAME
  BLOCKCHAIN_SELECT
  TOKEN_SELECT
  TOKEN_AMOUNT
  NFT_AMOUNT
  EVM_CONTRACT_ADDRESS
  DATE_INPUT
  NUMBER
  BIG_NUMBER
  NULL
  DOTSAMA_ADDRESS
  AXELAR_CHAIN_SELECT
  AXELAR_ASSET_SELECT
  WORMHOLE_CHAIN_SELECT
}

type FormDataValue {
  value: String!
}

type FormAnswer {
  id: String!
  value: [String]
}

type WidgetOption {
  id: String!
  name: String!
  icon: String
  tag: String
}

type WidgetTaskData {
  id: String!
  order: Int!
  title: String!
  widget: FormWidgetType!
  required: Boolean!
  hidden: Boolean
  values: [FormDataValue!]
  options: [WidgetOption]!
}

type TaskConfig {
  title: String!
  description: String!
  customVerification: Boolean
  connectionGuards: [AuthProvider]
  iconUrl: String
  isPremium: Boolean
  comingSoon: Boolean
  isBeta: Boolean
  hidden: Boolean
  ui: [WidgetTaskData]
  publicUi: [WidgetTaskData]
}

enum AuthProvider {
  DISCORD
  EVM_BLOCKCHAIN
  DOTSAMA_BLOCKCHAIN
  GOOGLE
  TELEGRAM
  TWITTER
  MAGIC_LINK
  PRODUCTHUNT
}

type AppTask {
  taskType: TaskType!
  key: String
  config: TaskConfig!
  renderer: [String!]!
}

enum TaskType {
  DISCORD_JOIN
  FORM_ANSWER
  QUIZ_PLAY
  EVM_CONTRACT
  SUBSTRATE_QUERY
  SUBSTRATE_ASSET_BALANCE
  SUBSTRATE_HOLD_NFT
  SUBSTRATE_BALANCE
  SUBSTRATE_STAKE
  SUBSTRATE_BOND_POOL
  SUBSTRATE_NOMINATE
  SUBSOCIAL_COMMENT
  SUBSOCIAL_FOLLOW
  SUBSOCIAL_POST
  SUBSOCIAL_PROFILE
  SUBSOCIAL_SHARE
  SUBSOCIAL_SPACE
  SUBSOCIAL_UPVOTE
  UPLOAD_FILE
  TELEGRAM_JOIN
  TWITTER_FOLLOW
  TWITTER_JOIN
  TWITTER_LIKE
  TWITTER_POST
  TWITTER_RETWEET
  TWITTER_UGC
  TWITTER_UPLOAD
  TWITTER_LIKE_RETWEET
  TWITTER_WHITELIST
  URL_VISIT
  URL_VIEW
  URL_SHARE
  INSTAGRAM_VISIT
  INSTAGRAM_VIEW
  INSTAGRAM_SHARE
  YOUTUBE_VISIT
  YOUTUBE_VIEW
  YOUTUBE_SHARE
  WALLET_EVM
  WALLET_DOTSAMA
  SUBGRAPH_RAW
  AIRBOOST_REFERRAL
  REST_RAW
  EMAIL_ADDRESS
  EMAIL_WHITELIST
  EMAIL_SUBSCRIBE
  TERMS_TEXT
  TERMS_EVM
  TERMS_DOTSAMA
  CHECKIN_DAILY
  LUCKYDRAW_PLAY
  LUCKYDRAW_SLOT
  LUCKYDRAW_BOX
  SECRET_CODE_VALIDATE
  BLOG_COMMENT
  BLOG_WRITE
  KICKSTARTER_SUPPORT
  FLASHCARD_VIEW
  SUBSQUID_RAW
  SUBQUERY_RAW
  REST_EVM
  REST_DOTSAMA
  FAUCET_DOTSAMA
  FAUCET_EVM
  AIRQUEST_FOLLOW
  MOBILE_APP_INSTALL
  PRODUCTHUNT_UPVOTE
  PRODUCTHUNT_FOLLOW
}

type AppConfig {
  title: String!
  description: String!
  color: String!
  connectionGuards: [AuthProvider]
  bgColor: String
  iconUrl: String
  isPremium: Boolean
}

type AirlyftApp {
  appType: AppType!
  key: String
  config: AppConfig!
  tasks: [AppTask!]!
}

enum AppType {
  DISCORD
  FORM
  MEDIA
  QUIZ
  EVM
  SUBSOCIAL
  TELEGRAM
  TWITTER
  UPLOAD
  URL
  INSTAGRAM
  YOUTUBE
  WALLET
  SUBGRAPH
  AIRBOOST
  REST
  EMAIL
  TERMS
  CHECKIN
  MOBILE_APP
  SECRET_CODE
  BLOG
  KICKSTARTER
  FLASHCARD
  SUBSQUID
  SUBQUERY
  FAUCET
  AIRQUEST
  SUBSTRATE
  LUCKYDRAW
  PRODUCTHUNT
}

type StrippedReferredUser {
  createdAt: DateTime!
  updatedAt: DateTime!
  referredHandle: String!
  referredUserId: String!
  id: String!
  rejected: Boolean!
}

type Condition {
  value: String
  values: [String]
  operator: ConditionOperator!
}

enum ConditionOperator {
  EQ
  LT
  LTE
  GT
  GTE
  RANGE
  IN
  ARR_LENGTH_EQ
  ARR_LENGTH_LT
  ARR_LENGTH_LTE
  ARR_LENGTH_GT
  ARR_LENGTH_GTE
}

type FunctionCondition {
  t: String!
  condition: Condition!
}

type InputParams {
  id: String!
  order: Int!
  title: String!
  value: String!
  widget: String!
}

type FunctionIO {
  name: String!
  type: String!
  internalType: String
  components: [FunctionIO]
  indexed: Boolean
}

type SmartContractFunction {
  name: String!
  type: String!
  constant: Boolean
  anonymous: Boolean
  inputs: [FunctionIO]
  outputs: [FunctionIO]
  stateMutability: String!
  payable: Boolean
}

type FormResponse {
  id: String!
  value: String
}

type FlashcardSection {
  id: String!
  order: Int!
  content: String
}

type ObjectLiteral {
  id: String!
}

type InsertResult {
  identifiers: [ObjectLiteral]
}

type LuckydrawReward {
  amount: Int!
  probability: Int!
}

type LuckydrawTaskParticipationResult {
  insertResult: InsertResult!
  resultIndex: Int!
  xp: Int!
  points: Int!
}

type TokenUsage {
  t: Float
  p: Float
  c: Float
}

type AIVerification {
  reason: String
  verified: Boolean
  description: String
  tokens: TokenUsage
}

type QuizQuestionOption {
  id: String!
  order: Int!
  text: String!
  isCorrect: Boolean
}

type QuizTaskParticipationResult {
  insertResult: InsertResult!
  correctAnswers: [String!]!
  xp: Int!
  points: Int!
}

type RestParam {
  key: String!
  paramType: RestParamType!
  value: String!
}

enum RestParamType {
  STRING
  ARR_STRING
  ADDRESS_32_BYTE
  INT
  BOOL
  JSON
}

type ZapierWebhookData {
  name: String!
  webhookId: String
  webhookUrl: String!
}

type IntegrationTask {
  createdAt: DateTime!
  updatedAt: DateTime!
  task: Task!
  taskId: String!
  integration: Integration!
  integrationId: String!
  routeId: String
  info: IntegrationInfoUnion
}

union IntegrationInfoUnion = NullableTaskData | ApiKeyIntegrationData | ZapierIntegrationData

type NullableTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  isNull: Boolean
}

type ApiKeyIntegrationData {
  createdAt: DateTime!
  updatedAt: DateTime!
  name: String
  webhookUrl: String
}

type ZapierIntegrationData {
  createdAt: DateTime!
  updatedAt: DateTime!
  publicKey: String
  webhooks: [ZapierWebhookData!]
}

type TaskParticipation {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  points: Int!
  xp: Int!
  task: Task
  taskId: String!
  userId: String!
  auth: Auth
  provider: AuthProvider
  providerId: String
  info: TaskParticipationInfoUnion!
  status: ParticipationStatus!
  primaryAuth: Auth
  hashedIp: String
}

union TaskParticipationInfoUnion = FormAnswerTaskParticipationData | TwitterUgcTaskParticipationData | TwitterPostTaskParticipationData | QuizTaskParticipationData | WalletAddressTaskParticipationData | SignTermsTaskParticipationData | UploadTaskParticipationData | FaucetTaskParticipationData | LuckydrawTaskParticipationData | MobileAppTaskParticipationData | SecretCodeTaskParticipationData | BlogCommentTaskParticipationData | BlogWriteTaskParticipationData | NullableTaskData

type FormAnswerTaskParticipationData {
  answers: [FormAnswer!]!
}

type TwitterUgcTaskParticipationData {
  url: String!
  tweet: String
  media: [String!]
}

type TwitterPostTaskParticipationData {
  url: String!
}

type QuizTaskParticipationData {
  answers: [String!]!
  correctAnswers: [String!]
}

type WalletAddressTaskParticipationData {
  address: String!
  verified: Boolean
}

type SignTermsTaskParticipationData {
  address: String
  signature: String
}

type UploadTaskParticipationData {
  aiVerification: AIVerification

  """Example files"""
  urls: [String!]
  maxCount: Int!
  fileTypes: [String!]
  maxSize: Int
}

type FaucetTaskParticipationData {
  address: String!
  amount: String!
  hash: String
}

type LuckydrawTaskParticipationData {
  luckydrawType: LuckydrawType!
  rewardType: RewardType!
  resultIndex: Int!
}

enum LuckydrawType {
  SPINWHEEL
  MYSTERYBOX
  SLOTMACHINE
}

enum RewardType {
  POINTS
  XP
}

type MobileAppTaskParticipationData {
  aiVerification: AIVerification
  url: String!
}

type SecretCodeTaskParticipationData {
  code: String!
}

type BlogCommentTaskParticipationData {
  username: String!
}

type BlogWriteTaskParticipationData {
  blogUrl: String!
}

enum ParticipationStatus {
  VALID
  INVALID
  IN_REVIEW
  IN_AI_VERIFICATION
}

type UserNotification {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  read: Boolean!
  notificationType: UserNotificationType!
  taskId: String
  message: String
  projectPublicLink: String
  eventPublicLink: String
  projectName: String
  eventTitle: String
  taskTitle: String
}

enum UserNotificationType {
  REWARD_SETTLED
  NEW_PROJECT_EVENT
  NEW_SHOP_ITEM
  TASK_INPROGRESS
  TASK_SUCCESS
  TASK_FAILED
  CLAIM_INPROGRESS
  CLAIM_SUCCESS
  CLAIM_FAILED
}

type SubTaskStats {
  count: Int
  totalPoints: Int
  totalXp: Int
}

type Task {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  order: Int!
  points: Int!
  xp: Int!
  title: String
  description: String
  iconUrl: String
  appType: AppType!
  taskType: TaskType!

  """Only applicable for custom apps"""
  appKey: String

  """Only applicable for custom apps and custom tasks"""
  taskKey: String
  frequency: Frequency!
  verify: VerifyType!
  info: TaskInfoUnion!
  subTaskStats: SubTaskStats!
  participantCount: Int
  hidden: Boolean
  guardConfig: TaskGuard
  parentId: String
}

enum Frequency {
  NONE
  DAY
  WEEK
  MONTH
  YEAR
}

enum VerifyType {
  AUTO
  NONE
  MANUAL
  AI
}

union TaskInfoUnion = AirquestFollowTaskData | AirboostReferralTaskData | UploadTaskData | LinkTaskData | TelegramJoinTaskData | QuizTaskData | DiscordJoinTaskData | FormAnswerTaskDataItems | EvmContractInteractTaskData | SubstrateQueryTaskData | TwitterFollowTaskData | TwitterLikeTaskData | TwitterPostTaskData | TwitterRetweetTaskData | TwitterUgcTaskData | TwitterLikeRetweetTaskData | TwitterWhitelistTaskData | SubsocialCommentTaskData | SubsocialFollowTaskData | SubsocialPostTaskData | SubsocialShareTaskData | SubsocialUpvoteTaskData | WalletAddressTaskData | SubgraphRawTaskData | RestRawTaskData | EmailWhitelistTaskData | SignTermsTaskData | FaucetRawTaskData | CustomTaskData | LuckydrawTaskData | MobileAppTaskData | ProducthuntUpvoteTaskData | ProducthuntFollowTaskData | SecretCodeTaskData | BlogCommentTaskData | NullableTaskData | KickstarterTaskData | FlashcardTaskData

type AirquestFollowTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
  projectId: String!
  projectName: String!
}

type AirboostReferralTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  shareTitle: String!
  shareBody: String
  max: Float!
}

type UploadTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!

  """Example files"""
  urls: [String!]
  maxCount: Int!
  fileTypes: [String!]
  maxSize: Int
  prompt: String
}

type LinkTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
}

type TelegramJoinTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  chatId: String!
  username: String!
  type: TelegramChatType!
  title: String!
}

enum TelegramChatType {
  CHANNEL
  SUPER_GROUP
}

type QuizTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  questionType: QuizQuestionType
  options: [QuizQuestionOption!]
}

enum QuizQuestionType {
  SINGLE_CHOICE
  MULTIPLE_CHOICE
}

type DiscordJoinTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
  guildId: String!
  guildName: String!
}

type FormAnswerTaskDataItems {
  createdAt: DateTime!
  updatedAt: DateTime!
  items: [FormAnswerTaskData!]!
}

type EvmContractInteractTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  blockchainId: String!
  contractAddress: String!
  function: SmartContractFunction!
  inputParams: [InputParams]!
  condition: [FunctionCondition!]!
  verifiedWallet: Boolean
}

type SubstrateQueryTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  blockchainId: String!
  section: String!
  method: String!
  inputParams: [FormAnswerTaskData]
  validation: String!
  verifiedWallet: Boolean
  extra: [FormAnswer]
  taskType: SubstrateTaskType
}

enum SubstrateTaskType {
  SUBSTRATE_QUERY
  SUBSTRATE_ASSET_BALANCE
  SUBSTRATE_HOLD_NFT
  SUBSTRATE_BALANCE
  SUBSTRATE_STAKE
  SUBSTRATE_BOND_POOL
  SUBSTRATE_NOMINATE
}

type TwitterFollowTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
}

type TwitterLikeTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
}

type TwitterPostTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  content: String
  type: TweetSubmitType
  url: String
  hashtags: [String!]
  userMentions: [String!]
  mentionsCount: Int
  minimumMediaEntries: Int
}

enum TweetSubmitType {
  TWEET
  QUOTE
  REPLY
}

type TwitterRetweetTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
}

type TwitterUgcTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  includeText: Boolean
  type: TweetSubmitType
  url: String
  hashtags: [String!]
  userMentions: [String!]
  mentionsCount: Int
  minimumMediaEntries: Int
}

type TwitterLikeRetweetTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
  actions: [TwitterAction!]!
}

enum TwitterAction {
  LIKE
  RETWEET
}

type TwitterWhitelistTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!

  """List of twitter handles to whitelist"""
  whitelist: [String!]
}

type SubsocialCommentTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  postId: String!
  postUrl: String!
}

type SubsocialFollowTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  spaceId: String!
  handle: String!
}

type SubsocialPostTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  spaceId: String!
  handle: String!
}

type SubsocialShareTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  postId: String!
  postUrl: String!
}

type SubsocialUpvoteTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  postId: String!
  postUrl: String!
}

type WalletAddressTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  blockchainId: String!
  verify: Boolean
  verification: WalletVerificationType!

  """This is a list of whitelisted when verification is list address"""
  walletList: [String!]
  excludedWallets: [Web3WalletType!]
  verifiedWallet: Boolean
}

enum WalletVerificationType {
  ANY
  LIST
}

enum Web3WalletType {
  EVM_METAMASK
  EVM_SUBWALLET
  EVM_TALISMAN
  EVM_WALLET_CONNECT
  EVM_MANUAL
  DOTSAMA_POLKADOT_JS
  DOTSAMA_TALISMAN
  DOTSAMA_SUBWALLET
  DOTSAMA_NOVA
  DOTSAMA_MANUAL
}

type SubgraphRawTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
  query: String!
  validation: String!
  blockchainId: String!
  blockchainType: BlockchainType!
  verifiedWallet: Boolean
  ss58Prefix: Int
}

type RestRawTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  url: String!
  requestType: RestRequestType!
  queryParams: [RestParam]
  bodyParams: [RestParam]
  headers: [RestParam]
  validation: String!
  blockchainId: String
  verifiedWallet: Boolean
}

enum RestRequestType {
  GET
  POST
}

type EmailWhitelistTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  emailList: [String!]
  verification: EmailVerificationType
}

enum EmailVerificationType {
  ANY
  LIST
}

type SignTermsTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  terms: String!
  blockchainId: String
}

type FaucetRawTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  blockchainId: String!
  amountPerUser: String
  maxUser: Int
  allocationCSVExists: Boolean
  tokenAddress: String
}

type CustomTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  items: [FormAnswer!]!
}

type LuckydrawTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  luckydrawType: LuckydrawType!
  rewardType: RewardType!
  rewards: [LuckydrawReward!]!
  slotMachineIcons: [String!]
}

type MobileAppTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  appName: String
  playStoreUrl: String
  appStoreUrl: String
  prompt: String
}

type ProducthuntUpvoteTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  postTitle: String!
  postUrl: String!
}

type ProducthuntFollowTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  userUrl: String!
}

type SecretCodeTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  secretCodes: [String!]!
}

type BlogCommentTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  blogUrl: String
}

type KickstarterTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  projectUrl: String!
}

type FlashcardTaskData {
  createdAt: DateTime!
  updatedAt: DateTime!
  sections: [FlashcardSection!]
}

type Giveaway {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  giveawayType: GiveawayType!
  distributionType: DistributionType!
  frequency: Frequency
  whitelistWallets: [String]
  startTime: DateTime
  endTime: DateTime
  lastSettledTime: DateTime

  """
  Indicates the latest review status for manual distribution type giveaways.
  """
  latestReviewStatus: GiveawayReviewStatus!
  eventRelativeWindow: Boolean!
  title: String
  description: String
  winningMessage: String
  winnerCount: Int!
  mode: Int!
  gasless: Boolean
  icon: String
  hidden: Boolean
  approved: Boolean
  info: GiveawayInfoUnion!
  hasWhitelist: Boolean!
}

enum GiveawayType {
  COUPON
  AUTO_GENERATED_SECRET
  PRE_GENERATED_SECRET
  WHITELIST
  TOKEN_WHITELIST
  NFT_WHITELIST
  ERC20_AIR_POOL
  ERC20_AIR_TOKEN
  ERC1155_AIR_POOL
  ERC1155_AIR_TOKEN
  ERC721_AIR_POOL
  ERC721_AIR_TOKEN
  MERCHANDISE
  DOTSAMA_TOKEN_AIR_POOL
  DOTSAMA_TOKEN_AIR_TOKEN
  DOTSAMA_NFT_AIR_TOKEN
  DOTSAMA_NFT_AIR_POOL
}

enum DistributionType {
  RANDOM_END
  RANDOM_SPECIFIC_END
  RANDOM_REC
  MANUAL_END
  MANUAL_CONTINUOUS
  MANUAL_REC
  RANK_END
  RANK_REC
  FCFS_RANGE_END
  FCFS_RANGE
  FCFS_TASK_ID
  SHOP
}

enum GiveawayReviewStatus {
  NONE
  IN_REVIEW
  REVIEWED
}

type IntegrationEvent {
  createdAt: DateTime!
  updatedAt: DateTime!
  eventId: String!
  projectId: String!
  integration: Integration!
  integrationId: String!
  routeId: String
  info: IntegrationInfoUnion
}

type ProjectEventSummary {
  totalTasks: Int
  totalParticipants: Int
  totalTaskParticipation: Int
  totalXP: Int
  totalPoints: Int
  totalPointsEarned: Int
}

type Season {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  name: String!
  projectId: String!
}

type GiveawaySummary {
  title: String
  giveawayType: GiveawayType!
  icon: String
  blockchainId: String
}

type ProjectEvent {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  title: String!
  startTime: DateTime
  publicLink: String!
  endTime: DateTime
  description: String
  bannerUrl: String
  state: EventState!
  settlementFiles: [String]
  settledAt: DateTime
  eventType: EventType!
  visibility: EventVisibility!
  mode: Int
  summary: ProjectEventSummary
  rewardTitle: String
  rewardSubtitle: String
  ipProtect: Boolean
  leaderboard: LeaderboardType!
  seasonId: String
  tags: [String]
  project: Project!
  giveawaySummary: [GiveawaySummary]
}

enum EventState {
  DRAFT
  COMMITTED
  SCHEDULED
  ONGOING
  READY_TO_SETTLE
  SETTLED
}

enum EventType {
  DEFAULT_QUEST
  CAMPAIGN
  E_LEARNING
}

enum EventVisibility {
  PRIVATE
  PUBLIC
  PUBLIC_EXPLORABLE
}

enum LeaderboardType {
  NONE
  EVENT
  SEASON
}

type EventConnection {
  createdAt: DateTime!
  updatedAt: DateTime!
  provider: AuthProvider!
  providerId: String!
}

type Auth {
  createdAt: DateTime!
  updatedAt: DateTime!
  provider: AuthProvider!
  providerId: String!
  firstName: String
  lastName: String
  picture: String
  username: String
  isPrimary: Boolean!
  verified: Boolean!
  userId: String
}

type Integration {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!

  """Integration Platform Name (enum)"""
  name: String

  """displayName is a user-friendly name of the integration"""
  displayName: String
  type: IntegrationType!
  auth: Auth!
  provider: AuthProvider
  providerId: String
  events: [IntegrationEvent!]
  tasks: [IntegrationTask!]
  info: IntegrationInfoUnion!
}

enum IntegrationType {
  API_KEY_INTEGRATION
  ZAPIER_INTEGRATION
  WEBHOOKS_INTEGRATION
}

type ApiKey {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  projectId: String!
  key: String!
}

type Project {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  name: String!
  publicLink: String!
  bio: String
  logo: String
  bannerUrl: String
  contactEmail: String
  webhookSecret: String
  verified: Boolean
  visibility: ProjectVisibility
  isHideQbRecentEvents: Boolean
  projectUrls: [ProjectUrl!]
  projectEvents: [ProjectEvent!]
  integrations: [Integration!]
  authorization: [Authorization!]
  followerCount: Float!
  ecosystems: [Ecosystem!]
  referredEcosystem: Ecosystem
  referralId: String
  sectors: [Sector!]
  plan: PaymentPlan!
  billings: [Billing!]
  seasons: [Season!]
}

enum ProjectVisibility {
  PRIVATE
  PUBLIC
  PUBLIC_EXPLORABLE
}

enum Sector {
  DEFI
  GAMEFI
  NFT
  DAO
  METVERSE
  DEX
  STAKING
  BRIDGE
  LAUNCHPAD
  CEFI
  COLLECTIBLES
  WEB3
  DID
  SOCIAL
  INFRASTRUCTURE
  WALLET
  PRIVACY
  STORAGE
  CEX
}

type ProjectUrl {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  urlType: ProjectUrlType!
  url: String!
  data: JSONObject
}

enum ProjectUrlType {
  DISCORD
  FACEBOOK
  GITHUB
  INSTAGRAM
  REDDIT
  TELEGRAM
  TOKENOMICS
  TWITTER
  WEBSITE
  WHITEPAPER
  YOUTUBE
}

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Referral {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
}

type ReferralCode {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  code: String!
  expiresAt: DateTime
  referrals: Int
}

type ConflictedUserProfile {
  picture: String
  displayName: String!
  providerType: AuthProvider!
}

type User {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  username: String
  firstName: String
  lastName: String
  email: String
  avatar: String
  auth: [Auth!]!
  onboarded: [Onboarding!]
  cookieConsent: CookieConsent
  auths: [Auth!]!
}

enum Onboarding {
  AIRPOOLS
  AIRTOKENS
  CAMPAIGNS
  NOTIFICATION_EMAIL
  PARTICIPANT_TERMS
}

enum CookieConsent {
  NECESSARY
  ACCEPT_ALL
}

type PublicUser {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  username: String
  firstName: String
  lastName: String
  avatar: String
  onboarded: [Onboarding!]
  cookieConsent: CookieConsent
  rep: Int!
}

type Contract {
  createdAt: DateTime!
  updatedAt: DateTime!
  address: String!
  owner: String!
  deployer: String!
  ca: String!
  blockchain: Blockchain!
  user: User!
}

type BlockchainPool {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  name: String
  poolId: String!
  amount: String!
  creatorBlockchainAddress: String!
  asset: BlockchainAsset!
  tokenIds: [String]
  status: TransactionStatus!
  contractAddress: String!
  createdById: String!
  dotsamaNfts: [DotsamaNft!]
}

enum TransactionStatus {
  ISSUED
  PROCESSING
  FAILED
  SUCCESS
}

type BlockchainAsset {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  address: String!
  tokenId: String!
  name: String!
  description: String
  ticker: String!
  decimals: Int!
  assetType: AssetType!
  creatorBlockchainAddress: String
  icon: String
  color: String
  website: String
  txHash: String
  external: Boolean
  status: BlockchainIntegrationStatus!
  blockchain: Blockchain!
  blockchainId: String!
  createdById: String!
  txStatus: TransactionStatus!
  contractAddress: String
  dotsamaNfts: [DotsamaNft!]
}

enum AssetType {
  ERC20
  ERC721
  ERC1155
  NATIVE
  DOTSAMA_TOKEN
  DOTSAMA_NFT
}

type ProjectEventData {
  total: Float!
  data: [ProjectEvent]
}

type DeleteResult {
  affected: Int
}

type FollowedProject {
  project: Project!
  totalXp: Int!
  totalPoints: Int!
}

type FollowedProjectData {
  total: Float!
  data: [FollowedProject]
}

type ProjectUserInfo {
  xp: Int
  fuel: Int
  currentLevel: Int
  levelLimit: Int
  rank: Int
  follows: Boolean
  userId: ID
}

type ProjectFuelInfo {
  count: Int
  min: Int
  max: Int
  avg: Float
}

type Participant {
  userId: String!
  totalTask: Int!
  totalPoints: Int!
  totalXp: Int!
  displayName: String!
  isChosen: Boolean!
  primaryAuth: Auth
  rank: Int
}

type TaskSubmissionsData {
  total: Float!
  data: [TaskParticipation!]!
}

type ParticipantsData {
  total: Float!
  data: [Participant!]!
}

type LeaderboardItem {
  user: User!
  rank: Int!
  xp: Int!
}

type LeaderboardData {
  total: Float!
  data: [LeaderboardItem]
  hasMore: Boolean!
}

type GlobalLeaderboardItem {
  userId: String!
  username: String
  firstName: String
  avatar: String
  userSince: DateTime!
  rank: Float!
  onchainQuests: Float!
  offchainQuests: Float!
  totalQuests: Float!
  onchainPoints: Float!
  offchainPoints: Float!
  totalPoints: Float!
  onchainXp: Float!
  offchainXp: Float!
  totalXp: Float!
  referrals: Float!
  longestStreak: Float!
  totalActivity: Float!
  totalProjects: Float!
}

type GlobalLeaderboardData {
  total: Float!
  data: [GlobalLeaderboardItem]
  hasMore: Boolean!
}

type UpdateResult {
  affected: Int
}

type BlockchainAssetsResponse {
  data: [BlockchainAsset!]!
  total: Int!
}

type EVMContractDeployResult {
  address: ID!
  contractType: ContractType!
}

enum ContractType {
  ERC20_AIRBASE
  ERC20_AIRPOOL
  ERC721_AIRBASE
  ERC721_AIRPOOL
  ERC1155_AIRBASE
  ERC1155_AIRPOOL
  DOTSAMA_ASSET
}

type CertificateResult {
  r: String!
  s: String!
  v: Int!
  deadline: String!
  nonce: String!
  signature: String!
  type: String!
}

type CreateAssetHubAirTokenResult {
  assetId: String!
  insertResult: InsertResult!
  txHash: String!
}

type CreateAirTokenResult {
  """Token Contract address"""
  contractAddress: String!

  """Token Contract owner"""
  owner: String!

  """Token base uri"""
  tokenBaseURI: String
  certificate: CertificateResult!
  insertResult: InsertResult!
}

type DauData {
  date: DateTime!
  count: Int!
}

type KeyValueGroup {
  createdAt: DateTime!
  updatedAt: DateTime!
  groupId: String!
  key: String!
  value: String!
}

type MauData {
  date: DateTime!
  count: Int!
}

type WalletAddressDateData {
  date: DateTime!
  count: Int!
}

type TaskDateParticipationsData {
  userCount: Int!
  taskId: String!
  date: DateTime!
  name: String!
  title: String!
}

type UserCountPoint {
  weekStart: DateTime!
  sum: Int!
}

type Invitation {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  projectId: String!
  createdById: String
  invitationCode: String!
  isUsed: Boolean!
  project: Project!
}

type CountryStat {
  countryCode: String!
  countryName: String!
  count: Int!
}

type LoginToken {
  token: String!
}

type AuthSubscriptionResponse {
  token: String
  error: String
}

type EventConnectionInsertResult {
  eventId: ID!
  userId: ID!
  provider: AuthProvider!
  providerId: ID!
}

type EventConnectionUser {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  username: String
  firstName: String
  lastName: String
  avatar: String
  onboarded: [Onboarding!]
  cookieConsent: CookieConsent
  auth: Auth!
}

type EventParticipantData {
  total: Float!
  data: [ProjectEvent]
}

type PlatformField {
  label: String!
  name: String!
  regex: String
  placeholder: String
}

type IntegrationPlatformConfig {
  name: String!
  platformType: IntegrationPlatform!
  bgColor: String!
  color: String!
  description: String!
  fields: [PlatformField!]
}

enum IntegrationPlatform {
  MAILCHIMP
  CONVERTKIT
  CUSTOMERIO
  KLAVIYO
  MAILERLITE
  MAILJET
  MARKETO
  SENDGRID
  SENDINBLUE
  SHOPIFY
  TELEGRAM_BOT
  ZAPIER
  WEBHOOKS
}

type PoolGiveawaySummary {
  id: String!
  amount: String!
  title: String!
  eventId: String!
}

type DotsamaPoolGiveawaySummary {
  id: String!
  amount: String!
  title: String!
  eventId: String!
  claimedAmount: String!
}

type GiveawayRule {
  id: String!
  min: Int
  max: Int
  amount: String!
  ruleType: GiveawayRuleType
  condition: GiveawayRuleCondition
  ids: [String]
}

enum GiveawayRuleType {
  POINTS
  TASK_COUNT
  TASK_ID
}

type NftSwap {
  address: String!
  blockchainId: String!
}

type ShopConfig {
  ruleType: GiveawaySwapRuleType!
  points: Int
  nfts: [NftSwap]
  amount: String!
  decimals: Int
}

enum GiveawaySwapRuleType {
  BURN_POINTS
  BURN_NFT
}

type GiveawaysData {
  total: Float!
  data: [Giveaway]
}

type RewardClaimResult {
  raw: [EventReward!]!
}

type RewardCertificate {
  raw: [EventReward!]!
  certificate: CertificateResult!
  claimIds: [String!]!
  window: String!
  windowLimit: String!
  amount: String!

  """Only for gasless tx"""
  txHash: String
}

type EventRewardData {
  giveaway: Giveaway!
  giveawayId: String!
  status: RewardStatus!
}

type EventRewardsResponse {
  data: [EventRewardData!]!
  total: Int!
}

type UserNotificationData {
  total: Float!
  data: [UserNotification]!
  unreadCount: Int!
}

type WithdrawBlockchainPoolOutput {
  hash: String!
  updateResult: UpdateResult!
}

type CreateBlockchainPoolResult {
  """Blockchain pool id"""
  poolId: String!
  certificate: CertificateResult!
  insertResult: InsertResult!
}

type CreateDotsamaBlockchainPoolResult {
  """pool id"""
  poolId: String!
  insertResult: InsertResult!
}

type SyncBlockchainPoolResult {
  status: TransactionStatus!
  amount: String!
}

type VerificationResult {
  aiVerification: AIVerification
}

type PromotedEvent {
  createdAt: DateTime!
  updatedAt: DateTime!
  event: ProjectEvent!
  eventId: String!
  promotionType: PromotionType!
}

enum PromotionType {
  EXPLORE_FEATURE
}

type PromotedProject {
  createdAt: DateTime!
  updatedAt: DateTime!
  project: Project!
  projectId: String!
  promotionType: PromotionType!
}

type Winner {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  ruleId: String!
  giveawayId: String!
  wallet: String
  status: RewardStatus!
  hashedIp: String
  txHash: String
  info: GiveawayInfoUnion!
  displayName: String!
  amount: String
  primaryAuth: Auth
  burnPoints: Int
  formAnswers: [FormAnswer]
}

type WinnersData {
  total: Float!
  data: [Winner!]!
}

type HSLColor {
  h: Float!
  s: Float!
  l: Float!
}

type WidgetColorConfig {
  primary: HSLColor
  primaryForeground: HSLColor
  background: HSLColor
  foreground: HSLColor
  popover: HSLColor
  popoverForeground: HSLColor
  secondary: HSLColor
  secondaryForeground: HSLColor
  accent: HSLColor
  accentForeground: HSLColor
  muted: HSLColor
  mutedForeground: HSLColor
  border: HSLColor
  link: HSLColor
}

type WidgetConfig {
  color: WidgetColorConfig
  forcedTheme: ThemeMode
  hideXp: Boolean
  hideFuel: Boolean
}

enum ThemeMode {
  DARK
  LIGHT
}

type Widget {
  createdAt: DateTime!
  updatedAt: DateTime!
  id: ID!
  domain: String
  eventId: String!
  config: WidgetConfig
  event: ProjectEvent!
}

input TaskRuleInput {
  dateValue: DateTime
  intValue: Int
  stringValue: String
  operator: RuleOperator!
  ruleType: TaskRuleType!
}

input TaskGuardInput {
  condition: GiveawayRuleCondition
  rules: [TaskRuleInput]
}

input FormAnswerTaskDataInput {
  id: String!
  order: Int!
  title: String!
  widget: FormWidgetType!
  required: Boolean!
  hidden: Boolean
  values: [FormDataValueInput!]
}

input FormDataValueInput {
  value: String!
}

input FormAnswerInput {
  id: String!
  value: [String]
}

input ConditionInput {
  value: String
  values: [String]
  operator: ConditionOperator!
}

input FunctionConditionInput {
  t: String!
  condition: ConditionInput!
}

input FunctionInputParamsInput {
  id: String!
  order: Int!
  title: String!
  value: String!
  widget: String!
}

input FunctionIOInput {
  name: String!
  type: String!
  internalType: String
  components: [FunctionIOInput]
  indexed: Boolean
}

input SmartContractFunctionInput {
  name: String!
  type: String!
  constant: Boolean
  anonymous: Boolean
  inputs: [FunctionIOInput]
  outputs: [FunctionIOInput]
  stateMutability: String!
  payable: Boolean
}

input FormResponseInput {
  id: String!
  value: String
}

input FlashcardSectionInput {
  id: String!
  order: Int!
  content: String
}

input LuckydrawRewardInput {
  amount: Int!
  probability: Int!
}

input QuizTaskDataOptionInput {
  id: String!
  order: Int!
  text: String!
  isCorrect: Boolean
}

input RestParamInput {
  key: String!
  paramType: RestParamType!
  value: String!
}

input ZapierWebhookDataInput {
  name: String!
  webhookId: String
  webhookUrl: String!
}

input NftSwapInput {
  address: String!
  blockchainId: String!
}

input ShopConfigInput {
  ruleType: GiveawaySwapRuleType!
  points: Int
  nfts: [NftSwapInput]
  amount: String!
  decimals: Int
}

input HSLColorInput {
  h: Float!
  s: Float!
  l: Float!
}

input WidgetColorConfigInput {
  primary: HSLColorInput
  primaryForeground: HSLColorInput
  background: HSLColorInput
  foreground: HSLColorInput
  popover: HSLColorInput
  popoverForeground: HSLColorInput
  secondary: HSLColorInput
  secondaryForeground: HSLColorInput
  accent: HSLColorInput
  accentForeground: HSLColorInput
  muted: HSLColorInput
  mutedForeground: HSLColorInput
  border: HSLColorInput
  link: HSLColorInput
}

input WidgetConfigInput {
  color: WidgetColorConfigInput
  forcedTheme: ThemeMode
  hideXp: Boolean
  hideFuel: Boolean
}

type Query {
  """Get Task by id"""
  task(taskId: ID!, projectId: ID!): Task!

  """Get Tasks by eventId"""
  tasks(eventId: ID!, projectId: ID!): [Task!]!

  """Get all sub tasks by parent taskId"""
  tasksByParentId(parentId: ID!, projectId: ID!): [Task!]!

  """Get task by id"""
  pTask(taskId: ID!): Task!

  """Get Tasks by eventId"""
  pTasks(eventId: ID!): [Task!]!

  """Get all sub tasks by parent taskId"""
  pTasksByParentId(parentId: ID!): [Task!]!

  """Get a user's XP and fuel for a projectId"""
  projectUserInfo(projectId: ID!): ProjectUserInfo

  """Get fuel stats for a projectId"""
  projectFuelInfo(projectId: ID!, minFuel: Float!): ProjectFuelInfo

  """Get Paginated list of users and their leaderboard position"""
  globalLeaderboardInternal(where: GlobalLeaderboardWhereInput!, sorter: GlobalLeaderboardSortInput!, pagination: PaginationInput!): GlobalLeaderboardData

  """Get total participations"""
  totalParticipations: Int

  """
  Get Paginated list of users and their XP for a given project or event. If interval is provided then this always returns the project leaderboard, else if season is provided then season leaderboard, else the event leaderboard
  """
  leaderboard(projectId: ID!, pagination: PaginationInput!, where: LeaderBoardWhereInput!, sorter: SortInput!): LeaderboardData!

  """
  Get leaderboard position for the logged in user in a given project in a specific interval.
  """
  userProjectRanking(projectId: String!, interval: LeaderboardInterval!): LeaderboardItem

  """Get Paginated list of users and their leaderboard position"""
  globalLeaderboard(where: GlobalLeaderboardWhereInput!, sorter: GlobalLeaderboardSortInput!, pagination: PaginationInput!): GlobalLeaderboardData

  """
  Get global leaderboard position for the logged in user in a specific interval.
  """
  userGlobalRanking(where: GlobalLeaderboardWhereInput!, sorter: GlobalLeaderboardSortInput!): GlobalLeaderboardItem

  """
  Get a users XP, cFuel and number of tasks completed for a give provider and providerId
  """
  projectUserInfoByProvider(projectId: ID!, provider: AuthProvider!, providerId: ID!): ProjectUserInfo

  """Get list of projects followed by a given user"""
  followedProjects(pagination: PaginationInput!, userId: ID!): FollowedProjectData!
  giveaway(giveawayId: ID!): Giveaway!
  giveaways(eventId: ID!): [Giveaway!]!
  poolGiveawaysSummary(poolId: ID!): [PoolGiveawaySummary!]!
  dotsamaPoolGiveawaysSummary(id: ID!): [DotsamaPoolGiveawaySummary!]!
  poolGiveaways(poolId: ID!): [Giveaway!]!
  pGiveaways(eventId: ID!): [Giveaway!]!
  pShopGiveaways(pagination: PaginationInput!, sorter: GiveawaySortInput!): GiveawaysData!
  blockchain(id: ID!): Blockchain!
  blockchains(where: BlockchainWhereInput!): [Blockchain!]
  blockchainAssets(blockchainId: ID!, where: BlockchainAssetWhereInput): [BlockchainAsset!]
  getDotsamaPoolAssetNftItems(blockchainPoolId: ID!, blockchainAssetId: ID!): [DotsamaNft!]

  """Internal Use Only - Get all whitelisted assets on the platform"""
  airpoolAssetWhitelist(pagination: PaginationInput!): BlockchainAssetsResponse

  """Get User's projects with pagination"""
  projects: [Project!]!

  """Get all projects that the logged in user has some authorization to"""
  project(projectId: ID!): Project

  """Check if public URL is available"""
  isProjectUrlAvailable(url: String!, projectId: ID): Boolean!

  """Get Project Progress"""
  projectsProgress(projectIds: [ID!]!): [ProjectUserStats!]!

  """Internal Use Only - Get all projects with pagination"""
  allProjects(pagination: PaginationInput!, where: AllProjectsWhereInput!): ProjectData!

  """Search projects"""
  exploreProjects(pagination: PaginationInput!, where: ProjectWhereInput, sorter: ProjectSortInput): ProjectData!

  """Get project details by link"""
  projectByLink(publicLink: String!): Project!

  """Get questboard event for project"""
  defaultProjectEvent(projectId: ID!): ProjectEvent!

  """Get project event by id"""
  projectEvent(projectId: ID!, eventId: ID!): ProjectEvent!

  """Search events"""
  projectEvents(pagination: PaginationInput!, where: ProjectEventWhereInput!): ProjectEventData!

  """Check if a public url is available"""
  isEventUrlAvailable(projectId: ID!, publicLink: ID!, eventId: ID): Boolean!

  """Get questboard event by project public link"""
  projectDefaultEventByLink(projectPublicLink: String!): ProjectEvent

  """Get project event by public link"""
  projectEventByLink(projectPublicLink: String!, publicLink: String!): ProjectEvent

  """Search public events"""
  exploreEvents(pagination: PaginationInput!, where: PublicProjectEventWhereInput, sorter: ProjectEventSortInput): ProjectEventData!

  """Count of published events by projectId"""
  eventsCount(projectId: ID!): Int!

  """Count of published events by all projects"""
  allEventsCount: Int!
  userEventRewards(eventId: ID!): [EventReward!]!

  """If user is shortlisted for an event with a giveaway"""
  findUserRewardByStatus(eventId: ID!, giveawayId: ID!): [EventReward!]!
  userRewards(pagination: PaginationInput!, userId: ID!): EventRewardsResponse!

  """Get logged in user's participations"""
  userTaskParticipation(eventId: ID!): [TaskParticipation!]!

  """Get task participation data"""
  participation(participationId: String!): TaskParticipation!
  eventParticipants(projectId: ID!, pagination: PaginationInput!, where: ParticipantWhereInput!, sorter: SortInput!): ParticipantsData!

  """Get task submission data"""
  eventSubmissions(projectId: ID!, pagination: PaginationInput!, where: SubmissionWhereInput!): TaskSubmissionsData!
  globalReferralCodes: [ReferralCode]

  """Check if code is available"""
  isReferralCodeAvailable(code: String!, referralCodeId: ID): Boolean!

  """Get integrations by eventId"""
  integrationEvent(eventId: ID!, projectId: ID!): [IntegrationEvent!]!

  """Get all notification for the logged in user"""
  userNotifications(pagination: PaginationInput!): UserNotificationData!

  """Get notifications by IDs (max 10)"""
  userNotificationsByIds(ids: [ID!]!): UserNotificationData!

  """Get Logged in User details"""
  me(projectId: ID): User!
  conflictedUserProfile(provider: AuthProvider!, providerId: ID!): ConflictedUserProfile

  """"""
  getTopCountries: [CountryStat!]
  isUsernameAvailable(data: UsernameInput!): Boolean!

  """User details"""
  user(userId: ID!, username: String): PublicUser!

  """Get user stats in your project using user's providerId"""
  userByProvider(projectId: ID!, providerId: String!, provider: AuthProvider!): ProjectUserInfo!

  """Get a temporary userId"""
  tempUserId: ID
  dotsamaWalletAddress(blockchainId: ID!, projectId: ID!): String!
  evmWalletAddress(projectId: ID!): String!

  """Get message to be singed by the user's wallet."""
  signingMessage(projectId: ID!, walletAddress: ID!): String!
  connectionToken(provider: AuthProvider!): String!

  """Get all users in a project with user details"""
  projectUsersWithUserDetails(projectId: ID!): [Authorization!]

  """Get user role in a project"""
  userRoleInProject(projectId: ID!): Role
  getCustomApps: [AirlyftApp!]!
  eventConnections(eventId: ID!): [EventConnection!]!
  eventConnectionUser(projectId: ID!, eventId: ID!, userId: ID!): EventConnectionUser!
  userEventParticipated(userId: ID!, pagination: PaginationInput!): EventParticipantData!
  contract(contractType: ContractType!, blockchainId: ID!): Contract

  """Get integrations by taskId"""
  integrationTask(taskId: ID!, projectId: ID!): [IntegrationTask!]!
  referredUsers(eventId: ID!): [StrippedReferredUser!]!
  referredUsersForUserId(eventId: ID!, userId: ID!): [StrippedReferredUser!]!

  """Get Records by group id"""
  keyValueGroups(groupId: ID!): [KeyValueGroup!]

  """Get record value"""
  valueByKeyGroup(groupId: ID!, key: String!): String
  blockchainPools(projectId: ID!, where: BlockchainPoolWhereInput): [BlockchainPool!]!
  blockchainPool(projectId: ID!, id: ID!): BlockchainPool!

  """"""
  getDau(noOfDays: Int!): [DauData!]

  """"""
  getMau(noOfMonths: Int!): [MauData!]

  """"""
  getWalletAddressDate(noOfDays: Int!): [WalletAddressDateData!]

  """"""
  getTopNQuest(top: Int, startDate: DateTime, endDate: DateTime): [TaskDateParticipationsData!]
  getUserCount(type: UserCountType!): [UserCountPoint!]!
  verifyImage(prompt: String!, imageUrl: String!): VerificationResult!
  globalTemplates(pagination: PaginationInput): [EventTemplate!]!
  ecosystemTemplates(ecosystem: Ecosystem!, projectId: ID!, pagination: PaginationInput): [EventTemplate!]!

  """Get Integration details"""
  integration(projectId: ID!, integrationId: ID!): Integration!

  """Get all integrations"""
  allIntegrationsByPlatformName(projectId: ID!, platformName: String!): [Integration!]!
  getIntegrationPlatformConfig(projectId: ID!): [IntegrationPlatformConfig!]!

  """Get all AirTokens created by the project"""
  airTokens(projectId: ID!, blockchainId: ID, assetType: AssetType): [BlockchainAsset!]!

  """AirToken details"""
  airToken(projectId: ID!, id: ID!): BlockchainAsset!

  """Get a list of promoted events"""
  promotedEvents(promotionType: PromotionType!, ecosystems: [Ecosystem]): [PromotedEvent!]

  """Get a list of promoted projects"""
  promotedProjects(promotionType: PromotionType!, ecosystems: [Ecosystem]): [PromotedProject!]

  """Get invite code issuing project"""
  getProjectFromInviteId(inviteId: ID!): Invitation!

  """Get all invite codes"""
  getInvitationCodes(projectId: ID!): [Invitation!]!
  billingPlans: [BillingPlan!]
  billingDetails(projectId: ID!): BillingDetails
  projectParticipationCount(projectId: ID!): Float
  projectAiVerifiedParticipationCount(projectId: ID!): Float
  enterpriseTrailEligible(projectId: ID!): Boolean!
  getBillings(projectId: ID!): [Billing!]!

  """Get winners list"""
  winners(projectId: ID!, pagination: PaginationInput!, where: WinnerWhereInput!): WinnersData!

  """Get Seasons list"""
  seasons(projectId: ID!): [Season]

  """Get Season details by id"""
  season(seasonId: ID!): Season!

  """Get widget details."""
  widget(id: ID!): Widget!

  """Get all widgets created for an event."""
  widgets(projectId: ID!, eventId: ID!): [Widget!]!
}

input GlobalLeaderboardWhereInput {
  projectId: ID
  interval: LeaderboardInterval
  date: DateInput
}

enum LeaderboardInterval {
  WEEK
  MONTH
  ALL_TIME
}

input DateInput {
  start: DateTime!
  end: DateTime!
}

input GlobalLeaderboardSortInput {
  sortKey: GlobalLeaderboardSortKey!
  direction: SortDirection!
}

"""Valid sort keys for the query"""
enum GlobalLeaderboardSortKey {
  ONCHAIN_POINTS
  OFFCHAIN_POINTS
  TOTAL_POINTS
  ONCHAIN_XP
  OFFCHAIN_XP
  TOTAL_XP
  ONCHAIN_QUESTS
  OFFCHAIN_QUESTS
  TOTAL_QUESTS
  REFERRALS
  TOTAL_ACTIVITY
  LONGEST_STREAK
  TOTAL_PROJECTS
}

enum SortDirection {
  DESC
  ASC
}

input PaginationInput {
  take: Int!
  skip: Int!
}

input LeaderBoardWhereInput {
  eventId: ID
  seasonId: ID
  interval: LeaderboardInterval
}

input SortInput {
  sortKey: String!
  direction: SortDirection!
}

input GiveawaySortInput {
  sortKey: GiveawaySortKey!
  direction: SortDirection!
}

"""Valid sort keys for the query"""
enum GiveawaySortKey {
  CREATED_AT
  FUEL
}

input BlockchainWhereInput {
  contractType: ContractType
  blockchainType: BlockchainType
}

input BlockchainAssetWhereInput {
  assetType: String
  statusIn: [BlockchainIntegrationStatus]
  tokenId: String
}

input AllProjectsWhereInput {
  publicUrl: String
  plan: PaymentPlan
  verified: String
  supportEnabled: String
}

input ProjectWhereInput {
  ecosystems: [Ecosystem!]
  sectors: [Sector!]
}

input ProjectSortInput {
  sortKey: ProjectSortKey!
  direction: SortDirection!
}

"""Valid sort keys for the query"""
enum ProjectSortKey {
  CREATED_AT
  TRENDING
}

input ProjectEventWhereInput {
  state: [EventState!]!
  projectId: ID!
  visibility: [AccountEventVisibility]
  ecosystems: [Ecosystem!]
  tags: [String]
}

enum AccountEventVisibility {
  PRIVATE
  PUBLIC
}

input PublicProjectEventWhereInput {
  state: [PublicEventState!]!
  visibility: [PublicEventVisibility!]!
  projectId: ID
  ecosystems: [Ecosystem!]
  tags: [String]
  eventType: EventType
}

enum PublicEventState {
  SCHEDULED
  ONGOING
  READY_TO_SETTLE
  SETTLED
}

enum PublicEventVisibility {
  PUBLIC
  PUBLIC_EXPLORABLE
}

input ProjectEventSortInput {
  sortKey: ProjectEventSortKey!
  direction: SortDirection!
}

"""Valid sort keys for the query"""
enum ProjectEventSortKey {
  CREATED_AT
  TRENDING
}

input ParticipantWhereInput {
  eventId: ID!
  taskIds: [ID]
  date: DateInput
}

input SubmissionWhereInput {
  eventId: ID!
  taskIds: [ID]
  userId: String
  status: [ParticipationStatus!]
  date: DateInput
}

input UsernameInput {
  username: String
}

input BlockchainPoolWhereInput {
  assetType: String
  blockchainId: String
  statusIn: [TransactionStatus]
}

enum UserCountType {
  WEEKLY
  TOTAL
}

input WinnerWhereInput {
  eventId: String!
  giveawayId: String!
  status: [RewardStatus]
  date: DateInput
}

type Mutation {
  """Change task ordering"""
  reorderTask(projectId: ID!, taskId: ID!, order: Int!): UpdateResult!

  """Hide task"""
  hideTask(projectId: ID!, taskId: ID!, hidden: Boolean!): UpdateResult!

  """Delete a task"""
  deleteTask(projectId: ID!, taskId: ID!): DeleteResult!

  """Clone a task"""
  cloneTask(projectId: ID!, eventId: ID!, taskId: ID!): ID!

  """Create project following by the logged in user"""
  follow(projectId: ID!): InsertResult!

  """Unfollow project by the logged in user"""
  unfollow(projectId: ID!): DeleteResult!

  """Delete a giveaway"""
  deleteGiveaway(projectId: ID!, giveawayId: ID!): DeleteResult!

  """Update hidden flag in giveaway"""
  hideGiveaway(projectId: ID!, giveawayId: ID!, hidden: Boolean!): UpdateResult!

  """Internal Use Only - Add a new blockchain on the platform"""
  addBlockchain(data: BlockchainInput!): Blockchain!

  """Internal Use Only - Update an existing blockchain on the platform"""
  updateBlockchain(id: ID!, data: BlockchainUpdateInput!): UpdateResult!

  """Internal Use Only - Delete a blockchain from the platform"""
  deleteBlockchain(id: ID!): DeleteResult!

  """Internal Use Only - Add a new blockchain asset on the platform"""
  addBlockchainAsset(data: BlockchainAssetInput!): BlockchainAsset!

  """
  Internal Use Only - Update an existing blockchain asset on the platform
  """
  updateBlockchainAsset(id: ID!, data: BlockchainAssetUpdateInput!): UpdateResult!

  """Internal Use Only - Delete a blockchain asset from the platform"""
  deleteBlockchainAsset(id: ID!): DeleteResult!

  """Create new Project"""
  createProject(data: ProjectInput!): InsertResult!

  """Update a project created by logged in user"""
  updateProject(projectId: ID!, data: ProjectUpdateInput!): UpdateResult!

  """Delete a project created by logged in user"""
  deleteProject(projectId: ID!): ID!

  """Delete a project url created by logged in user"""
  deleteProjectUrl(id: ID!, projectId: ID!): DeleteResult!

  """Create new Project Url"""
  createProjectUrl(projectId: ID!, data: ProjectUrlInput!): InsertResult!

  """Create Project Webhook secret"""
  createProjectwebhookSecret(projectId: ID!): String!

  """Update a project url created by logged in user"""
  updateProjectUrl(id: ID!, projectId: ID!, data: ProjectUrlUpdateInput!): UpdateResult!

  """Internal Use Only - Update a project visibility and verified status"""
  updateProjectVisibility(projectId: ID!, data: ProjectVisibilityUpdateInput!): UpdateResult!
  superAdminProjectAuthorization(projectId: ID!, status: Boolean!): Boolean!

  """Create a project event. Events are always created in a DRAFT state"""
  createProjectEvent(projectId: ID!, data: ProjectEventInput!): InsertResult!

  """Delete an event"""
  deleteProjectEvent(projectId: ID!, eventId: ID!): DeleteResult!

  """Update a project event"""
  updateProjectEvent(projectId: ID!, eventId: ID!, data: ProjectEventUpdateInput!): UpdateResult!

  """Create event from template"""
  createFromTemplate(projectId: ID!, templateId: ID!): InsertResult

  """Clone an event"""
  cloneEvent(projectId: ID!, eventId: ID!): InsertResult!

  """Convert event to a reusable template"""
  convertToTemplate(projectId: ID!, eventId: ID!): UpdateResult

  """Publish event"""
  publishProjectEvent(projectId: ID!, eventId: ID!, announce: Boolean): UpdateResult

  """Get the questboard event for project"""
  createOrReturnDefaultEvent(projectId: ID!): ProjectEvent!

  """Modify task participation status"""
  updateTaskParticipationStatus(projectId: ID!, eventId: ID!, participationId: ID!, status: AccountParticipationStatus!): UpdateResult!

  """Delete a specific task participation entry"""
  deleteTaskParticipation(projectId: ID!, eventId: ID!, participationId: ID!): DeleteResult!

  """Update rejection status for a user referral"""
  updateRejectedStatus(id: ID!, userId: ID!, taskId: ID!, projectId: ID!, eventId: ID!, rejected: Boolean!): TaskParticipation!

  """Create a global referral code"""
  createGlobalReferralCode(code: String!, expiresAt: DateTime!): ReferralCode!

  """Update a global referral code"""
  updateGlobalReferralCode(id: ID!, code: String!, expiresAt: DateTime!): UpdateResult!

  """Delete a global referral code"""
  deleteGlobalReferralCode(id: ID!): DeleteResult!

  """Submit global referral code"""
  submitGlobalReferralCode(code: String!): Boolean!

  """Create integration for the given event"""
  createIntegrationEvent(eventId: ID!, integrationId: ID!, projectId: ID!, data: IntegrationEventInput): IntegrationEvent!

  """Update integration for the given event"""
  updateIntegrationEvent(eventId: ID!, integrationId: ID!, projectId: ID!, data: IntegrationEventUpdateInput!): IntegrationEvent!

  """Remove integration for the given eventId"""
  deleteIntegrationEvent(eventId: ID!, integrationId: ID!, projectId: ID!): DeleteResult!

  """Mark notification as read"""
  markReadNotification(notificationId: ID!): UpdateResult!

  """Clear all notifications of the logged in user"""
  clearAllNotifications: DeleteResult!

  """Update logged-in user details"""
  updateMe(data: UserInput!): UpdateResult!

  """Delete logged-in user account"""
  deleteMe: ID!

  """Update User Onboarding Apps"""
  updateUserOnboardingApp(onboardingApp: Onboarding!): UpdateResult

  """Update User Cookie Consent"""
  updateUserCookieConsent(cookieConsent: CookieConsent!): UpdateResult

  """Login using polkadot wallet signature"""
  polkadotLogin(projectId: ID!, authDto: DotsamaBlockchainAuthDto!): LoginToken!

  """Login using EVM wallet signature"""
  evmLogin(projectId: ID!, authDto: EvmBlockchainAuthDto!): LoginToken!

  """Revoke project access"""
  revokeProjectAccess(projectId: ID!): UpdateResult!
  notifyTelegramConnection(sessionId: ID!, payload: ID!): Boolean

  """Internal Use Only - Generate API key for project"""
  generateApiKey(projectId: ID!, expiresAt: DateTime!): ApiKey!

  """Add or update a project member role"""
  projectMember(projectId: ID!, userId: ID!, role: Role!): Boolean

  """Add or update a project member role"""
  deleteProjectMember(projectId: ID!, userId: ID!): DeleteResult
  createCustomTask(projectId: ID!, eventId: ID!, appKey: ID!, taskKey: ID!, data: CustomTaskInput!): ID!
  updateCustomTask(projectId: ID!, eventId: ID!, taskId: ID!, data: CustomTaskUpdateInput!): Boolean!
  participateFormAnswerTask(eventId: ID!, taskId: ID!, data: FormAnswerTaskParticipationInput!): InsertResult!
  createFormAnswerTask(projectId: ID!, eventId: ID!, data: FormAnswerTaskInput!): ID!
  updateFormAnswerTask(projectId: ID!, eventId: ID!, taskId: ID!, data: FormAnswerTaskUpdateInput!): Boolean!
  participateLinkTask(eventId: ID!, taskId: ID!): InsertResult!
  createLinkTask(projectId: ID!, eventId: ID!, data: LinkTaskInput!): ID!
  updateLinkTask(projectId: ID!, eventId: ID!, taskId: ID!, data: LinkTaskUpdateInput!): Boolean!
  participateDiscordJoinTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createDiscordJoinTask(projectId: ID!, eventId: ID!, data: DiscordJoinTaskInput!): ID!
  updateDiscordJoinTask(projectId: ID!, eventId: ID!, taskId: ID!, data: DiscordJoinTaskUpdateInput!): Boolean!
  createEventConnection(eventId: ID!, provider: AuthProvider!, providerId: ID!): EventConnectionInsertResult!
  participateUploadTask(eventId: ID!, taskId: ID!, data: UploadTaskParticipationInput!): InsertResult!
  createUploadTask(projectId: ID!, eventId: ID!, data: UploadTaskInput!): ID!
  updateUploadTask(projectId: ID!, eventId: ID!, taskId: ID!, data: UploadTaskUpdateInput!): Boolean!
  participateQuizPlayTask(eventId: ID!, taskId: ID!, data: QuizTaskParticipationInput!): QuizTaskParticipationResult!
  createQuizPlayTask(projectId: ID!, eventId: ID!, data: QuizTaskInput!): ID!
  updateQuizPlayTask(projectId: ID!, eventId: ID!, taskId: ID!, data: QuizTaskUpdateInput!): Boolean!
  participateEvmContractInteractTask(eventId: ID!, taskId: ID!, data: EvmContractInteractTaskParticipationInput!, providerId: ID!): InsertResult!
  createEvmContractInteractTask(projectId: ID!, eventId: ID!, data: EvmContractInteractTaskInput!): ID!
  updateEvmContractInteractTask(projectId: ID!, eventId: ID!, taskId: ID!, data: EvmContractInteractTaskUpdateInput!): Boolean!

  """Internal Use Only - Deploy AirLyft EVM contracts to Blockchain"""
  deployContracts(blockchainId: ID!, owner: ID!, ca: ID!, contracts: [ContractType!]!): [EVMContractDeployResult!]!

  """Internal Use Only - Whitelist AirToken"""
  addAirToken(blockchainId: ID!, tokenAddress: String!, contractAddress: String!, owner: String!, creator: String!, isExternal: Boolean!): String!
  participateTelegramJoinTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createTelegramJoinTask(projectId: ID!, eventId: ID!, data: TelegramJoinTaskInput!): ID!
  updateTelegramJoinTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TelegramJoinTaskUpdateInput!): Boolean!

  """Remove integration for the given taskId"""
  deleteIntegrationTask(projectId: ID!, taskId: ID!, integrationId: ID!): DeleteResult!
  participateTwitterFollowTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createTwitterFollowTask(projectId: ID!, eventId: ID!, data: TwitterFollowTaskInput!): ID!
  updateTwitterFollowTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterFollowTaskUpdateInput!): Boolean!
  participateTwitterLikeTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createTwitterLikeTask(projectId: ID!, eventId: ID!, data: TwitterLikeTaskInput!): ID!
  updateTwitterLikeTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterLikeTaskUpdateInput!): Boolean!
  participateTwitterLikeRetweetTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createTwitterLikeRetweetTask(projectId: ID!, eventId: ID!, data: TwitterLikeRetweetTaskInput!): ID!
  updateTwitterLikeRetweetTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterLikeRetweetTaskUpdateInput!): Boolean!
  participateTwitterRetweetTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createTwitterRetweetTask(projectId: ID!, eventId: ID!, data: TwitterRetweetTaskInput!): ID!
  updateTwitterRetweetTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterRetweetTaskUpdateInput!): Boolean!
  participateTwitterPostTask(eventId: ID!, taskId: ID!, data: TwitterPostTaskParticipationInput!, providerId: ID!): InsertResult!
  createTwitterPostTask(projectId: ID!, eventId: ID!, data: TwitterPostTaskInput!): ID!
  updateTwitterPostTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterPostTaskUpdateInput!): Boolean!
  participateTwitterUgcTask(eventId: ID!, taskId: ID!, data: TwitterUgcTaskParticipationInput!, providerId: ID!): InsertResult!
  createTwitterUgcTask(projectId: ID!, eventId: ID!, data: TwitterUgcTaskInput!): ID!
  updateTwitterUgcTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterUgcTaskUpdateInput!): Boolean!
  participateTwitterWhitelistTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createTwitterWhitelistTask(projectId: ID!, eventId: ID!, data: TwitterWhitelistTaskInput!): ID!
  updateTwitterWhitelistTask(projectId: ID!, eventId: ID!, taskId: ID!, data: TwitterWhitelistTaskUpdateInput!): Boolean!
  participateSubsocialCommentTask(eventId: ID!, taskId: ID!, data: SubsocialCommentTaskParticipationInput!, providerId: ID!): InsertResult!
  createSubsocialCommentTask(projectId: ID!, eventId: ID!, data: SubsocialCommentTaskInput!): ID!
  updateSubsocialCommentTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialCommentTaskUpdateInput!): Boolean!
  participateSubsocialFollowTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createSubsocialFollowTask(projectId: ID!, eventId: ID!, data: SubsocialFollowTaskInput!): ID!
  updateSubsocialFollowTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialFollowTaskUpdateInput!): Boolean!
  participateSubsocialPostTask(eventId: ID!, taskId: ID!, data: SubsocialPostTaskParticipationInput!, providerId: ID!): InsertResult!
  createSubsocialPostTask(projectId: ID!, eventId: ID!, data: SubsocialPostTaskInput!): ID!
  updateSubsocialPostTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialPostTaskUpdateInput!): Boolean!
  participateSubsocialProfileTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createSubsocialProfileTask(projectId: ID!, eventId: ID!, data: SubsocialProfileTaskInput!): ID!
  updateSubsocialProfileTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialProfileTaskUpdateInput!): Boolean!
  participateSubsocialShareTask(eventId: ID!, taskId: ID!, data: SubsocialShareTaskParticipationInput!, providerId: ID!): InsertResult!
  createSubsocialShareTask(projectId: ID!, eventId: ID!, data: SubsocialShareTaskInput!): ID!
  updateSubsocialShareTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialShareTaskUpdateInput!): Boolean!
  participateSubsocialSpaceTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createSubsocialSpaceTask(projectId: ID!, eventId: ID!, data: SubsocialSpaceTaskInput!): ID!
  updateSubsocialSpaceTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialSpaceTaskUpdateInput!): Boolean!
  participateSubsocialUpvoteTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createSubsocialUpvoteTask(projectId: ID!, eventId: ID!, data: SubsocialUpvoteTaskInput!): ID!
  updateSubsocialUpvoteTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubsocialUpvoteTaskUpdateInput!): Boolean!
  participateWalletAddressTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  participateDotsamaWalletAddressTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createWalletAddressTask(projectId: ID!, eventId: ID!, data: WalletAddressTaskInput!): ID!
  updateWalletAddressTask(projectId: ID!, eventId: ID!, taskId: ID!, data: WalletAddressTaskUpdateInput!): Boolean!
  participateSubgraphRawTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  participateDotsamaSubgraphRawTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createSubgraphRawTask(projectId: ID!, eventId: ID!, data: SubgraphRawTaskInput!): ID!
  updateSubgraphRawTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubgraphRawTaskUpdateInput!): Boolean!

  """Create or get a referral code linked to a particular task"""
  referralCode(eventId: ID!): String!
  createAirboostReferralTask(projectId: ID!, eventId: ID!, data: AirboostReferralTaskInput!): ID!
  updateAirboostReferralTask(projectId: ID!, eventId: ID!, taskId: ID!, data: AirboostReferralTaskUpdateInput!): Boolean!
  participateEmailWhitelistTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createEmailWhitelistTask(projectId: ID!, eventId: ID!, data: EmailWhitelistTaskInput!): ID!
  updateEmailWhitelistTask(projectId: ID!, eventId: ID!, taskId: ID!, data: EmailWhitelistTaskUpdateInput!): Boolean!
  participateEmailAddressTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createEmailAddressTask(projectId: ID!, eventId: ID!, data: EmailAddressTaskInput!): ID!
  updateEmailAddressTask(projectId: ID!, eventId: ID!, taskId: ID!, data: EmailAddressTaskUpdateInput!): Boolean!
  participateEmailSubscribeTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createEmailSubscribeTask(projectId: ID!, eventId: ID!, data: EmailSubscribeTaskInput!): ID!
  updateEmailSubscribeTask(projectId: ID!, eventId: ID!, taskId: ID!, data: EmailSubscribeTaskUpdateInput!): Boolean!
  participateTextSignTermsTask(eventId: ID!, taskId: ID!): InsertResult!
  participateEVMSignTermsTask(eventId: ID!, taskId: ID!): InsertResult!
  participateDotsamaSignTermsTask(eventId: ID!, taskId: ID!, data: SignTermsTaskParticipationInput!): InsertResult!
  createSignTermsTask(projectId: ID!, eventId: ID!, data: SignTermsTaskInput!): ID!
  updateSignTermsTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SignTermsTaskUpdateInput!): Boolean!
  participateCheckinTask(eventId: ID!, taskId: ID!): InsertResult!
  createCheckinTask(projectId: ID!, eventId: ID!, data: CheckinDailyTaskInput!): ID!
  updateCheckinTask(projectId: ID!, eventId: ID!, taskId: ID!, data: CheckinDailyTaskUpdateInput!): Boolean!
  participateEvmFaucetRawTask(eventId: ID!, taskId: ID!, providerId: ID!, captcha: String): InsertResult!
  participateDotsamaFaucetRawTask(eventId: ID!, taskId: ID!, providerId: ID!, captcha: String): InsertResult!
  createFaucetRawTask(projectId: ID!, eventId: ID!, data: FaucetRawTaskInput!): ID!
  updateFaucetRawTask(projectId: ID!, eventId: ID!, taskId: ID!, data: FaucetRawTaskUpdateInput!): Boolean!
  participateAirquestFollowTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createAirquestFollowTask(projectId: ID!, eventId: ID!, data: AirquestFollowTaskInput!): ID!
  updateAirquestFollowTask(projectId: ID!, eventId: ID!, taskId: ID!, data: AirquestFollowTaskUpdateInput!): Boolean!
  participateSubstrateQueryTask(eventId: ID!, taskId: ID!, data: SubstrateQueryTaskParticipationInput!, providerId: ID!): InsertResult!
  createSubstrateQueryTask(projectId: ID!, eventId: ID!, data: SubstrateQueryTaskInput!): ID!
  updateSubstrateQueryTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SubstrateQueryTaskUpdateInput!): Boolean!
  participateLuckydrawTask(eventId: ID!, taskId: ID!): LuckydrawTaskParticipationResult!
  createLuckydrawTask(projectId: ID!, eventId: ID!, data: LuckydrawTaskInput!): ID!
  updateLuckydrawTask(projectId: ID!, eventId: ID!, taskId: ID!, data: LuckydrawTaskUpdateInput!): Boolean!
  participateMobileAppTask(eventId: ID!, taskId: ID!, data: MobileAppParticipationInput!): InsertResult!
  createMobileAppTask(projectId: ID!, eventId: ID!, data: MobileAppTaskInput!): ID!
  updateMobileAppTask(projectId: ID!, eventId: ID!, taskId: ID!, data: MobileAppTaskUpdateInput!): Boolean!
  participateSecretCodeTask(eventId: ID!, taskId: ID!, data: SecretCodeTaskParticipationInput!): InsertResult!
  createSecretCodeTask(projectId: ID!, eventId: ID!, data: SecretCodeTaskInput!): ID!
  updateSecretCodeTask(projectId: ID!, eventId: ID!, taskId: ID!, data: SecretCodeTaskUpdateInput!): Boolean!
  participateProducthuntUpvoteTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createProducthuntUpvoteTask(projectId: ID!, eventId: ID!, data: ProducthuntUpvoteTaskInput!): ID!
  updateProducthuntUpvoteTask(projectId: ID!, eventId: ID!, taskId: ID!, data: ProducthuntUpvoteTaskUpdateInput!): Boolean!
  participateProducthuntFollowTask(eventId: ID!, taskId: ID!, providerId: ID!): InsertResult!
  createProducthuntFollowTask(projectId: ID!, eventId: ID!, data: ProducthuntFollowTaskInput!): ID!
  updateProducthuntFollowTask(projectId: ID!, eventId: ID!, taskId: ID!, data: ProducthuntFollowTaskUpdateInput!): Boolean!
  participateBlogCommentTask(eventId: ID!, taskId: ID!, data: BlogCommentParticipationInput!): InsertResult!
  createBlogCommentTask(projectId: ID!, eventId: ID!, data: BlogCommentTaskInput!): ID!
  updateBlogCommentTask(projectId: ID!, eventId: ID!, taskId: ID!, data: BlogCommentTaskUpdateInput!): Boolean!
  participateBlogWriteTask(eventId: ID!, taskId: ID!, data: BlogWriteParticipationInput!): InsertResult!
  createBlogWriteTask(projectId: ID!, eventId: ID!, data: BlogWriteTaskInput!): ID!
  updateBlogWriteTask(projectId: ID!, eventId: ID!, taskId: ID!, data: BlogWriteTaskUpdateInput!): Boolean!
  participateKickstarterTask(eventId: ID!, taskId: ID!): InsertResult!
  createKickstarterTask(projectId: ID!, eventId: ID!, data: KickstarterTaskInput!): ID!
  updateKickstarterTask(projectId: ID!, eventId: ID!, taskId: ID!, data: KickstarterTaskUpdateInput!): Boolean!
  completeFlashcardViewTask(eventId: ID!, taskId: ID!): Boolean!
  createFlashcardTask(projectId: ID!, eventId: ID!, data: FlashcardTaskInput!): ID!
  updateFlashcardTask(projectId: ID!, eventId: ID!, taskId: ID!, data: FlashcardTaskUpdateInput!): Boolean!
  createAirTokenGiveaway(projectId: ID!, eventId: ID!, data: AirTokenGiveawayInput!): InsertResult!
  updateAirTokenGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, data: AirTokenGiveawayUpdateInput!): UpdateResult!

  """Sync claim"""
  syncClaimAirTokenGiveaway(giveawayId: ID!, ids: [ID!]!): TransactionStatus!
  claimAirTokenGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, userAddress: String!, captcha: String): RewardCertificate!
  claimDotsamaAirTokenGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, userAddress: String!, captcha: String): RewardCertificate!
  createWhitelistGiveaway(projectId: ID!, eventId: ID!, data: WhitelistGiveawayInput!): InsertResult!
  updateWhitelistGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, data: WhitelistGiveawayUpdateInput!): UpdateResult!
  createAirPoolGiveaway(projectId: ID!, eventId: ID!, data: AirPoolGiveawayInput!): InsertResult!
  updateAirPoolGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, data: AirPoolGiveawayUpdateInput!): UpdateResult!

  """Sync claim"""
  syncClaimAirPoolGiveaway(giveawayId: ID!, ids: [ID!]!): TransactionStatus!
  claimAirPoolGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, userAddress: String!, captcha: String): RewardCertificate!
  claimDotsamaAirPoolGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, userAddress: String!, captcha: String): RewardClaimResult!

  """Create a blockchain pool for the user"""
  createBlockchainPool(projectId: ID!, data: BlockchainPoolInput!): CreateBlockchainPoolResult!

  """Create a dotsama blockchain pool for the user"""
  createDotsamaBlockchainPool(projectId: ID!, data: BlockchainPoolInput!): CreateDotsamaBlockchainPoolResult!

  """Deposit fund"""
  depositBlockchainPool(projectId: ID!, data: DepositBlockchainPoolInput!): CertificateResult!

  """Withdraw fund"""
  withdrawBlockchainPool(projectId: ID!, data: WithdrawBlockchainPoolInput!): CertificateResult!

  """Withdraw fund"""
  withdrawDotsamaBlockchainPool(projectId: ID!, data: WithdrawBlockchainPoolInput!): WithdrawBlockchainPoolOutput!

  """Sync state"""
  syncBlockchainPool(projectId: ID!, id: ID!): SyncBlockchainPoolResult!

  """Delete a failed Blockchain Pool"""
  deleteBlockchainPool(projectId: ID!, id: ID!): DeleteResult!
  createSecretGiveaway(projectId: ID!, eventId: ID!, data: SecretGiveawayInput!): InsertResult!
  updateSecretGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, data: SecretGiveawayUpdateInput!): UpdateResult!
  createMerchandiseGiveaway(projectId: ID!, eventId: ID!, data: MerchandiseGiveawayInput!): InsertResult!
  updateMerchandiseGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, data: MerchandiseGiveawayUpdateInput!): UpdateResult!
  claimMerchandiseGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!, data: FormAnswerTaskParticipationInput!): [EventReward!]!

  """Delete an integration"""
  deleteIntegration(projectId: ID!, integrationId: ID!): DeleteResult!

  """Create an API Key integration"""
  createApiKeyIntegration(projectId: ID!, integrationName: String!, displayName: String, data: ApiKeyIntegrationInput!): InsertResult!

  """Update API key integration"""
  updateApiKeyIntegration(projectId: ID!, integrationId: ID!, integrationName: String!, displayName: String, data: ApiKeyIntegrationUpdateInput!): Boolean!

  """Update webhook"""
  updateZapierWebhook(projectId: ID!, integrationId: ID!, webhookId: ID!, name: String!, webhookUrl: String!): Boolean!

  """Create webhook"""
  createZapierWebhook(projectId: ID!, integrationId: ID!, name: String!, webhookUrl: String!): ZapierWebhookData!

  """Delete webhook"""
  deleteZapierWebhook(projectId: ID!, integrationId: ID!, webhookId: ID!): Boolean!

  """Generate Zapier API key"""
  generateZapierApiKey(projectId: ID!): InsertResult!

  """Create AirToken"""
  createAirToken(projectId: ID!, data: CreateAirTokenInput!): CreateAirTokenResult!

  """Create AssetHub AirToken"""
  createAssetHubAirToken(projectId: ID!, data: CreateAssetHubAirToken!): CreateAssetHubAirTokenResult!

  """Sync state"""
  syncAirToken(projectId: ID!, id: ID!): TransactionStatus!

  """Mark AirToken creation failure"""
  createAirTokenFailed(projectId: ID!, id: ID!): TransactionStatus!

  """Delete AirToken"""
  deleteAirToken(projectId: ID!, id: ID!): DeleteResult!
  participateRestRawTask(eventId: ID!, taskId: ID!, data: RestTaskParticipationInput!): InsertResult!
  participateEvmRestRawTask(eventId: ID!, taskId: ID!, data: RestTaskParticipationInput!, providerId: ID!): InsertResult!
  participateDotsamaRestRawTask(eventId: ID!, taskId: ID!, data: RestTaskParticipationInput!, providerId: ID!): InsertResult!
  createRestRawTask(projectId: ID!, eventId: ID!, data: RestRawTaskInput!): ID!
  updateRestRawTask(projectId: ID!, eventId: ID!, taskId: ID!, data: RestRawTaskUpdateInput!): Boolean!

  """Mark completion status of an onboarding template by the project"""
  updateProjectTemplateOnboardingStatus(projectId: ID!, templateId: ID!): Boolean!
  addEventPromotion(url: String!, promotionType: String!): PromotedEvent!
  updateEventPromotion(eventId: ID!, url: String, updatedAt: DateTime): UpdateResult!
  deleteEventPromotion(id: ID!): DeleteResult!
  addProjectPromotion(url: String!, promotionType: String!): PromotedProject!
  updateProjectPromotion(projectId: ID!, url: String, updatedAt: DateTime): UpdateResult!
  deleteProjectPromotion(id: ID!): DeleteResult!

  """Create an invitations for your team members to join your project"""
  createInvitation(projectId: ID!): Invitation!

  """Verify invite code"""
  verifyInvitation(invitationCode: String!, id: String!): Invitation
  createCheckoutSession(projectId: ID!, planId: ID!, redirectTo: ID!): String!
  cancelBilling(projectId: ID!, billingId: ID!): UpdateResult
  createBilling(data: BillingInput!): Billing!
  updateBilling(billingId: ID!, data: BillingUpdateInput!): UpdateResult!
  billingUsingCrypto(projectId: ID!, planId: ID!, data: BillingUsingCryptoInput!): UpdateResult!

  """Replace all winners by input list"""
  addWinners(projectId: ID!, eventId: ID!, giveawayId: ID!, data: [WinnerInput!]!): InsertResult!

  """Remove single winner item"""
  deleteWinner(projectId: ID!, eventId: ID!, giveawayId: ID!, rewardId: ID!): DeleteResult!

  """Remove all winner items"""
  clearWinners(projectId: ID!, eventId: ID!, giveawayId: ID!): DeleteResult!

  """Submit final winners after review"""
  settleGiveaway(projectId: ID!, eventId: ID!, giveawayId: ID!): Boolean!

  """Create Season"""
  createSeason(projectId: ID!, name: String!): Season!

  """Create Widget"""
  createWidget(projectId: ID!, data: WidgetDataInput!): InsertResult!

  """Update Widget"""
  updateWidget(projectId: ID!, widgetId: ID!, data: WidgetDataUpdateInput!): UpdateResult!

  """Delete Widget"""
  deleteWidget(projectId: ID!, widgetId: ID!): DeleteResult!
  refreshWalletDotsamaNftItems(blockchainId: ID!, assetId: ID!, walletAddress: String!, poolId: ID!, projectId: ID!): [DotsamaNft!]!
}

input BlockchainInput {
  chainId: Int!
  name: String!
  type: BlockchainType!
  blockExplorerUrls: [String!]!
  nativeCurrency: String!
  decimals: Int!
  rpcUrls: [String!]!
  icon: String!
  status: BlockchainIntegrationStatus!
  keyPairType: KeyPairType
}

enum KeyPairType {
  ED25519
  SR25519
  ECDSA
  ETHEREUM
}

input BlockchainUpdateInput {
  chainId: Int
  name: String
  type: BlockchainType
  blockExplorerUrls: [String!]
  nativeCurrency: String
  decimals: Int
  rpcUrls: [String!]
  icon: String
  status: BlockchainIntegrationStatus
  keyPairType: KeyPairType
}

input BlockchainAssetInput {
  name: String!
  ticker: String!
  assetType: AssetType!
  address: String!
  status: BlockchainIntegrationStatus!
  txStatus: TransactionStatus!
  decimals: Int!
  blockchainId: ID!
  tokenId: String!
  icon: String!
}

input BlockchainAssetUpdateInput {
  name: String
  ticker: String
  assetType: AssetType
  address: String
  status: BlockchainIntegrationStatus
  txStatus: TransactionStatus
  decimals: Int
  blockchainId: ID
  tokenId: String
  icon: String
}

input ProjectInput {
  name: String!
  publicLink: String!
  bio: String
  logo: String
  bannerUrl: String
  contactEmail: String
  urls: [ProjectUrlInput]
  ecosystems: [Ecosystem!]
  referredEcosystem: Ecosystem
  referralId: String
  sectors: [Sector!]
  isHideQbRecentEvents: Boolean
}

input ProjectUrlInput {
  urlType: ProjectUrlType!
  url: String!
  data: JSONObject
}

input ProjectUpdateInput {
  name: String
  publicLink: String
  bio: String
  logo: String
  bannerUrl: String
  contactEmail: String
  urls: [ProjectUrlInput!]
  ecosystems: [Ecosystem!]
  referredEcosystem: Ecosystem
  referralId: String
  sectors: [Sector!]
  isHideQbRecentEvents: Boolean
}

input ProjectUrlUpdateInput {
  urlType: ProjectUrlType
  url: String
  data: JSONObject
}

input ProjectVisibilityUpdateInput {
  visibility: ProjectVisibility
  verified: Boolean
}

input ProjectEventInput {
  title: String!
  startTime: DateTime
  publicLink: String!
  endTime: DateTime
  description: String
  bannerUrl: String
  visibility: AccountEventVisibility
  leaderboard: LeaderboardType
  seasonId: ID
  ipProtect: Boolean
  webhook: String
  zapierWebhook: String
}

input ProjectEventUpdateInput {
  title: String
  startTime: DateTime
  publicLink: String
  endTime: DateTime
  description: String
  bannerUrl: String
  visibility: AccountEventVisibility
  leaderboard: LeaderboardType
  seasonId: ID
  ipProtect: Boolean
  webhook: String
  zapierWebhook: String
}

enum AccountParticipationStatus {
  VALID
  INVALID
}

input IntegrationEventInput {
  """Route ID required for zapier integration"""
  routeId: ID
}

input IntegrationEventUpdateInput {
  """Route ID required for zapier integration"""
  routeId: ID

  """New integration ID required for webhook integration"""
  newIntegrationId: ID
}

input UserInput {
  username: String
  firstName: String
  lastName: String
  email: String
  avatar: String
}

input DotsamaBlockchainAuthDto {
  address: String!
  message: String!
  signature: String!
  source: String!
  name: String!
}

input EvmBlockchainAuthDto {
  address: String!
  message: String!
  signature: String!
}

input CustomTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: [FormAnswerInput!]!
}

input IntegrationTaskInput {
  integrationId: String!
  routeId: String
}

input CustomTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: [FormAnswerInput!]!
}

input FormAnswerTaskParticipationInput {
  answers: [FormAnswerInput!]!
}

input FormAnswerTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: [FormAnswerTaskDataInput!]!
}

input FormAnswerTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: [FormAnswerTaskDataUpdateInput!]!
}

input FormAnswerTaskDataUpdateInput {
  id: String
  order: Int
  title: String
  widget: FormWidgetType
  required: Boolean
  hidden: Boolean
  values: [FormDataValueInput!]
}

input LinkTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: LinkTaskDataInput!
  appType: AppType!
  taskType: TaskType!
}

input LinkTaskDataInput {
  url: String!
}

input LinkTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: LinkTaskDataUpdateInput!
}

input LinkTaskDataUpdateInput {
  url: String
}

input DiscordJoinTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: DiscordJoinTaskDataInput!
}

input DiscordJoinTaskDataInput {
  url: String!
  guildId: String!
  guildName: String!
}

input DiscordJoinTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: DiscordJoinTaskDataUpdateInput!
}

input DiscordJoinTaskDataUpdateInput {
  url: String
  guildId: String
  guildName: String
}

input UploadTaskParticipationInput {
  urls: [String!]!
}

input UploadTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: UploadTaskDataInput!
}

input UploadTaskDataInput {
  """Example files"""
  urls: [String!]
  maxCount: Int!
  fileTypes: [String!]
  maxSize: Int
  prompt: String
}

input UploadTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: UploadTaskDataUpdateInput!
}

input UploadTaskDataUpdateInput {
  """Example files"""
  urls: [String!]
  maxCount: Int
  fileTypes: [String!]
  maxSize: Int
  prompt: String
}

input QuizTaskParticipationInput {
  answers: [String!]!
}

input QuizTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: QuizTaskDataInput
}

input QuizTaskDataInput {
  questionType: QuizQuestionType
  options: [QuizTaskDataOptionInput!]
}

input QuizTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: QuizTaskDataUpdateInput
}

input QuizTaskDataUpdateInput {
  questionType: QuizQuestionType
  options: [QuizTaskDataOptionInput!]
}

input EvmContractInteractTaskParticipationInput {
  formResponses: [FormResponseInput!]
}

input EvmContractInteractTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: EvmContractInteractTaskDataInput!
}

input EvmContractInteractTaskDataInput {
  blockchainId: String!
  contractAddress: String!
  function: SmartContractFunctionInput!
  inputParams: [FunctionInputParamsInput]!
  condition: [FunctionConditionInput!]!
  verifiedWallet: Boolean
}

input EvmContractInteractTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: EvmContractInteractTaskDataUpdateInput!
}

input EvmContractInteractTaskDataUpdateInput {
  blockchainId: String
  contractAddress: String
  function: SmartContractFunctionInput
  inputParams: [FunctionInputParamsInput!]
  condition: [FunctionConditionInput!]
  verifiedWallet: Boolean
}

input TelegramJoinTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TelegramJoinTaskDataInput!
}

input TelegramJoinTaskDataInput {
  chatId: String!
  username: String!
  type: TelegramChatType!
  title: String!
}

input TelegramJoinTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TelegramJoinTaskDataUpdateInput!
}

input TelegramJoinTaskDataUpdateInput {
  chatId: String
  username: String
  type: TelegramChatType
  title: String
}

input TwitterFollowTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterFollowTaskDataInput!
}

input TwitterFollowTaskDataInput {
  url: String!
}

input TwitterFollowTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterFollowTaskDataUpdateInput!
}

input TwitterFollowTaskDataUpdateInput {
  url: String
}

input TwitterLikeTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterLikeTaskDataInput!
}

input TwitterLikeTaskDataInput {
  url: String!
}

input TwitterLikeTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterLikeTaskDataUpdateInput!
}

input TwitterLikeTaskDataUpdateInput {
  url: String
}

input TwitterLikeRetweetTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterLikeRetweetTaskDataInput!
}

input TwitterLikeRetweetTaskDataInput {
  url: String!
  actions: [TwitterAction!]!
}

input TwitterLikeRetweetTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterLikeRetweetTaskDataUpdateInput!
}

input TwitterLikeRetweetTaskDataUpdateInput {
  url: String
  actions: [TwitterAction!]
}

input TwitterRetweetTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterRetweetTaskDataInput!
}

input TwitterRetweetTaskDataInput {
  url: String!
}

input TwitterRetweetTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterRetweetTaskDataUpdateInput!
}

input TwitterRetweetTaskDataUpdateInput {
  url: String
}

input TwitterPostTaskParticipationInput {
  url: String!
}

input TwitterPostTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterPostTaskDataInput!
}

input TwitterPostTaskDataInput {
  content: String
  type: TweetSubmitType
  url: String
  hashtags: [String!]
  userMentions: [String!]
  mentionsCount: Int
  minimumMediaEntries: Int
}

input TwitterPostTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterPostTaskDataUpdateInput!
}

input TwitterPostTaskDataUpdateInput {
  content: String
  type: TweetSubmitType
  url: String
  hashtags: [String!]
  userMentions: [String!]
  mentionsCount: Int
  minimumMediaEntries: Int
}

input TwitterUgcTaskParticipationInput {
  url: String!
  tweet: String
  media: [String!]
}

input TwitterUgcTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterUgcTaskDataInput!
}

input TwitterUgcTaskDataInput {
  includeText: Boolean
  type: TweetSubmitType
  url: String
  hashtags: [String!]
  userMentions: [String!]
  mentionsCount: Int
  minimumMediaEntries: Int
}

input TwitterUgcTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterUgcTaskDataUpdateInput!
}

input TwitterUgcTaskDataUpdateInput {
  includeText: Boolean
  type: TweetSubmitType
  url: String
  hashtags: [String!]
  userMentions: [String!]
  mentionsCount: Int
  minimumMediaEntries: Int
}

input TwitterWhitelistTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterWhitelistTaskDataInput!
}

input TwitterWhitelistTaskDataInput {
  """List of twitter handles to whitelist"""
  whitelist: [String!]
}

input TwitterWhitelistTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: TwitterWhitelistTaskDataUpdateInput!
}

input TwitterWhitelistTaskDataUpdateInput {
  """List of twitter handles to whitelist"""
  whitelist: [String!]
}

input SubsocialCommentTaskParticipationInput {
  postUrl: String!
}

input SubsocialCommentTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialCommentTaskDataInput!
}

input SubsocialCommentTaskDataInput {
  postId: String!
  postUrl: String!
}

input SubsocialCommentTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialCommentTaskDataUpdateInput!
}

input SubsocialCommentTaskDataUpdateInput {
  postId: String
  postUrl: String
}

input SubsocialFollowTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialFollowTaskDataInput!
}

input SubsocialFollowTaskDataInput {
  spaceId: String!
  handle: String!
}

input SubsocialFollowTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialFollowTaskDataUpdateInput!
}

input SubsocialFollowTaskDataUpdateInput {
  spaceId: String
  handle: String
}

input SubsocialPostTaskParticipationInput {
  postUrl: String!
}

input SubsocialPostTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialPostTaskDataInput!
}

input SubsocialPostTaskDataInput {
  spaceId: String!
  handle: String!
}

input SubsocialPostTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialPostTaskDataUpdateInput!
}

input SubsocialPostTaskDataUpdateInput {
  spaceId: String
  handle: String
}

input SubsocialProfileTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input SubsocialProfileTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input SubsocialShareTaskParticipationInput {
  postUrl: String!
}

input SubsocialShareTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialShareTaskDataInput!
}

input SubsocialShareTaskDataInput {
  postId: String!
  postUrl: String!
}

input SubsocialShareTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialShareTaskDataUpdateInput!
}

input SubsocialShareTaskDataUpdateInput {
  postId: String
  postUrl: String
}

input SubsocialSpaceTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input SubsocialSpaceTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input SubsocialUpvoteTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialUpvoteTaskDataInput!
}

input SubsocialUpvoteTaskDataInput {
  postId: String!
  postUrl: String!
}

input SubsocialUpvoteTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubsocialUpvoteTaskDataUpdateInput!
}

input SubsocialUpvoteTaskDataUpdateInput {
  postId: String
  postUrl: String
}

input WalletAddressTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: WalletAddressTaskDataInput!
  taskType: TaskType!
}

input WalletAddressTaskDataInput {
  blockchainId: String!
  verify: Boolean
  verification: WalletVerificationType! = ANY

  """This is a list of whitelisted when verification is list address"""
  walletList: [String!]
  excludedWallets: [Web3WalletType!]
  verifiedWallet: Boolean
}

input WalletAddressTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: WalletAddressTaskDataUpdateInput!
}

input WalletAddressTaskDataUpdateInput {
  blockchainId: String
  verify: Boolean
  verification: WalletVerificationType = ANY

  """This is a list of whitelisted when verification is list address"""
  walletList: [String!]
  excludedWallets: [Web3WalletType!]
  verifiedWallet: Boolean
}

input SubgraphRawTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubgraphRawTaskDataInput!
}

input SubgraphRawTaskDataInput {
  url: String!
  query: String!
  validation: String!
  blockchainId: String!
  blockchainType: BlockchainType!
  verifiedWallet: Boolean
  ss58Prefix: Int
}

input SubgraphRawTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubgraphRawTaskDataUpdateInput!
}

input SubgraphRawTaskDataUpdateInput {
  url: String
  query: String
  validation: String
  blockchainId: String
  blockchainType: BlockchainType
  verifiedWallet: Boolean
  ss58Prefix: Int
}

input AirboostReferralTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: AirboostReferralTaskDataInput!
}

input AirboostReferralTaskDataInput {
  shareTitle: String!
  shareBody: String
  max: Float!
}

input AirboostReferralTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: AirboostReferralTaskDataUpdateInput!
}

input AirboostReferralTaskDataUpdateInput {
  shareTitle: String
  shareBody: String
  max: Float
}

input EmailWhitelistTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: EmailWhitelistTaskDataInput!
}

input EmailWhitelistTaskDataInput {
  emailList: [String!]
  verification: EmailVerificationType
}

input EmailWhitelistTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: EmailWhitelistTaskDataUpdateInput!
}

input EmailWhitelistTaskDataUpdateInput {
  emailList: [String!]
  verification: EmailVerificationType
}

input EmailAddressTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input EmailAddressTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input EmailSubscribeTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input EmailSubscribeTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input SignTermsTaskParticipationInput {
  address: String!
  signature: String!
}

input SignTermsTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SignTermsTaskDataInput!
  taskType: TaskType!
}

input SignTermsTaskDataInput {
  terms: String!
  blockchainId: String
}

input SignTermsTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SignTermsTaskDataUpdateInput!
}

input SignTermsTaskDataUpdateInput {
  terms: String
  blockchainId: String
}

input CheckinDailyTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input CheckinDailyTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input FaucetRawTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: FaucetRawTaskDataInput!
  allocationCSV: [KeyValueGroupInput!]
  taskType: TaskType!
}

input FaucetRawTaskDataInput {
  blockchainId: String!
  amountPerUser: String
  maxUser: Int
  allocationCSVExists: Boolean
  tokenAddress: String
}

input KeyValueGroupInput {
  groupId: String
  key: String!
  value: String
}

input FaucetRawTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: FaucetRawTaskDataUpdateInput!
  allocationCSV: [KeyValueGroupInput!]
}

input FaucetRawTaskDataUpdateInput {
  blockchainId: String
  amountPerUser: String
  maxUser: Int
  allocationCSVExists: Boolean
  tokenAddress: String
}

input AirquestFollowTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: AirquestFollowTaskDataInput!
}

input AirquestFollowTaskDataInput {
  url: String!
  projectId: String!
  projectName: String!
}

input AirquestFollowTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: AirquestFollowTaskDataUpdateInput!
}

input AirquestFollowTaskDataUpdateInput {
  url: String
  projectId: String
  projectName: String
}

input SubstrateQueryTaskParticipationInput {
  answers: [FormAnswerInput!]
}

input SubstrateQueryTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubstrateQueryTaskDataInput!
}

input SubstrateQueryTaskDataInput {
  blockchainId: String!
  section: String!
  method: String!
  inputParams: [FormAnswerTaskDataInput]
  validation: String!
  verifiedWallet: Boolean
  extra: [FormAnswerInput]
  taskType: SubstrateTaskType
}

input SubstrateQueryTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SubstrateQueryTaskDataUpdateInput!
}

input SubstrateQueryTaskDataUpdateInput {
  blockchainId: String
  section: String
  method: String
  inputParams: [FormAnswerTaskDataInput!]
  validation: String
  verifiedWallet: Boolean
  extra: [FormAnswerInput!]
  taskType: SubstrateTaskType
}

input LuckydrawTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: LuckydrawTaskDataInput!
  taskType: TaskType!
}

input LuckydrawTaskDataInput {
  luckydrawType: LuckydrawType!
  rewardType: RewardType!
  rewards: [LuckydrawRewardInput!]!
  slotMachineIcons: [String!]
}

input LuckydrawTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: LuckydrawTaskDataUpdateInput!
}

input LuckydrawTaskDataUpdateInput {
  luckydrawType: LuckydrawType
  rewardType: RewardType
  rewards: [LuckydrawRewardInput!]
  slotMachineIcons: [String!]
}

input MobileAppParticipationInput {
  url: String!
}

input MobileAppTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: MobileAppTaskDataInput!
}

input MobileAppTaskDataInput {
  appName: String
  playStoreUrl: String
  appStoreUrl: String
  prompt: String
}

input MobileAppTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: MobileAppTaskDataUpdateInput!
}

input MobileAppTaskDataUpdateInput {
  appName: String
  playStoreUrl: String
  appStoreUrl: String
  prompt: String
}

input SecretCodeTaskParticipationInput {
  code: String!
}

input SecretCodeTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SecretCodeTaskDataInput!
}

input SecretCodeTaskDataInput {
  secretCodes: [String!]!
}

input SecretCodeTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: SecretCodeTaskDataUpdateInput!
}

input SecretCodeTaskDataUpdateInput {
  secretCodes: [String!]
}

input ProducthuntUpvoteTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: ProducthuntUpvoteTaskDataInput!
}

input ProducthuntUpvoteTaskDataInput {
  postTitle: String!
  postUrl: String!
}

input ProducthuntUpvoteTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: ProducthuntUpvoteTaskDataUpdateInput!
}

input ProducthuntUpvoteTaskDataUpdateInput {
  postTitle: String
  postUrl: String!
}

input ProducthuntFollowTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: ProducthuntFollowTaskDataInput!
}

input ProducthuntFollowTaskDataInput {
  userUrl: String!
}

input ProducthuntFollowTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: ProducthuntFollowTaskDataUpdateInput!
}

input ProducthuntFollowTaskDataUpdateInput {
  userUrl: String
}

input BlogCommentParticipationInput {
  username: String!
}

input BlogCommentTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: BlogCommentTaskDataInput!
}

input BlogCommentTaskDataInput {
  blogUrl: String
}

input BlogCommentTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: BlogCommentTaskDataUpdateInput!
}

input BlogCommentTaskDataUpdateInput {
  blogUrl: String
}

input BlogWriteParticipationInput {
  blogUrl: String!
}

input BlogWriteTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input BlogWriteTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
}

input KickstarterTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: KickstarterTaskDataInput!
}

input KickstarterTaskDataInput {
  projectUrl: String!
}

input KickstarterTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: KickstarterTaskDataUpdateInput!
}

input KickstarterTaskDataUpdateInput {
  projectUrl: String
}

input FlashcardTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: FlashcardTaskDataInput
  projectId: String!
  eventId: String!
}

input FlashcardTaskDataInput {
  sections: [FlashcardSectionInput!]
}

input FlashcardTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: FlashcardTaskDataUpdateInput
  projectId: String!
  eventId: String!
  id: String!
}

input FlashcardTaskDataUpdateInput {
  sections: [FlashcardSectionInput!]
}

input AirTokenGiveawayInput {
  giveawayType: GiveawayType!
  distributionType: DistributionType!
  parentId: ID
  condition: GiveawayEventCondition! = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean! = true
  hidden: Boolean = false
  whitelistWallets: [String]
  airTokenId: ID!
  data: AirTokenGiveawayDataInput!
}

enum GiveawayEventCondition {
  AND
  OR
  NA
}

input AirTokenGiveawayDataInput {
  rules: [GiveawayRuleInput]

  """Cap"""
  amount: String
  capped: Boolean!

  """amount per winner"""
  winnerAmount: String
  shopConfig: ShopConfigInput
  guardConfig: TaskGuardInput
}

input GiveawayRuleInput {
  min: Int
  max: Int
  amount: String!
  ruleType: GiveawayRuleType
  condition: GiveawayRuleCondition
  ids: [String]
}

input AirTokenGiveawayUpdateInput {
  parentId: ID
  condition: GiveawayEventCondition = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean = true
  hidden: Boolean = false
  whitelistWallets: [String!]
  data: AirTokenGiveawayDataUpdateInput!
}

input AirTokenGiveawayDataUpdateInput {
  rules: [GiveawayRuleInput!]

  """Cap"""
  amount: String
  capped: Boolean

  """amount per winner"""
  winnerAmount: String
  shopConfig: ShopConfigInput
  guardConfig: TaskGuardInput
}

input WhitelistGiveawayInput {
  giveawayType: GiveawayType!
  distributionType: DistributionType!
  parentId: ID
  condition: GiveawayEventCondition! = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean! = true
  hidden: Boolean = false
  whitelistWallets: [String]
  data: WhitelistGiveawayDataInput!
}

input WhitelistGiveawayDataInput {
  reward: String!
  rewardAmount: String
  distributionMsg: String
}

input WhitelistGiveawayUpdateInput {
  parentId: ID
  condition: GiveawayEventCondition = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean = true
  hidden: Boolean = false
  whitelistWallets: [String!]
  data: WhitelistGiveawayDataUpdateInput!
}

input WhitelistGiveawayDataUpdateInput {
  reward: String
  rewardAmount: String
  distributionMsg: String
}

input AirPoolGiveawayInput {
  giveawayType: GiveawayType!
  distributionType: DistributionType!
  parentId: ID
  condition: GiveawayEventCondition! = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean! = true
  hidden: Boolean = false
  whitelistWallets: [String]
  airPoolId: ID!
  data: AirPoolGiveawayDataInput!
}

input AirPoolGiveawayDataInput {
  rules: [GiveawayRuleInput]

  """Cap"""
  amount: String
  capped: Boolean!

  """amount per winner"""
  winnerAmount: String
  shopConfig: ShopConfigInput
  guardConfig: TaskGuardInput
}

input AirPoolGiveawayUpdateInput {
  parentId: ID
  condition: GiveawayEventCondition = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean = true
  hidden: Boolean = false
  whitelistWallets: [String!]
  data: AirPoolGiveawayDataUpdateInput!
}

input AirPoolGiveawayDataUpdateInput {
  rules: [GiveawayRuleInput!]

  """Cap"""
  amount: String
  capped: Boolean

  """amount per winner"""
  winnerAmount: String
  shopConfig: ShopConfigInput
  guardConfig: TaskGuardInput
}

input BlockchainPoolInput {
  name: String!
  amount: String!
  assetId: String!
  blockchainId: String!
  contractAddress: String!
  tokenIds: [String]
  assetType: AssetType!
  creatorBlockchainAddress: String!
}

input DepositBlockchainPoolInput {
  id: String!
  amount: String!
  tokenIds: [String]
  depositorBlockchainAddress: String!
  assetType: AssetType!
}

input WithdrawBlockchainPoolInput {
  id: String!
  amount: String!
  withdrawRemaining: Boolean
  userBlockchainAddress: String!
  assetType: AssetType!
}

input SecretGiveawayInput {
  giveawayType: GiveawayType!
  distributionType: DistributionType!
  parentId: ID
  condition: GiveawayEventCondition! = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean! = true
  hidden: Boolean = false
  whitelistWallets: [String]
  data: SecretGiveawayDataInput!
}

input SecretGiveawayDataInput {
  title: String!
  winnerCount: Float
  displayType: DisplayType!
  deliveryType: DeliveryType!
  distributionMsg: String
}

input SecretGiveawayUpdateInput {
  parentId: ID
  condition: GiveawayEventCondition = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean = true
  hidden: Boolean = false
  whitelistWallets: [String!]
  data: SecretGiveawayDataUpdateInput!
}

input SecretGiveawayDataUpdateInput {
  title: String
  winnerCount: Float
  displayType: DisplayType
  deliveryType: DeliveryType
  distributionMsg: String
}

input MerchandiseGiveawayInput {
  giveawayType: GiveawayType!
  distributionType: DistributionType!
  parentId: ID
  condition: GiveawayEventCondition! = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean! = true
  hidden: Boolean = false
  whitelistWallets: [String]
  data: MerchandiseGiveawayDataInput!
}

input MerchandiseGiveawayDataInput {
  reward: String!
  rewardAmount: Float!
  description: String
  shopConfig: ShopConfigInput
  guardConfig: TaskGuardInput
  formItems: [FormAnswerTaskDataInput!]!
}

input MerchandiseGiveawayUpdateInput {
  parentId: ID
  condition: GiveawayEventCondition = NA
  title: String
  icon: String
  description: String
  winningMessage: String
  winnerCount: Int = -1
  frequency: Frequency
  startTime: DateTime
  endTime: DateTime
  eventRelativeWindow: Boolean = true
  hidden: Boolean = false
  whitelistWallets: [String!]
  data: MerchandiseGiveawayDataUpdateInput!
}

input MerchandiseGiveawayDataUpdateInput {
  reward: String
  rewardAmount: Float
  description: String
  shopConfig: ShopConfigInput
  guardConfig: TaskGuardInput
  formItems: [FormAnswerTaskDataInput!]
}

input ApiKeyIntegrationInput {
  name: String
  type: IntegrationType!
  data: ApiKeyIntegrationDataInput!
}

input ApiKeyIntegrationDataInput {
  """apiKey used for hard coded integrations like telegram"""
  apiKey: String
  name: String

  """arguments used for dynamics API integrations like email subscribtions"""
  args: [String!]
  webhookUrl: String
}

input ApiKeyIntegrationUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: ApiKeyIntegrationDataUpdateInput!
}

input ApiKeyIntegrationDataUpdateInput {
  """apiKey used for hard coded integrations like telegram"""
  apiKey: String
  name: String

  """arguments used for dynamics API integrations like email subscribtions"""
  args: [String!]
  webhookUrl: String
}

input CreateAirTokenInput {
  name: String!
  description: String
  baseURI: String
  ticker: String!
  transferable: Boolean!
  icon: String!
  blockchainId: String!
  contractAddress: String!
  assetType: AssetType!
  creatorBlockchainAddress: String!
}

input CreateAssetHubAirToken {
  name: String!
  description: String
  baseURI: String
  ticker: String!
  transferable: Boolean!
  icon: String!
  blockchainId: String!
  assetType: AssetType!
}

input RestTaskParticipationInput {
  queryParams: [RestParamParticipate]
  bodyParams: [RestParamParticipate]
  headers: [RestParamParticipate]
}

input RestParamParticipate {
  key: String!
  value: String!
}

input RestRawTaskInput {
  points: Int!
  xp: Int!
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: RestRawTaskDataInput!
  taskType: TaskType!
}

input RestRawTaskDataInput {
  url: String!
  requestType: RestRequestType!
  queryParams: [RestParamInput]
  bodyParams: [RestParamInput]
  headers: [RestParamInput]
  validation: String!
  blockchainId: String
  verifiedWallet: Boolean
}

input RestRawTaskUpdateInput {
  points: Int
  xp: Int
  title: String
  hidden: Boolean
  verify: VerifyType
  description: String
  frequency: Frequency
  parentId: String
  integrations: [IntegrationTaskInput!]
  guardConfig: TaskGuardInput
  iconUrl: String
  data: RestRawTaskDataUpdateInput!
}

input RestRawTaskDataUpdateInput {
  url: String
  requestType: RestRequestType
  queryParams: [RestParamInput!]
  bodyParams: [RestParamInput!]
  headers: [RestParamInput!]
  validation: String
  blockchainId: String
  verifiedWallet: Boolean
}

input BillingInput {
  planId: String!
  projectId: String!
  currentPeriodStart: DateTime!
  currentPeriodEnd: DateTime!
  amount: Float!
  currency: String!
  status: String!
  isCurrent: Boolean!
  txInfo: String
}

input BillingUpdateInput {
  planId: String
  projectId: String
  currentPeriodStart: DateTime
  currentPeriodEnd: DateTime
  amount: Float
  currency: String
  status: String
  isCurrent: Boolean
  txInfo: String
}

input BillingUsingCryptoInput {
  transHash: String!
  email: String!
  telegram: String!
}

input WinnerInput {
  userId: String!
  amount: String
}

input WidgetDataInput {
  domain: String
  eventId: String!
  config: WidgetConfigInput
}

input WidgetDataUpdateInput {
  domain: String
  eventId: String
  config: WidgetConfigInput
}

type Subscription {
  onUserNotification: [ID!]!
  onOAuthComplete(sessionId: ID!): AuthSubscriptionResponse
}