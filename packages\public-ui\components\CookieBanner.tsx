'use client';

import { useState, useEffect } from 'react';
import { Check, <PERSON>ie, Gear, X } from '@phosphor-icons/react';
import { cn } from '@Root/utils/utils';
import Button from './Button';
import { useUserDetails } from '@Hooks/useUserDetails';
import { CookieConsent, Onboarding } from '@airlyft/types';
import toaster from './Toaster/Toaster';
import useUpdateUserCookieConsent from '@Hooks/useUpdateUserCookieConsent';
import { useGtmTrack } from '@Root/services/tracking';
import { Dialog } from '@headlessui/react';

export default function CookieBanner() {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const { data, loading } = useUserDetails();
  const [updateUserCookieConsent] = useUpdateUserCookieConsent();
  const { signUpTrack } = useGtmTrack();

  useEffect(() => {
    if (loading) return;

    const serverOnboarded =
      data?.me?.onboarded?.includes(Onboarding.PARTICIPANT_TERMS) ?? false;
    const serverConsent = data?.me?.cookieConsent;

    const shouldShow = serverOnboarded && !serverConsent;

    let timer: ReturnType<typeof setTimeout>;
    if (shouldShow) {
      timer = setTimeout(() => setIsVisible(true), 1500);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [data, loading]);

  const cookieOnboarding = (consent: CookieConsent) => {
    updateUserCookieConsent({
      variables: {
        cookieConsent: consent,
      },
      onCompleted: () => {
        setIsVisible(false);
        signUpTrack(consent);
      },
      onError: () => {
        toaster({
          title: 'Update failed',
          text: 'Unable to update cookie consent',
          type: 'error',
        });
      },
    });
  };

  const dismiss = () => {
    setIsVisible(false);
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const content = (
    <Dialog.Panel className="backdrop-blur-lg bg-background/80 border border-border/60 rounded-2xl shadow-xl shadow-primary/20 overflow-hidden">
      <div className="relative">
        {/* Decorative elements */}
        <div className="absolute top-0 right-5 w-32 h-32 bg-primary/5 rounded-full blur-3xl -z-10"></div>
        <div className="absolute bottom-0 left-10 w-24 h-24 bg-blue-500/5 rounded-full blur-3xl -z-10"></div>

        <div className="p-5 md:p-6">
          <button
            onClick={dismiss}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            aria-label="Dismiss cookie banner"
          >
            <X className="h-4 w-4" />
          </button>

          <div className="flex flex-col md:flex-row items-start md:items-center gap-5 md:gap-6">
            <div className="flex-shrink-0 p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary">
              <Cookie className="h-6 w-6" />
            </div>

            <div className="flex-grow">
              <h3 className="text-base font-semibold mb-1.5 flex items-center">
                Cookie Preferences
                <span className="ml-2 text-xs py-0.5 px-1.5 bg-primary/10 text-primary rounded-full">
                  Privacy
                </span>
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 max-w-2xl">
                We use cookies to enhance your browsing experience and analyze
                our traffic. You can choose which cookies you want to allow.
              </p>

              <div
                className={cn(
                  'mt-4 space-y-3 overflow-hidden transition-all duration-300',
                  showDetails ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0',
                )}
              >
                <div className="flex items-center justify-between p-3 rounded-lg bg-card border">
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 bg-green-100 dark:bg-green-900/30 rounded-full">
                      <Check className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">Necessary</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Required for basic functionality
                      </div>
                    </div>
                  </div>
                  <div className="px-2 py-0.5 text-xs bg-primary/20 rounded">
                    Always On
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 rounded-lg bg-card border">
                  <div className="flex items-center gap-3">
                    <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                      <Gear className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">Analytics</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Help us improve our website
                      </div>
                    </div>
                  </div>
                  <div className="px-2 py-0.5 text-xs bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded">
                    Optional
                  </div>
                </div>
              </div>

              <button
                onClick={toggleDetails}
                className="text-xs text-primary hover:text-primary/80 underline-offset-2 hover:underline mt-2 inline-flex items-center"
              >
                {showDetails ? 'Hide details' : 'View cookie details'}
                <span
                  className={`ml-1 transition-transform duration-200 ${
                    showDetails ? 'rotate-180' : ''
                  }`}
                >
                  ▼
                </span>
              </button>
            </div>

            <div className="flex flex-col items-center gap-2 w-full md:w-[200px] md:flex-shrink-0">
              <Button
                size="small"
                className="text-xs w-full rounded-lg py-2"
                onClick={() => cookieOnboarding(CookieConsent.ACCEPT_ALL)}
              >
                Accept All
              </Button>
              <Button
                size="small"
                className="text-xs text-foreground w-full rounded-lg bg-card border py-2"
                onClick={() => cookieOnboarding(CookieConsent.NECESSARY)}
              >
                Necessary Only
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom gradient border */}
        {/* <div className="h-[2px] bg-gradient-to-r from-transparent via-primary/20 to-transparent"></div> */}
      </div>
    </Dialog.Panel>
  );

  return (
    <Dialog
      as="div"
      className="fixed inset-0 z-[99999] overflow-y-auto"
      onClose={() => {
        /* prevent auto-closing */
      }}
      open={isVisible}
    >
      <div className="fixed bottom-5 left-0 right-0 mx-auto max-w-4xl px-4">
        {content}
      </div>
    </Dialog>
  );
}
