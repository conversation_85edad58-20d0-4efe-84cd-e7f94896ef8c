import { BigNumberish } from 'ethers';

export enum QuizQuestionType {
  SINGLE_CHOICE = 'single-choice',
  MULTIPLE_CHOICE = 'multiple-choice',
}

export enum UploadType {
  TASK_ICON = 'TASK_ICON',
  PROJECT_LOGO = 'PROJECT_LOGO',
  BLOCKCHAIN_LOGO = 'BLOCKCHAIN_LOGO',
  ASSET_LOGO = 'ASSET_LOGO',
  BANNER = 'BANNER',
  USER_IMAGE = 'USER_IMAGE',
  RICH_TEXT_MEDIA = 'RICH_TEXT_MEDIA',
}

export interface UploadConfig {
  maxSize: number;
  aspectRatio?: {
    width: number;
    height: number;
  };
  allowedTypes?: string[];
  displayName: string;
  maxSizeByType?: Record<string, number>;
}

export const UPLOAD_CONFIGS: Record<UploadType, UploadConfig> = {
  [UploadType.TASK_ICON]: {
    maxSize: 512 * 1024,
    aspectRatio: { width: 1, height: 1 },
    displayName: 'task icon',
  },
  [UploadType.PROJECT_LOGO]: {
    maxSize: 1024 * 1024,
    aspectRatio: { width: 1, height: 1 },
    displayName: 'project logo',
  },
  [UploadType.BLOCKCHAIN_LOGO]: {
    maxSize: 1024 * 1024,
    aspectRatio: { width: 1, height: 1 },
    displayName: 'blockchain logo',
  },
  [UploadType.ASSET_LOGO]: {
    maxSize: 10 * 1024 * 1024,
    aspectRatio: { width: 1, height: 1 },
    displayName: 'asset logo',
    allowedTypes: ['image/*', 'video/*'],
    maxSizeByType: {
      image: 1024 * 1024,
      video: 10 * 1024 * 1024,
    },
  },
  [UploadType.BANNER]: {
    maxSize: 3 * 1024 * 1024,
    aspectRatio: { width: 16, height: 9 },
    displayName: 'banner',
  },
  [UploadType.USER_IMAGE]: {
    maxSize: 2 * 1024 * 1024,
    aspectRatio: { width: 1, height: 1 },
    displayName: 'user image',
  },
  [UploadType.RICH_TEXT_MEDIA]: {
    maxSize: 10 * 1024 * 1024,
    displayName: 'rich text media',
    allowedTypes: ['image/*', 'video/*'],
    aspectRatio: { width: 16, height: 9 },
    maxSizeByType: {
      image: 1024 * 1024,
      video: 10 * 1024 * 1024,
    },
  },
};

export interface IStorage {
  key?: string;
  action?: string;
  size?: number;
  uploadType?: UploadType;
}

export interface ICertificate {
  r: string;
  s: string;
  v: number;
  deadline: BigNumberish;
  nonce: BigNumberish;
}
