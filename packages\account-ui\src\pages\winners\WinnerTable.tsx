import ExportTable from '@Components/ExportTable';
import Table, { TableWrapper } from '@Components/Table';
import {
  GlobalSortOrder,
  useEntityFilterSorterPage,
} from '@Hooks/useEntityFilterSorterPage';
import { PAGE_SIZE } from '@Root/constants';
import {
  AirPoolGiveawayData,
  AirTokenGiveawayData,
  AssetType,
  GiveawayReviewStatus,
  Participant,
  ProjectEvent,
  RewardStatus,
  Winner,
} from '@airlyft/types';
import { Button, Pagination, Popconfirm, Space } from 'antd';
import { Fragment, useContext, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import usePageTotal from '@Hooks/usePageTotal';
import UserCellRenderer from '@Pages/participants/UserCellRenderer';
import GiveawaySelect from '@Root/giveaways/components/GiveawaySelect';
import { AppContext } from '@Root/states/AppContext';
import { formatAmount } from '@airlyft/web3-evm';
import { DeleteOutlined } from '@ant-design/icons';
import moment from 'moment';
import { useTranslation } from 'react-i18next';
import GiveawaySettlementAction from './GiveawaySettlementActions';
import RewardStatusTag from './RewardStatusTag';
import WinnerFilter from './WinnerFilter';
import { useDeleteWinner } from './hooks/useDeleteWinner';
import { useDownloadWinners } from './hooks/useDownloadWinners';
import { useGetWinners } from './hooks/useGetWinners';
import useWinnerActiveGiveaway from './hooks/useWinnerActiveGiveaway';

export const WINNERS_KEY_PREFIX = 'wn_';
export const winnersEntityFilterSorter = (eventId: string) =>
  WINNERS_KEY_PREFIX + eventId;

export default function WinnersTable({
  projectEvent,
  isLoading,
}: {
  projectEvent: ProjectEvent | undefined;
  isLoading?: boolean;
}) {
  const { t } = useTranslation();
  const {
    state: { projectId },
  } = useContext(AppContext);
  const { eventId } = useParams();

  const navigate = useNavigate();

  const {
    initialized: activeGiveawayInitialized,
    loading: giveawayLoading,
    giveaways,
    activeGiveaway,
    activate: activateGiveaway,
  } = useWinnerActiveGiveaway(eventId!);

  const entityFilterSorterPageId = winnersEntityFilterSorter(eventId!);

  const {
    initialized,
    page,
    update,
    filters,
    pagination,
    formattedFilters,
    reset,
  } = useEntityFilterSorterPage(entityFilterSorterPageId);

  const where = {
    ...(formattedFilters || {}),
    eventId: eventId!,
    giveawayId: activeGiveaway?.id || '',
  };

  const { data: winnersData, loading: winnersLoading } = useGetWinners(
    projectId,
    pagination,
    where,
    !initialized || !activeGiveawayInitialized,
  );

  const { download, loading: downloading } = useDownloadWinners({
    projectId,
    where,
    fileName: projectEvent?.publicLink,
  });

  const { total } = usePageTotal({
    skip: !winnersData?.winners,
    total: winnersData?.winners?.total || 0,
  });

  const { deleteWinner, isDeletingById } = useDeleteWinner(
    projectId,
    pagination,
    where,
  );
  const loading = winnersLoading || isLoading || giveawayLoading;

  const [isClearingDraft, setIsClearingDraft] = useState(false);

  const draftWinnersLength = useMemo(() => {
    return winnersData?.winners?.data.filter(
      (winner) => winner.status === RewardStatus.DRAFT,
    ).length;
  }, [winnersData]);

  const formatGiveawayAmount = (amount: string) => {
    if (!amount) return '-';

    if (activeGiveaway?.info?.__typename === 'AirTokenGiveawayData') {
      const giveawayInfo = activeGiveaway.info as AirTokenGiveawayData;
      if (
        [AssetType.DOTSAMA_TOKEN, AssetType.ERC20, AssetType.NATIVE].includes(
          giveawayInfo.airToken.assetType,
        ) &&
        giveawayInfo.airToken.decimals
      ) {
        return formatAmount(amount, giveawayInfo.airToken.decimals);
      }
    }

    if (activeGiveaway?.info?.__typename === 'AirPoolGiveawayData') {
      const giveawayInfo = activeGiveaway.info as AirPoolGiveawayData;
      if (
        [
          AssetType.DOTSAMA_TOKEN,
          AssetType.ERC20,
          AssetType.NATIVE,
          AssetType.DOTSAMA_TOKEN,
        ].includes(giveawayInfo.airPool.asset.assetType) &&
        giveawayInfo.airPool.asset.decimals
      ) {
        return formatAmount(amount, giveawayInfo.airPool.asset.decimals);
      }
    }
    return amount;
  };

  const columns: any[] = [
    {
      title: 'User',
      dataIndex: ['id'],
      key: 'id',
      width: 350,
      render: (id: string, record: Winner) => (
        <UserCellRenderer
          displayName={record.displayName}
          auth={record.primaryAuth!}
          onClick={() => {
            if (!record.primaryAuth) return;
            navigate(
              `/campaigns/${eventId}/winners/${record.primaryAuth.userId}`,
            );
          }}
        />
      ),
    },
    {
      title: t('winners.columns.amount'),
      dataIndex: ['amount'],
      key: 'amount',
      width: 160,
      render: (amount: string) => formatGiveawayAmount(amount),
    },
    {
      title: t('winners.columns.status'),
      dataIndex: ['status'],
      key: 'status',
      width: 160,
      render: (status: RewardStatus) => <RewardStatusTag status={status} />,
    },
    {
      title: t('winners.columns.updatedAt'),
      dataIndex: ['updatedAt'],
      key: 'updatedAt',
      width: 170,
      ellipsis: true,
      render: (updatedAt: Date) =>
        moment(updatedAt).format('YYYY-MM-DD HH:mm a'),
    },
    ...(activeGiveaway?.latestReviewStatus === GiveawayReviewStatus.IN_REVIEW
      ? [
          {
            title: 'Actions',
            dataIndex: ['id'],
            key: 'action',
            width: 110,
            fixed: 'right',
            align: 'center',
            render: (id: string, record: Winner) => {
              if (record.status !== RewardStatus.DRAFT) return '-';
              const isLoading = isDeletingById(id);
              return (
                <Popconfirm
                  title="Delete this entry"
                  description="Are you sure to delete this entry?"
                  onConfirm={() => {
                    if (draftWinnersLength === 1) {
                      setIsClearingDraft(true);
                    } else {
                      deleteWinner(id);
                    }
                  }}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    size="small"
                    icon={<DeleteOutlined />}
                    loading={isLoading}
                    disabled={isLoading}
                  >
                    Delete
                  </Button>
                </Popconfirm>
              );
            },
          },
        ]
      : []),
  ];

  return (
    <Fragment>
      <TableWrapper
        offset={100}
        header={
          <Fragment>
            <Space align="start">
              <GiveawaySelect
                giveaways={giveaways || []}
                loading={giveawayLoading}
                value={activeGiveaway?.id}
                onChange={(value) => {
                  activateGiveaway(value);
                  reset({ page: true });
                }}
              />
              <WinnerFilter
                projectEventId={eventId!}
                id={entityFilterSorterPageId}
              />
            </Space>

            <ExportTable
              isFilteredData={filters.length > 0}
              onClick={download}
              isLoading={downloading}
            />
          </Fragment>
        }
        skeletonProps={{
          loading,
          active: loading,
          size: 'small',
          columns,
          rowCount: PAGE_SIZE,
        }}
        footer={
          <Fragment>
            <div>Total {total}</div>
            <Space>
              <Pagination
                onChange={(page) => {
                  update({ page });
                }}
                total={total}
                current={page}
                pageSize={PAGE_SIZE}
                showSizeChanger={false}
                size="small"
              />
              <GiveawaySettlementAction
                projectId={projectId}
                giveaway={activeGiveaway}
                projectEventId={eventId!}
                projectEvent={projectEvent}
                isClearingDraft={isClearingDraft}
                onClearDraft={() => {
                  setIsClearingDraft(false);
                }}
              />
            </Space>
          </Fragment>
        }
      >
        <Table
          size="small"
          loading={isLoading}
          rowKey={(record) => (record as Participant).userId}
          columns={columns}
          dataSource={winnersData?.winners?.data || []}
          sortDirections={['ascend', 'descend', 'ascend']}
          showSorterTooltip={false}
          onChange={(_, _f, sorter: any) => {
            update({
              sorter: {
                direction:
                  GlobalSortOrder[sorter.order as keyof typeof GlobalSortOrder],
                sortKey: sorter.columnKey,
              },
            });
          }}
          pagination={false}
          scroll={{
            y: '800px',
          }}
        />
      </TableWrapper>
    </Fragment>
  );
}
