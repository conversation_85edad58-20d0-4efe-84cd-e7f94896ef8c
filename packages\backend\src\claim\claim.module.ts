import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GiveawayEvent } from '@root/giveaway/entities/giveaway-event.entity';
import { Giveaway } from '@root/giveaway/entities/giveaway.entity';
import { ProjectEvent } from '@root/project-event/project-event.entity';
import { TaskParticipation } from '@root/task-participation/task-participation.entity';
import { ClaimResolver } from './claim.resolver';
import { ClaimService } from './claim.service';
import { EventReward } from './event-reward.entity';
import { ClaimPublicResolver } from './claim.public.resolver';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      EventReward,
      GiveawayEvent,
      Giveaway,
      ProjectEvent,
      TaskParticipation,
    ]),
  ],
  providers: [ClaimService, ClaimResolver, ClaimPublicResolver],
  exports: [ClaimService],
})
export class ClaimModule {}
