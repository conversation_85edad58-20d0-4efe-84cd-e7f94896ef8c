import { registerEnumType } from '@nestjs/graphql';

export enum AppType {
  DISCORD = 'DISCORD',
  FORM = 'FORM',
  MEDIA = 'MEDIA',
  QUIZ = 'QUIZ',
  EVM = 'EVM',
  SUBSOCIAL = 'SUBSOCIAL',
  TELEGRAM = 'TELEGRAM',
  TWITTER = 'TWITTER',
  UPLOAD = 'UPLOAD',
  URL = 'URL',
  INSTAGRAM = 'INSTAGRAM',
  YOUTUBE = 'YOUTUBE',
  WALLET = 'WALLET',
  SUBGRAPH = 'SUBGRAPH',
  AIRBOOST = 'AIRBOOST',
  REST = 'REST',
  EMAIL = 'EMAIL',
  TERMS = 'TERMS',
  CHECKIN = 'CHECKIN',
  MOBILE_APP = 'MOBILE_APP',
  SECRET_CODE = 'SECRET_CODE',
  BLOG = 'BLOG',
  KICKSTARTER = 'KICKSTARTER',
  FLASHCARD = 'FLASHCARD',
  //Constants only for account UI
  SUBSQUID = 'SUBSQUID',
  SUBQUERY = 'SUBQUERY',
  FAUCET = 'FAUCET',
  AIRQUEST = 'AIRQUEST',
  SUBSTRATE = 'SUBSTRATE',
  LUCKYDRAW = 'LUCKYDRAW',
  PRODUCTHUNT = 'PRODUCTHUNT',
}

export enum TaskType {
  DISCORD_JOIN = 'DISCORD_JOIN',
  FORM_ANSWER = 'FORM_ANSWER',
  QUIZ_PLAY = 'QUIZ_PLAY',
  EVM_CONTRACT = 'EVM_CONTRACT',
  SUBSTRATE_QUERY = 'SUBSTRATE_QUERY',
  SUBSTRATE_ASSET_BALANCE = 'SUBSTRATE_ASSET_BALANCE',
  SUBSTRATE_HOLD_NFT = 'SUBSTRATE_HOLD_NFT',
  SUBSTRATE_BALANCE = 'SUBSTRATE_BALANCE',
  SUBSTRATE_STAKE = 'SUBSTRATE_STAKE',
  SUBSTRATE_BOND_POOL = 'SUBSTRATE_BOND_POOL',
  SUBSTRATE_NOMINATE = 'SUBSTRATE_NOMINATE',
  SUBSOCIAL_COMMENT = 'SUBSOCIAL_COMMENT',
  SUBSOCIAL_FOLLOW = 'SUBSOCIAL_FOLLOW',
  SUBSOCIAL_POST = 'SUBSOCIAL_POST',
  SUBSOCIAL_PROFILE = 'SUBSOCIAL_PROFILE',
  SUBSOCIAL_SHARE = 'SUBSOCIAL_SHARE',
  SUBSOCIAL_SPACE = 'SUBSOCIAL_SPACE',
  SUBSOCIAL_UPVOTE = 'SUBSOCIAL_UPVOTE',
  UPLOAD_FILE = 'UPLOAD_FILE',
  TELEGRAM_JOIN = 'TELEGRAM_JOIN',
  TWITTER_FOLLOW = 'TWITTER_FOLLOW',
  TWITTER_JOIN = 'TWITTER_JOIN',
  TWITTER_LIKE = 'TWITTER_LIKE',
  TWITTER_POST = 'TWITTER_POST',
  TWITTER_RETWEET = 'TWITTER_RETWEET',
  TWITTER_UGC = 'TWITTER_UGC',
  TWITTER_UPLOAD = 'TWITTER_UPLOAD',
  TWITTER_LIKE_RETWEET = 'TWITTER_LIKE_RETWEET',
  TWITTER_WHITELIST = 'TWITTER_WHITELIST',
  URL_VISIT = 'URL_VISIT',
  URL_VIEW = 'URL_VIEW',
  URL_SHARE = 'URL_SHARE',
  INSTAGRAM_VISIT = 'INSTAGRAM_VISIT',
  INSTAGRAM_VIEW = 'INSTAGRAM_VIEW',
  INSTAGRAM_SHARE = 'INSTAGRAM_SHARE',
  YOUTUBE_VISIT = 'YOUTUBE_VISIT',
  YOUTUBE_VIEW = 'YOUTUBE_VIEW',
  YOUTUBE_SHARE = 'YOUTUBE_SHARE',
  WALLET_EVM = 'WALLET_EVM',
  WALLET_DOTSAMA = 'WALLET_DOTSAMA',
  SUBGRAPH_RAW = 'SUBGRAPH_RAW',
  AIRBOOST_REFERRAL = 'AIRBOOST_REFERRAL',
  REST_RAW = 'REST_RAW',
  EMAIL_ADDRESS = 'EMAIL_ADDRESS',
  EMAIL_WHITELIST = 'EMAIL_WHITELIST',
  EMAIL_SUBSCRIBE = 'EMAIL_SUBSCRIBE',
  TERMS_TEXT = 'TERMS_TEXT',
  TERMS_EVM = 'TERMS_EVM',
  TERMS_DOTSAMA = 'TERMS_DOTSAMA',
  CHECKIN_DAILY = 'CHECKIN_DAILY',
  LUCKYDRAW_PLAY = 'LUCKYDRAW_PLAY',
  LUCKYDRAW_SLOT = 'LUCKYDRAW_SLOT',
  LUCKYDRAW_BOX = 'LUCKYDRAW_BOX',
  SECRET_CODE_VALIDATE = 'SECRET_CODE_VALIDATE',
  BLOG_COMMENT = 'BLOG_COMMENT',
  BLOG_WRITE = 'BLOG_WRITE',
  KICKSTARTER_SUPPORT = 'KICKSTARTER_SUPPORT',
  FLASHCARD_VIEW = 'FLASHCARD_VIEW',
  //Constants only for account UI
  SUBSQUID_RAW = 'SUBSQUID_RAW',
  SUBQUERY_RAW = 'SUBQUERY_RAW',
  REST_EVM = 'REST_EVM',
  REST_DOTSAMA = 'REST_DOTSAMA',
  FAUCET_DOTSAMA = 'FAUCET_DOTSAMA',
  FAUCET_EVM = 'FAUCET_EVM',
  AIRQUEST_FOLLOW = 'AIRQUEST_FOLLOW',
  MOBILE_APP_INSTALL = 'MOBILE_APP_INSTALL',
  PRODUCTHUNT_UPVOTE = 'PRODUCTHUNT_UPVOTE',
  PRODUCTHUNT_FOLLOW = 'PRODUCTHUNT_FOLLOW',
}

export enum VerifyType {
  AUTO = 1,
  NONE = 2,
  MANUAL = 3,
  AI = 4,
}

export enum Frequency {
  NONE = 'NONE',
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  YEAR = 'YEAR',
}

export enum TaskRuleType {
  DATE = 'DATE',
  TASK_ID = 'TASK_ID',
  XP = 'XP',
  DISCORD_ROLE = 'DISCORD_ROLE',
  MAX_PARTICIPANTS = 'MAX_PARTICIPANTS',
}

export enum RuleOperator {
  LT = 'LT',
  GT = 'GT',
  EQ = 'EQ',
  LTE = 'LTE',
  GTE = 'GTE',
  NE = 'NE',
}

export enum ParticipationStatus {
  VALID = 'VALID',
  INVALID = 'INVALID',
  IN_REVIEW = 'IN_REVIEW',
  IN_AI_VERIFICATION = 'IN_AI_VERIFICATION',
}

export enum AccountParticipationStatus {
  VALID = 'VALID',
  INVALID = 'INVALID',
}

export enum EmailVerificationType {
  ANY = 'ANY',
  LIST = 'LIST',
}

export type TaskKey<T = string> = T;

// Register enum
registerEnumType(AppType, { name: 'AppType' });
registerEnumType(TaskType, { name: 'TaskType' });
registerEnumType(VerifyType, { name: 'VerifyType' });
registerEnumType(Frequency, { name: 'Frequency' });
registerEnumType(TaskRuleType, { name: 'TaskRuleType' });
registerEnumType(RuleOperator, { name: 'RuleOperator' });
registerEnumType(ParticipationStatus, { name: 'ParticipationStatus' });
registerEnumType(AccountParticipationStatus, {
  name: 'AccountParticipationStatus',
});
registerEnumType(EmailVerificationType, { name: 'EmailVerificationType' });
export const HierarchicalTask = [TaskType.QUIZ_PLAY];

export const isHierarchicalTask = (taskType: TaskType) =>
  HierarchicalTask.includes(taskType);
