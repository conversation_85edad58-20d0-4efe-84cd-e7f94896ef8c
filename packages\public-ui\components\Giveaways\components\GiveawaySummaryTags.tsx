import { FeatureGuard } from '@Components/FeatureGuard';
import { Tag } from '@Components/Tag';
import { GiveawaySummary, GiveawayType } from '@airlyft/types';
import {
  Rocket,
  Coins,
  Star,
  Sparkle,
  Fire,
  TShirt,
} from '@phosphor-icons/react';
import { useTranslation } from 'next-i18next';
import { useState, useEffect, useRef, Fragment, useMemo } from 'react';
import Image from 'next/image';
import { pluralize } from '@Root/utils/string-utils';

type TagItem = {
  id: string;
  title: string;
  icon?: React.ReactNode;
  type: 'summary' | 'xp' | 'points';
};

export const GiveawaySummaryTags = ({
  summaries,
  totalPoints,
  totalXP,
  size = 'small',
}: {
  summaries: GiveawaySummary[];
  totalXP?: number;
  totalPoints?: number;
  size?: 'default' | 'large' | 'small';
}) => {
  const { t: globalT } = useTranslation('ssr', { keyPrefix: 'global' });
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleTags, setVisibleTags] = useState<TagItem[]>([]);
  const [hiddenCount, setHiddenCount] = useState(0);

  const formatMeta = (groupKey: GiveawayType) => {
    switch (groupKey) {
      case GiveawayType.ERC1155_AIR_TOKEN:
      case GiveawayType.ERC721_AIR_TOKEN:
      case GiveawayType.DOTSAMA_NFT_AIR_TOKEN:
        return {
          icon: (
            <Rocket size={16} className="text-fuchsia-500" weight="duotone" />
          ),
          title: 'NFT',
        };
      case GiveawayType.ERC20_AIR_TOKEN:
      case GiveawayType.DOTSAMA_TOKEN_AIR_TOKEN:
        return {
          icon: <Coins size={16} className="text-teal-500" weight="duotone" />,
          title: 'AirToken',
        };
      case GiveawayType.WHITELIST:
        return {
          icon: <Star size={16} className="text-cyan-500" weight="duotone" />,
          title: 'Whitelist',
        };
      case GiveawayType.MERCHANDISE:
        return {
          icon: (
            <TShirt size={16} className="text-purple-500" weight="duotone" />
          ),
          title: 'Merchandise',
        };
    }
  };

  const group = useMemo(() => {
    return summaries.reduce((acc, item) => {
      return {
        ...acc,
        [item.giveawayType]: [...(acc[item.giveawayType] || []), item],
      };
    }, {} as Record<GiveawayType, Array<GiveawaySummary>>);
  }, [summaries]);

  const allTags = useMemo(() => {
    const tags: TagItem[] = [];
    const uniqueTitles = new Set<string>();

    (Object.keys(group) as GiveawayType[]).forEach((item, index) => {
      const items = group[item];

      if (
        item === GiveawayType.ERC1155_AIR_POOL ||
        item === GiveawayType.ERC20_AIR_POOL ||
        item === GiveawayType.ERC721_AIR_POOL ||
        item === GiveawayType.DOTSAMA_TOKEN_AIR_POOL ||
        item === GiveawayType.DOTSAMA_NFT_AIR_POOL
      ) {
        items.forEach((summary, idx) => {
          const poolTitle = summary?.title || '';

          if (!uniqueTitles.has(poolTitle)) {
            uniqueTitles.add(poolTitle);

            tags.push({
              id: `pool-${index}-${idx}`,
              title: poolTitle,
              icon: summary?.icon ? (
                <Image
                  src={summary.icon}
                  width={20}
                  height={20}
                  className="rounded-full"
                  alt={poolTitle || 'Token icon'}
                />
              ) : undefined,
              type: 'summary',
            });
          }
        });
      } else {
        const meta = formatMeta(item as GiveawayType);
        if (meta) {
          if (!uniqueTitles.has(meta.title)) {
            uniqueTitles.add(meta.title);
            tags.push({
              id: `type-${item}-${index}`,
              title: meta.title,
              icon: <span className="w-5 h-5">{meta.icon}</span>,
              type: 'summary',
            });
          }
        }
      }
    });

    if (typeof totalXP === 'number' && totalXP > 0) {
      tags.push({
        id: 'xp',
        title: `${totalXP} ${pluralize(
          totalXP,
          globalT('projectXp'),
          globalT('projectXp_many'),
        )}`,
        icon: (
          <Sparkle size={16} weight="duotone" className="text-orange-500" />
        ),
        type: 'xp',
      });
    }

    if (typeof totalPoints === 'number' && totalPoints > 0) {
      tags.push({
        id: 'points',
        title: `${totalPoints} ${pluralize(
          totalPoints,
          globalT('projectPoints'),
          globalT('projectPoints_many'),
        )}`,
        icon: <Fire size={16} weight="duotone" className="text-blue-500" />,
        type: 'points',
      });
    }

    return tags;
  }, [group, totalXP, totalPoints, globalT]);

  useEffect(() => {
    if (!containerRef.current || allTags.length === 0) return;

    const resizeObserver = new ResizeObserver(() => {
      if (!containerRef.current) return;

      const containerWidth = containerRef.current.clientWidth;
      const moreTagWidth = 64;
      const gap = 8;

      // Create temporary tags to measure their widths
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.visibility = 'hidden';
      tempDiv.style.display = 'flex';
      tempDiv.style.gap = `${gap}px`;
      document.body.appendChild(tempDiv);

      // Create temporary elements for each tag to measure width
      const tagWidths = allTags.map((tag) => {
        const elem = document.createElement('div');
        elem.className = 'tag-measure';
        elem.textContent = tag.title;
        elem.style.padding = '0 12px';
        elem.style.whiteSpace = 'nowrap';
        elem.style.fontSize = size === 'small' ? '12px' : '14px';
        tempDiv.appendChild(elem);
        return elem.offsetWidth + 28;
      });

      document.body.removeChild(tempDiv);

      let currentWidth = 0;
      let visibleCount = 0;

      let needsMoreTag = false;
      let totalWidth = 0;

      for (let i = 0; i < tagWidths.length; i++) {
        totalWidth += tagWidths[i];
        if (i < tagWidths.length - 1) {
          totalWidth += gap;
        }
      }

      needsMoreTag = totalWidth > containerWidth;

      const availableWidth = needsMoreTag
        ? containerWidth - moreTagWidth - gap
        : containerWidth;

      for (let i = 0; i < tagWidths.length; i++) {
        if (currentWidth + tagWidths[i] <= availableWidth) {
          currentWidth += tagWidths[i] + (i > 0 ? gap : 0);
          visibleCount++;
        } else {
          break;
        }
      }

      setVisibleTags(allTags.slice(0, visibleCount));
      setHiddenCount(allTags.length - visibleCount);
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [allTags, size]);

  return (
    <div
      className="flex relative overflow-x-hidden w-full gap-2"
      ref={containerRef}
    >
      {visibleTags.map((tag) => {
        if (tag.type === 'xp') {
          return (
            <FeatureGuard feature="XP" key={tag.id}>
              <Tag
                title={tag.title}
                size={size}
                icon={tag.icon}
                className="!font-semibold"
              />
            </FeatureGuard>
          );
        }

        if (tag.type === 'points') {
          return (
            <FeatureGuard feature="POINTS" key={tag.id}>
              <Tag
                title={tag.title}
                size={size}
                icon={tag.icon}
                className="!font-semibold"
              />
            </FeatureGuard>
          );
        }

        return (
          <Tag
            key={tag.id}
            title={tag.title}
            size={size}
            icon={tag.icon}
            className="!font-semibold"
          />
        );
      })}

      {hiddenCount > 0 && (
        <Tag
          title={`+${hiddenCount} more`}
          size={size}
          className="!font-semibold !px-2 max-w-[64px]"
        />
      )}
    </div>
  );
};
