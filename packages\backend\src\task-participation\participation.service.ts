import { Air<PERSON>yftError, AuthProvider } from '@models/auth';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { Auth } from '@root/auth/auth.entity';
import { PaginationInput } from '@root/core/dto/pagination.dto';
import { SortInput } from '@root/core/dto/sort-input.dto';
import { UpdateResult } from '@root/core/dto/update-result.dto';
import { EventState } from '@root/project-event/project-event.entity';
import { Task } from '@root/task/task.entity';
import {
  TASK_PARTICIPATED,
  TaskParticipatedPayload,
} from '@root/task/task.events';
import { TaskService } from '@root/task/task.service';
import { XPService } from '@root/xp/xp.service';
import {
  DataSource,
  EntityManager,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import {
  buildTaskRulesToQuery,
  taskFrequencyRule,
  taskParticipationRule,
} from '../task/task-rule';
import { TaskParticipationData } from './task-participation-data.entity';
import { TaskParticipationLock } from './task-participation-lock.entity';
import {
  Participant,
  ParticipantWhereInput,
  ParticipantsData,
  SubmissionWhereInput,
  TaskSubmissionsData,
} from './task-participation.dto';
import { TaskParticipation } from './task-participation.entity';
import {
  AccountParticipationStatus,
  ParticipationStatus,
  VerifyType,
} from '@root/task/task.constants';
import { ProjectEventService } from '@root/project-event/project-event.service';
import { SidecarService } from '@root/sidecar/sidecar.service';
import { WebhookService, WebhookPayload } from './webhook.service';

interface TaskParticipationPipeline<T> {
  getData?: (task: Task) => Promise<T> | T;
  preProcessor?: (task: Task, data: T) => Promise<T> | T;
  builders?: Array<
    (task: Task, data: T) => Promise<SelectQueryBuilder<TaskParticipation>>
  >;
  validators?: Array<(task: Task, data: T) => Promise<boolean> | boolean>;
  processor?: (task: Task, data: T, validation?: boolean[]) => Promise<T> | T;
  throwValidationError?: boolean;
  getScore?: (
    task: Task,
    data: T,
    validation?: boolean[],
  ) => Promise<{ xp: number; points: number }> | { xp: number; points: number };
  postValidation?: (
    task: Task,
    data: T,
  ) => Promise<{ data: T; success: boolean }>;
  finalize?: (task: Task, data: T, participationId: string) => Promise<void>;
}

interface AuthId {
  provider?: AuthProvider;
  providerId?: string;
}

interface TaskParticipationExtraArgs {
  authId?: AuthId;
  referralCode?: string;
  globalReferralCode?: string;
  hashedIp?: string;
}

@Injectable()
export class ParticipationService {
  constructor(
    @InjectRepository(TaskParticipation)
    private readonly repository: Repository<TaskParticipation>,

    @InjectRepository(TaskParticipationData)
    private readonly dataRepository: Repository<TaskParticipationData>,

    private readonly eventService: ProjectEventService,

    private readonly taskService: TaskService,

    private readonly xpService: XPService,

    private sidecarService: SidecarService,

    private webhookService: WebhookService,

    private eventEmitter: EventEmitter2,

    private dataSource: DataSource,
  ) {}

  findByTaskId(userId: string, taskId: string) {
    return this.repository.findOne({
      where: {
        userId,
        task: {
          id: taskId,
        },
      },
    });
  }

  faucetCheckIfAddressUsed(taskId: string, address: string) {
    return this.repository
      .createQueryBuilder()
      .innerJoin(
        TaskParticipationData,
        'tpd',
        '"TaskParticipation".id = "tpd"."participationId"',
      )
      .where(`"tpd"."data"->>'address' = :address`, { address })
      .andWhere('"taskId" = :taskId', { taskId })
      .getOne();
  }

  countByUserId(userId: string, eventId: string) {
    return this.repository
      .createQueryBuilder()
      .where('"userId" = :userId', { userId })
      .andWhere('"eventId" = :eventId', { eventId })
      .getCount();
  }

  findValidByTaskIds(userId: string, ids: Array<string>) {
    return this.repository
      .createQueryBuilder()
      .where('"taskId" IN (:...ids)', {
        ids,
      })
      .andWhere('"userId" = :userId', { userId })
      .andWhere('status = :status', { status: ParticipationStatus.VALID })
      .getMany();
  }

  findParticipantStats(userId: string, eventId: string) {
    return this.repository
      .createQueryBuilder('tp')
      .select('count(distinct(tp."taskId"))', 'totalTask')
      .addSelect('sum(tp.points)', 'totalPoints')
      .where('tp.eventId = :eventId', {
        eventId: eventId,
      })
      .andWhere('tp.userId = :userId', {
        userId,
      })
      .andWhere('tp.status = :status', { status: ParticipationStatus.VALID })
      .getRawOne();
  }

  findOne(participationId: string) {
    return this.repository
      .createQueryBuilder('task_participations')
      .leftJoinAndSelect('task_participations.data', 'data')
      .leftJoinAndSelect('task_participations.task', 'task')
      .where('task_participations.id = :participationId', { participationId })
      .getOne();
  }

  findUserParticipationByEventId(
    userId: string,
    eventId: string,
    mode: number,
  ) {
    return this.repository
      .createQueryBuilder('task_participations')
      .leftJoinAndSelect('task_participations.data', 'data')
      .leftJoinAndSelect('task_participations.task', 'task')
      .where('task_participations.eventId = :eventId', { eventId })
      .andWhere('task_participations.userId = :userId', { userId })
      .andWhere('task_participations.mode = :mode', { mode })
      .orderBy('task_participations.createdAt', 'DESC')
      .getMany();
  }

  findByEventId(eventId: string, mode: number) {
    return this.repository
      .createQueryBuilder('task_participations')
      .leftJoinAndSelect('task_participations.data', 'data')
      .leftJoin('task_participations.task', 'task')
      .select('task.taskType', 'taskType')
      .where({
        event: { id: eventId },
        mode,
      })
      .getMany();
  }

  /**
   * Updates a participation status
   * Also modifies 'fuel', 'weekly_xp' and 'project_event_summaries' if required
   */
  updateTaskParticipationStatus(
    projectId: string,
    eventId: string,
    participationId: string,
    newStatus: AccountParticipationStatus,
  ): Promise<UpdateResult> {
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const data = await transactionalEntityManager.query(
        `
        UPDATE
          task_participations tp
        SET
          status = $1
        FROM
          project_events pe,
          task_participations tpold
        WHERE 
          pe.state != '${EventState.SETTLED}'
          AND pe."projectId" = $2
          AND pe.id = $3
          AND tp.id = $4
          AND tp."eventId" = pe.id
          AND tp.id = tpold.id
        RETURNING tp.xp, tp.points, tp."userId", tpold.status, tpold.id
      `,
        [newStatus, projectId, eventId, participationId],
      );

      const result = data?.[0]?.[0];
      const originalStatus = result?.status;

      if (result?.xp && result?.points && result?.userId) {
        const multiplier = this.findMultiplier(originalStatus, newStatus);
        if (multiplier != 0) {
          await this.updateXPFuelSummaryTx({
            userId: result.userId,
            points: result.points,
            xp: result.xp,
            projectId,
            multiplier,
            transactionalEntityManager,
            eventId,
          });
        }
      }

      return {
        affected: data?.[1] || 0,
      };
    });
  }

  /**
   * Deletes a participation entry
   * Also modifies 'fuel', 'weekly_xp' and 'project_event_summaries' if required
   */
  deleteParticipation(
    projectId: string,
    eventId: string,
    participationId: string,
  ) {
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const result = await transactionalEntityManager
        .createQueryBuilder()
        .delete()
        .from(TaskParticipation)
        .where(
          'id = :participationId AND eventId = :eventId AND eventId IN ' +
            '(SELECT id FROM project_events WHERE id = :eventId AND "projectId" = :projectId)',
          {
            participationId,
            eventId,
            projectId,
          },
        )
        .returning('*')
        .execute();
      const participation = result.raw?.[0];
      const oldStatus = participation.status;
      if (participation.xp && participation.points && participation.userId) {
        //Get multiplier, in case a VALID task is being deleted then we need to update Fuel, XP and Summary
        const multiplier = this.findMultiplier(
          oldStatus,
          AccountParticipationStatus.INVALID,
        );
        if (multiplier != 0) {
          await this.updateXPFuelSummaryTx({
            userId: participation.userId,
            points: participation.points,
            xp: participation.xp,
            projectId,
            multiplier,
            transactionalEntityManager,
            eventId,
          });
        }
      }
      return result;
    });
  }

  private async updateXPFuelSummaryTx({
    userId,
    points,
    xp,
    projectId,
    multiplier,
    transactionalEntityManager,
    eventId,
  }: {
    userId: string;
    points: number;
    xp: number;
    projectId: string;
    multiplier: number;
    transactionalEntityManager: EntityManager;
    eventId: string;
  }) {
    await this.xpService.updateFuelAndXPTx(
      userId,
      projectId,
      points * multiplier,
      xp * multiplier,
      transactionalEntityManager,
    );
    await this.eventService.updateSummary(
      eventId,
      {
        totalPointsEarned: points * multiplier,
        totalTaskParticipation: multiplier,
      },
      transactionalEntityManager,
    );
  }

  private findMultiplier(
    originalStatus: any,
    newStatus: AccountParticipationStatus,
  ): number {
    //If no change in status then return 0
    if (originalStatus === newStatus) {
      return 0;
    }

    if (
      originalStatus === ParticipationStatus.INVALID ||
      originalStatus === ParticipationStatus.IN_REVIEW
    ) {
      if (newStatus === AccountParticipationStatus.VALID) {
        return 1;
      } else {
        return 0;
      }
    } else {
      //It was Valid earlier then it means that
      //it has either been marked invalid or in review
      return -1;
    }
  }

  /**
   * USE WITH CAUTION
   * This function does not check for event state and directly increments the points
   */
  incrementPointsAndXP(
    participation: TaskParticipation,
    incrementPointsBy: number,
    incrementXpBy: number,
    maxPoints: number,
    maxXp: number,
    userId: string,
    projectId: string,
  ): Promise<UpdateResult> {
    if (participation.xp >= maxXp || participation.points >= maxPoints) {
      return;
    }
    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const updateResult = await transactionalEntityManager
        .createQueryBuilder()
        .update(TaskParticipation)
        .set({
          xp: () => `LEAST(xp + ${incrementXpBy}, ${maxXp})`,
          points: () => `LEAST(points + ${incrementPointsBy}, ${maxPoints})`,
        })
        .where('id = :id', { id: participation.id })
        .returning(['xp', 'points'])
        .execute();

      if (updateResult?.affected > 0) {
        incrementPointsBy = updateResult.raw?.[0].points - participation.points;
        incrementXpBy = updateResult.raw?.[0].xp - participation.xp;
        await this.xpService.updateFuelAndXPTx(
          userId,
          projectId,
          incrementPointsBy,
          incrementXpBy,
          transactionalEntityManager,
        );
      }

      return updateResult;
    });
  }

  participationLock = () => {
    return {
      acquire: async (userId, taskId) => {
        try {
          //Create a participation lock for this userId and taskId
          await this.dataSource.query(
            'CALL acquire_participation_lock($1, $2)',
            [userId, taskId],
          );
        } catch (e) {
          throw new BadRequestException(AirLyftError.PARTICIPATION_LOCKED);
        }
      },
      release: async (userId, taskId) => {
        try {
          await this.dataSource
            .getRepository(TaskParticipationLock)
            .delete({ userId, taskId: taskId });
        } catch (e) {
          //ignore in case delete was not possible, it will automatically expire in an hour
        }
      },
    };
  };

  getParticipationStatus(verifyType: VerifyType) {
    switch (verifyType) {
      case VerifyType.MANUAL:
        return ParticipationStatus.IN_REVIEW;
      case VerifyType.AI:
        return ParticipationStatus.IN_AI_VERIFICATION;
      default:
        return ParticipationStatus.VALID;
    }
  }

  async participate<T>(
    eventId: string,
    taskId: string,
    userId: string,
    pipeline: TaskParticipationPipeline<T>,
    args: TaskParticipationExtraArgs = {},
  ) {
    const task = await this.taskService.findOne(taskId, {
      event: true,
      project: true,
    });
    if (!task)
      throw new BadRequestException(`No task found for taskId ${taskId}`);

    if (task.event.state !== EventState.ONGOING)
      throw new BadRequestException(AirLyftError.EVENT_NOT_ONGOING);

    await this.participationLock().acquire(userId, taskId);

    //Start participation pipeline
    try {
      const {
        getData,
        validators = [],
        processor,
        preProcessor,
        throwValidationError = true,
        getScore,
        postValidation,
        finalize,
      } = pipeline;
      const { authId, referralCode, hashedIp } = args;

      // 1. Get data
      let data = getData ? await getData(task) : null;

      // 2. Apply data pre-processing pipelines
      data = (await preProcessor?.(task, data)) || data;

      // 3. builders
      const query = buildTaskRulesToQuery(
        task,
        'task_participations',
        [],
        [taskParticipationRule, taskFrequencyRule],
        {
          hashedIp,
          ipProtect: !!task?.event?.ipProtect ? 'true' : undefined,
          userId,
          taskId,
          provider: authId?.provider,
          providerId: authId?.providerId,
        },
      );

      // 4. Apply validation pipeline
      const validationResult = [];
      for (const validate of validators) {
        const isOk = await validate(task, data);
        if (throwValidationError && !isOk) {
          throw new BadRequestException('Validation failed');
        }
        validationResult.push(isOk);
      }

      // 5. Apply data processing pipelines
      data = (await processor?.(task, data)) || data;

      // 6. Get score if there is any changes
      const { points, xp } = (await getScore?.(
        task,
        data,
        validationResult,
      )) || {
        points: task.points,
        xp: task.xp,
      };

      const participationStatus = this.getParticipationStatus(task.verify);

      // 7. Valid participation -- Save
      const { identifiers, taskParticipationData, newUser } = await this.save(
        eventId,
        userId,
        task,
        data,
        query,
        points,
        xp,
        participationStatus,
        { authId: !!authId ? authId : undefined, hashedIp, postValidation },
      );

      //8. Emit referral event in case user had referral header
      const payload: TaskParticipatedPayload = {
        referralCode,
        eventId,
        userId,
        taskId,
        taskType: task.taskType,
        projectId: task.event.projectId,
        points,
        xp,
        newUser,
        participationStatus,
      };

      this.eventEmitter.emit(TASK_PARTICIPATED, payload);

      await finalize?.(task, taskParticipationData, identifiers[0].id);

      await this.participationLock().release(userId, taskId);

      const webhookPayload: WebhookPayload = {
        userId,
        provider: authId?.provider,
        providerId: authId?.providerId,
        xp,
        points,
        data: JSON.stringify(taskParticipationData),
        taskId,
        eventId,
        tasktype: task.taskType,
        apptype: task.appType,
        participationStatus,
      };

      await this.webhookService.processWebhooks(
        eventId,
        task.event.project.webhookSecret,
        task.event.projectId,
        webhookPayload,
      );

      return { identifiers };
    } catch (err) {
      await this.participationLock().release(userId, taskId);
      throw err;
    }
  }

  private async save<T>(
    eventId: string,
    userId: string,
    task: Task,
    data: T,
    query: string,
    points: number,
    xp: number,
    participationStatus,
    extra: {
      authId?: AuthId;
      hashedIp?: string;
      postValidation?: (
        task: Task,
        data: T,
      ) => Promise<{ data: T; success: boolean }>;
    },
  ) {
    const { id: taskId } = task;

    return this.dataSource.transaction(async (transactionalEntityManager) => {
      const eventParticipant = await transactionalEntityManager.query(
        `INSERT INTO event_participants("eventId", "userId", mode) VALUES ($1, $2, $3) ON CONFLICT("eventId", "userId", mode) DO NOTHING RETURNING event_participants."userId"`,
        [eventId, userId, 1],
      );

      const taskParticipationResult: Array<{ id: string }> =
        await transactionalEntityManager.query(
          `INSERT INTO task_participations (points, xp,  "taskId", "userId", status, mode, "eventId", "provider", "providerId", "hashedIp") 
            SELECT $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
            ${query && ' WHERE ' + query} 
           RETURNING task_participations.id`,
          [
            points,
            xp,
            taskId,
            userId,
            participationStatus,
            1,
            eventId,
            extra?.authId?.provider,
            extra?.authId?.providerId,
            extra?.hashedIp,
          ],
        );

      if (
        !taskParticipationResult ||
        !Array.isArray(taskParticipationResult) ||
        taskParticipationResult.length == 0
      ) {
        throw new BadRequestException(
          'Could not verify. The same account/IP might have been used for verifying the same quest already.',
        );
      }

      const participationId = taskParticipationResult[0]?.id;

      const postResult = await extra?.postValidation?.(task, data);

      if (postResult && !postResult.success) {
        throw new BadRequestException('Rule validation failed');
      }

      const taskParticipationData = postResult?.data || data;

      if (taskParticipationData) {
        const entityData = this.dataRepository.merge(
          new TaskParticipationData(),
          {
            data: taskParticipationData,
            participation: { id: participationId },
          },
        );
        await transactionalEntityManager.insert(
          TaskParticipationData,
          entityData,
        );
      }

      return {
        identifiers: taskParticipationResult,
        newUser: Array.isArray(eventParticipant) && eventParticipant.length > 0,
        taskParticipationData,
      };
    });
  }

  getSubmissionsQb(where: SubmissionWhereInput) {
    const qb = this.dataSource
      .createQueryBuilder()
      .select([
        'task_participations."points"',
        'task_participations."xp"',
        'task_participations."id"',
        'task_participations."taskId"',
        'task_participations."userId"',
        'task_participations."status"',
        'task_participations."mode"',
        'task_participations."eventId"',
        'task_participations."createdAt"',
        'task_participations."updatedAt"',
      ])
      .addSelect('data."data"')
      .addSelect([
        'task."title"',
        'task."appType"',
        'task."taskType"',
        'task."eventId"',
      ])
      .addSelect([
        'auth."providerId"',
        'auth."email"',
        'auth."picture"',
        'auth."username"',
        'auth."provider"',
        'auth."isPrimary"',
      ])
      .addSelect('pauth."providerId"', 'pauth_providerId')
      .addSelect('pauth."email"', 'pauth_email')
      .addSelect('pauth."picture"', 'pauth_picture')
      .addSelect('pauth."username"', 'pauth_username')
      .addSelect('pauth."provider"', 'pauth_provider')
      .addSelect('pauth."isPrimary"', 'pauth_isPrimary')
      .from(TaskParticipation, 'task_participations')
      .leftJoin(
        TaskParticipationData,
        'data',
        'task_participations.id = data.participationId',
      )
      .leftJoin(Task, 'task', 'task_participations.taskId = task.id')
      .leftJoin(
        Auth,
        'auth',
        'task_participations.provider = auth.provider AND task_participations.providerId = auth.providerId',
      )
      .leftJoin(
        Auth,
        'pauth',
        'task_participations.userId = pauth.userId and pauth.isPrimary = true',
      )
      .where('task_participations.eventId = :eventId', {
        eventId: where.eventId,
      })
      .andWhere('task_participations.mode = :mode', { mode: 1 });

    if (where.userId) {
      qb.andWhere('task_participations.userId = :userId', {
        userId: where.userId,
      });
    }

    if (where.taskIds?.length > 0) {
      qb.andWhere('task_participations.taskId IN (:...taskIds)', {
        taskIds: where.taskIds,
      });
    }

    if (where.date?.start && where.date?.end) {
      qb.andWhere('task_participations.createdAt BETWEEN :start AND :end', {
        start: where.date.start,
        end: where.date.end,
      });
    }

    if (where?.status) {
      qb.andWhere('task_participations.status IN (:...status)', {
        status: where.status,
      });
    }

    return qb;
  }

  async getEventSubmissions(
    pagination: PaginationInput,
    where: SubmissionWhereInput,
    sorter?: SortInput,
  ): Promise<TaskSubmissionsData> {
    const qb = this.getSubmissionsQb(where);

    const [taskParticipationQuery, params] = qb.getQueryAndParameters();

    const taskParticipationData = await this.dataSource.query(
      `WITH cte AS (
        ${taskParticipationQuery}
      )
      SELECT * FROM (
        SELECT * FROM cte
        ORDER BY "createdAt", id
        OFFSET ${pagination.skip} LIMIT ${pagination.take}
      ) result
      RIGHT JOIN (
        SELECT COUNT(*)::numeric FROM cte
      ) c(total) ON TRUE`,
      params,
    );

    const total = taskParticipationData?.[0]?.total || 0;

    const data =
      total === 0
        ? []
        : taskParticipationData.map((participation) => {
            const {
              id,
              points,
              xp,
              taskId,
              userId,
              status,
              mode,
              eventId,
              createdAt,
              data,
              title,
              appType,
              taskType,
              providerId,
              email,
              picture,
              username,
              provider,
              isPrimary,
            } = participation;
            return {
              id,
              points,
              xp,
              taskId,
              userId,
              status,
              mode,
              eventId,
              createdAt,
              data: { data },
              task: {
                title,
                appType,
                taskType,
                eventId,
              },
              primaryAuth: {
                providerId: participation.pauth_providerId,
                email: participation.pauth_email,
                picture: participation.pauth_picture,
                username: participation.pauth_username,
                provider: participation.pauth_provider,
                userId: participation.userId,
                isPrimary: participation.pauth_isPrimary,
              },
              auth: participation.providerId
                ? {
                    providerId,
                    email,
                    picture,
                    username,
                    provider,
                    userId,
                    isPrimary,
                  }
                : null,
            };
          });

    return {
      total,
      data,
    };
  }

  async updateParticipationData(
    participationId: string,
    newData: any,
  ): Promise<TaskParticipationData> {
    const existingData = await this.dataRepository.findOne({
      where: { participationId },
    });

    let dataToUpdate: TaskParticipationData;
    if (!existingData) {
      dataToUpdate = new TaskParticipationData();
      dataToUpdate.participationId = participationId;
    } else {
      dataToUpdate = existingData;
    }
    const mergedData = { ...dataToUpdate.data, ...newData };
    dataToUpdate.data = mergedData;
    return this.dataRepository.save(dataToUpdate);
  }

  getEventParticipantsQb(where: ParticipantWhereInput) {
    return this.xpService.getEventParticipantsQb(where);
  }

  async getEventParticipants(
    pagination: PaginationInput,
    where: ParticipantWhereInput,
    sorter: SortInput,
  ): Promise<ParticipantsData> {
    const participants = await this.xpService.getEventParticipants(
      pagination,
      where,
      sorter,
    );
    const formattedParticipants: Participant[] = participants?.map((i) => ({
      userId: i.userId,
      totalTask: i.totalTask,
      totalPoints: i.totalPoints,
      isChosen: i.isChosen || false,
      totalXp: i.totalXp,
      displayName: `${i.fname} ${i.lname}`,
      primaryAuth: {
        providerId: i.providerId,
        email: i.email,
        picture: i.picture,
        username: i.username,
        provider: i.provider,
        userId: i.userId,
      },
    }));

    return {
      total: participants?.[0]?.total || 0,
      data: formattedParticipants || [],
    };
  }

  findTaskParticipationsByTelegramUser(
    telegramUserId: number,
    taskId?: string,
  ) {
    const qb = this.repository
      .createQueryBuilder('tp')
      .select(['tp.id', 'tp.taskId', 'tp.createdAt', 'tp.status'])
      .innerJoin(Auth, 'a', 'a.userId = tp.userId')
      .where('a.provider = :provider', { provider: AuthProvider.TELEGRAM })
      .andWhere('a.providerId = :providerId', { providerId: telegramUserId });

    if (taskId) {
      qb.andWhere('tp."taskId" = :taskId', { taskId });
      qb.orderBy('tp."taskId"', 'DESC');
    }

    return qb.getMany();
  }

  async countEventParticipants(
    eventId: string,
    startDate?: Date,
    endDate?: Date,
  ) {
    if (!startDate || !endDate)
      return this.repository
        .createQueryBuilder()
        .select('count( DISTINCT "userId" )')
        .andWhere('"eventId" = :eventId', {
          eventId,
        })
        .getRawOne();
    else {
      const diffTime =
        new Date(endDate).getTime() - new Date(startDate).getTime();
      const diffDays = Math.round(diffTime / (1000 * 3600 * 24));

      return this.dataSource
        .createQueryBuilder()
        .select('d.date', 'date')
        .addSelect('COALESCE(subq.count, 0)', 'count')
        .from(
          `(SELECT CURRENT_DATE - i AS date FROM generate_series(0, ${diffDays}) AS i)`,
          'd',
        )
        .leftJoin(
          (qb) =>
            qb
              .select('DATE("createdAt")', 'date')
              .addSelect('COUNT(DISTINCT "userId")', 'count')
              .from('task_participations', 'TaskParticipation')
              .where('"eventId" = :eventId', { eventId })
              .andWhere('"createdAt" >= :startDate', { startDate })
              .andWhere('"createdAt" <= :endDate', { endDate })
              .groupBy('DATE("createdAt")'),
          'subq',
          'd.date = subq.date',
        )
        .orderBy('d.date', 'ASC')
        .getRawMany();
    }
  }

  async countEventTaskParticipants(
    eventId: string,
    startDate: Date,
    endDate: Date,
  ) {
    return this.repository
      .createQueryBuilder()
      .select('count( DISTINCT "userId" )', 'count')
      .addSelect('t."taskType"', 'dimension')
      .innerJoin(Task, 't', 'TaskParticipation.taskId = t.id')
      .andWhere('"TaskParticipation"."eventId" = :eventId', {
        eventId,
      })
      .andWhere('"TaskParticipation"."createdAt" >= :startDate', { startDate })
      .andWhere('"TaskParticipation"."createdAt" <= :endDate', { endDate })
      .groupBy('"taskId", "taskType"')
      .getRawMany();
  }

  async getParticipationsInAIVerification(limit: number) {
    return this.repository
      .createQueryBuilder('task_participations')
      .leftJoinAndSelect('task_participations.data', 'data')
      .leftJoinAndSelect('task_participations.task', 'task')
      .leftJoinAndSelect('task.data', 'taskData')
      .where('task_participations.status = :status', {
        status: ParticipationStatus.IN_AI_VERIFICATION,
      })
      .orderBy('task_participations.createdAt', 'ASC')
      .limit(limit)
      .getMany();
  }
}
