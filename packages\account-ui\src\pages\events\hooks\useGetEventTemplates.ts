import { gql, useQuery } from '@apollo/client';
import {
  Ecosystem,
  EventTemplate,
  PaginationInput,
  Query_ecosystemTemplatesArgs,
  Query_globalTemplatesArgs,
} from '@airlyft/types';

const GET_GLOBAL_EVENT_TEMPLATES = gql`
  query GlobalTemplates($pagination: PaginationInput) {
    globalTemplates(pagination: $pagination) {
      id
      name
      description
      bannerUrl
      tags
    }
  }
`;

export const GET_ECOSYSTEM_EVENT_TEMPLATES = gql`
  query EcosystemTemplates(
    $ecosystem: Ecosystem!
    $projectId: ID!
    $pagination: PaginationInput
  ) {
    ecosystemTemplates(
      ecosystem: $ecosystem
      projectId: $projectId
      pagination: $pagination
    ) {
      id
      name
      description
      bannerUrl
      tags
      eventId
      displayType
      ecosystem
      onboarding {
        projectId
        eventTemplateId
      }
    }
  }
`;

export function useGetGlobalTemplates(pagination?: PaginationInput) {
  const defaultPagination = pagination || { skip: 0, take: 12 };
  return useQuery<
    { globalTemplates: Array<EventTemplate> },
    Query_globalTemplatesArgs
  >(GET_GLOBAL_EVENT_TEMPLATES, {
    variables: {
      pagination: defaultPagination,
    },
  });
}

export function useGetEcosystemTemplates(
  ecosystem?: Ecosystem,
  pagination?: PaginationInput,
  projectId?: string,
) {
  const defaultPagination = pagination || { skip: 0, take: 12 };
  return useQuery<
    { ecosystemTemplates: Array<EventTemplate> },
    Query_ecosystemTemplatesArgs
  >(GET_ECOSYSTEM_EVENT_TEMPLATES, {
    variables: {
      ecosystem: ecosystem!,
      projectId: projectId!,
      pagination: defaultPagination,
    },
    skip: !ecosystem,
  });
}
