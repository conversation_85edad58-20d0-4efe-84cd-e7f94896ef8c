import { BadRequestException, Injectable } from '@nestjs/common';
import '@polkadot/api-augment';
import { BN } from '@polkadot/util';
import { DotsamaClientService } from './dotsama-client.service';

@Injectable()
export class DotsamaService {
  constructor(private readonly dotsamaClientService: DotsamaClientService) {}

  transfer(
    blockchainId: string,
    userId: string,
    toAddress: string,
    amount: string,
  ) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const { data } = await api.query.system.account(user.address);
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        const balance = data?.free ?? new BN('0');

        const ed = api.consts.balances.existentialDeposit;
        const transfer = api.tx.balances.transferKeepAlive(toAddress, amount);
        const info = await transfer.paymentInfo(user.address);
        const maxAllowed = balance.sub(ed).sub(info.partialFee);

        if (!balance || maxAllowed.lt(new BN(amount))) {
          throw new BadRequestException(
            'Insufficient balance. Please contact the community host for assistance.',
          );
        }

        return transfer.signAndSend(user, { nonce });
      },
      userId,
    );
  }

  transferAllowDeath(
    blockchainId: string,
    userId: string,
    toAddress: string,
    amount: string,
  ) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const { data } = await api.query.system.account(user.address);
        const nonce = await api.rpc.system.accountNextIndex(user.address);
        const balance = data?.free;

        const transfer = api.tx.balances.transferAllowDeath(toAddress, amount);
        const info = await transfer.paymentInfo(user.address);
        const maxAllowed = balance.sub(info.partialFee);

        if (!balance || balance.lte(new BN('0'))) {
          throw new BadRequestException(
            'Insufficient balance. Please contact the community host for assistance.',
          );
        }

        if (maxAllowed.lt(new BN(amount))) {
          const tx = api.tx.balances.transferAll(toAddress, false);
          return tx.signAndSend(user, { nonce });
        }

        return transfer.signAndSend(user, { nonce });
      },
      userId,
    );
  }

  balance({ blockchainId, userId }: { blockchainId: string; userId: string }) {
    return this.dotsamaClientService.withClient(
      blockchainId,
      async ({ api, user }) => {
        const { data: balance } = await api.query.system.account(user.address);

        return balance?.free?.toString() ?? '0';
      },
      userId,
    );
  }
}
