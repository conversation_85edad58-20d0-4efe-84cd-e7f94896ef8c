import { Task, TaskRuleType, TaskType } from '@airlyft/types';

import {
  ArrowSquareOut,
  CheckCircle,
  Clock,
  Lock,
  Users,
} from '@phosphor-icons/react';
import { useRouter } from 'next/router';
import { Fragment } from 'react';
import useAppSelector from '../app-store';
import { selectAppByType, selectTaskByType } from '../app-store.helper';
import { ExtendedTaskRule } from '../hooks/useGetTasks';
import AppStoreIconRenderer from './AppStoreIconRenderer';
import { cn } from '@Root/utils/utils';

function TaskIcon({ task }: { task: Task }) {
  const { appType, taskType } = task;
  const appKey = task.appKey as string | undefined;
  const taskKey = task.taskKey as string | undefined;
  const app = useAppSelector(selectAppByType(appType, appKey));
  const appTask = useAppSelector(
    selectTaskByType(appType, taskType, { appKey, taskKey }),
  );

  return task.iconUrl ? (
    <div className="w-6 h-6 relative flex items-center justify-center">
      <img
        src={task.iconUrl}
        alt="Custom Task Icon"
        className="w-full h-full object-cover rounded"
      />
    </div>
  ) : (
    <AppStoreIconRenderer
      iconKey={taskType}
      className="h-6 w-6"
      color={appTask?.config?.color || app?.config.color}
      url={appTask?.config?.iconUrl || app?.config.iconUrl}
    />
  );
}

export default function TaskRulesList({
  rules,
}: {
  rules: ExtendedTaskRule[];
}) {
  const router = useRouter();
  const iconTpl = (eRule: ExtendedTaskRule) => {
    switch (eRule.rule.ruleType) {
      case TaskRuleType.TASK_ID:
        return eRule.task ? <TaskIcon task={eRule.task} /> : <Fragment />;
      case TaskRuleType.DATE:
        return <Clock size={24} />;
      case TaskRuleType.MAX_PARTICIPANTS:
        return <Users size={24} />;
      default:
        return <Lock size={24} />;
    }
  };

  return (
    <div className="space-y-2 mt-4 w-full">
      {(rules || []).map((eRule, key) => (
        <div
          className={cn(
            `flex items-center space-x-2 p-2 px-3 rounded-lg component-bg border border-foreground/10`,
            eRule.completed ? 'border-primary' : '',
            eRule.task && eRule.task.taskType !== TaskType.CHECKIN_DAILY
              ? 'cursor-pointer component-bg-hover'
              : '',
          )}
          key={key}
          onClick={() => {
            if (!eRule.task) return;
            router.query['taskid'] = eRule.task.id;
            router.push(router, undefined, { shallow: true });
          }}
        >
          <div>{iconTpl(eRule)}</div>
          <div className="text-left flex gap-0 flex-col flex-1">
            <div className="text-xs text-cs font-semibold">{eRule.title}</div>
            <div className="space-x-1 flex items-center">
              <div className="text-base font-medium truncate max-w-[220px] text-cs">
                {eRule.description}
              </div>
              {eRule.task && eRule.task.taskType !== TaskType.CHECKIN_DAILY && (
                <ArrowSquareOut className="h-4" weight="light" />
              )}
            </div>
          </div>
          {eRule.warningText && (
            <div className="text-xs text-ch rounded font-semibold bg-red-500/20 border border-red-500 py-0.5 px-2">
              {eRule.warningText}
            </div>
          )}
          {eRule.completed && (
            <CheckCircle className="h-6 w-6 text-primary" weight="fill" />
          )}
        </div>
      ))}
    </div>
  );
}
