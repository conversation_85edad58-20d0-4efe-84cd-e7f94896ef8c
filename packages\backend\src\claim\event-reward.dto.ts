import { Field, Int, ObjectType } from '@nestjs/graphql';
import { Giveaway } from '@root/giveaway/entities/giveaway.entity';
import { RewardStatus } from './event-reward.entity';

@ObjectType()
export class EventRewardData {
  @Field(() => Giveaway)
  giveaway: Giveaway;

  @Field()
  giveawayId: string;

  @Field((type) => RewardStatus)
  status: RewardStatus;
}

@ObjectType()
export class EventRewardsResponse {
  @Field(() => [EventRewardData])
  data: EventRewardData[];

  @Field(() => Int)
  total: number;
}
