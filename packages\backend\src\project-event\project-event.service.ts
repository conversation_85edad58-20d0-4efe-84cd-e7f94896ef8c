import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MODES } from '@root/constants';
import { BaseCrudService } from '@root/core/base-crud/base-crud.service';
import { InsertResult } from '@root/core/dto/insert-result.dto';
import { PaginationInput } from '@root/core/dto/pagination.dto';
import { UpdateResult } from '@root/core/dto/update-result.dto';
import { EventTemplate } from '@root/event-template/event-template.entity';
import {
  Project,
  ProjectUrl,
  ProjectUrlType,
} from '@root/project/project.entity';
import { Referral } from '@root/referral/entities/referral.entity';
import {
  AppType,
  Frequency,
  TaskRuleType,
  TaskType,
  VerifyType,
} from '@root/task/task.constants';
import { Task } from '@root/task/task.entity';
import { addDays, isFuture } from 'date-fns';
import { DataSource, EntityManager, In, Not, Repository } from 'typeorm';
import {
  ProjectEventInput,
  ProjectEventSortInput,
  ProjectEventSortKey,
  ProjectEventUpdateInput,
  ProjectEventWhereInput,
  PublicProjectEventWhereInput,
} from './project-event.dto';
import {
  EventState,
  EventType,
  EventVisibility,
  ProjectEvent,
} from './project-event.entity';

import { AirquestFollowTaskData } from '@root/apps/airquest/tasks/follow/airquest-follow-task.dto';
import { PubSubService } from '@root/core/services/pub-sub.service';
import { TaskService } from '@root/task/task.service';
import { ProjectEventSummary } from './project-event-summary.entity';

const DEFAULT_QUESTS = 'default-quests';
const RESERVED_EVENT_URLS = [DEFAULT_QUESTS];
@Injectable()
export class ProjectEventService extends BaseCrudService<ProjectEvent> {
  constructor(
    @InjectRepository(ProjectEvent)
    repository: Repository<ProjectEvent>,

    @InjectRepository(Task)
    private taskRepository: Repository<Task>,

    @InjectRepository(Task)
    private projectEventSummaryRepository: Repository<ProjectEventSummary>,

    private dataSource: DataSource,

    private taskService: TaskService,

    private readonly pubsubService: PubSubService,
  ) {
    super(repository);
  }

  findById(id: string) {
    return this.repository.findOne({ where: { id } });
  }

  updateSummary(
    eventId: string,
    summary: Partial<ProjectEventSummary>,
    transactionalEntityManager?: EntityManager,
  ) {
    const updateQueryBuilder = (
      field: string,
      valuePosition: number,
      value: number,
      end?: boolean,
    ) => {
      if (value === 0) {
        if (end)
          return `"${field}" = project_event_summaries."${field}" + $${valuePosition}`;

        return '';
      }

      if (value < 0) {
        return `"${field}" = (
          CASE WHEN project_event_summaries."${field}" > 0
          THEN (project_event_summaries."${field}" + $${valuePosition}) 
          ELSE 0 END
        )${end ? '' : ','}`;
      }

      return `"${field}" = project_event_summaries."${field}" + $${valuePosition}${
        end ? '' : ','
      }`;
    };

    const repo = transactionalEntityManager
      ? transactionalEntityManager.getRepository(ProjectEventSummary)
      : this.projectEventSummaryRepository;

    return repo.query(
      `
        INSERT INTO project_event_summaries ("eventId" , "totalTasks" , "totalParticipants" , "totalTaskParticipation", "totalXP", "totalPoints", "totalPointsEarned" )
        VALUES($1, $2, $3, $4, $5, $6, $7) 
        ON CONFLICT ("eventId") 
        DO UPDATE SET ${updateQueryBuilder(
          'totalTasks',
          2,
          summary.totalTasks || 0,
        )} ${updateQueryBuilder(
        'totalParticipants',
        3,
        summary.totalParticipants || 0,
      )} ${updateQueryBuilder(
        'totalTaskParticipation',
        4,
        summary.totalTaskParticipation || 0,
      )} ${updateQueryBuilder(
        'totalXP',
        5,
        summary.totalXP || 0,
      )} ${updateQueryBuilder(
        'totalPoints',
        6,
        summary.totalPoints || 0,
      )} ${updateQueryBuilder(
        'totalPointsEarned',
        7,
        summary.totalPointsEarned || 0,
        true,
      )};
        `,
      [
        eventId,
        summary.totalTasks || 0,
        summary.totalParticipants || 0,
        summary.totalTaskParticipation || 0,
        summary.totalXP || 0,
        summary.totalPoints || 0,
        summary.totalPointsEarned || 0,
      ],
    );
  }

  search(
    pagination: PaginationInput,
    where?: ProjectEventWhereInput | PublicProjectEventWhereInput,
    sorter?: ProjectEventSortInput,
  ): Promise<[ProjectEvent[], number]> {
    const eventType = where?.eventType ?? EventType.CAMPAIGN;
    const qb = this.repository
      .createQueryBuilder('projectEvent')
      .where('projectEvent.eventType = :eventType', {
        eventType: eventType,
      });

    if (
      where?.state.length === 1 &&
      (where?.state[0] as EventState) === EventState.ONGOING
    ) {
      //If returning ONGOING events then also check endTime
      //FIXME: Need proper fix for this
      qb.andWhere('projectEvent.state = ANY(:state)', {
        state: where?.state,
      }).andWhere(
        '(projectEvent.endTime IS NULL OR projectEvent.endTime > :now)',
        {
          now: new Date(),
        },
      );
    } else {
      qb.andWhere('projectEvent.state = ANY(:state)', {
        state: where?.state,
      });
    }

    qb.leftJoinAndSelect(
      'projectEvent.giveawayEvents',
      'giveawayEvents',
    ).leftJoinAndSelect('giveawayEvents.giveaway', 'giveaway');

    if (where.projectId) {
      qb.andWhere('projectEvent.projectId = :projectId', {
        projectId: where.projectId,
      });
    }

    if (where.visibility) {
      qb.andWhere('projectEvent.visibility = ANY(:visibility)', {
        visibility: where.visibility,
      });
    }

    if (where?.ecosystems?.length > 0) {
      qb.andWhere('project.ecosystems && :ecosystems', {
        ecosystems: where.ecosystems,
      });
    }

    if (where?.tags?.length > 0) {
      qb.andWhere('projectEvent.tags && :tags', {
        tags: where.tags,
      });
    }

    qb.leftJoinAndSelect('projectEvent.project', 'project');

    if (sorter?.sortKey === ProjectEventSortKey.TRENDING) {
      //Since sorting by summary.totalTaskParticipation,
      //Select only those events that have summary.
      //If migration is proper then it would be same as left join.
      qb.innerJoinAndSelect('projectEvent.summary', 'summary');
      qb.orderBy(
        'summary.totalTaskParticipation',
        sorter?.direction ? sorter?.direction : 'DESC',
      );
    } else {
      qb.leftJoinAndSelect('projectEvent.summary', 'summary');
      qb.orderBy(
        sorter?.sortKey
          ? `projectEvent.${sorter?.sortKey}`
          : 'projectEvent.createdAt',
        sorter?.direction ? sorter?.direction : 'DESC',
      );
    }

    return qb.take(pagination.take).skip(pagination.skip).getManyAndCount();
  }

  async createOrReturnDefaultEvent(projectId: string) {
    //Return if exists
    const defaultEvent = await this.repository
      .createQueryBuilder('projectEvent')

      .where('projectEvent.projectId = :projectId', { projectId: projectId })
      .andWhere('projectEvent.eventType = :eventType', {
        eventType: EventType.DEFAULT_QUEST,
      })
      .leftJoinAndSelect('projectEvent.summary', 'summary')
      .getOne();

    if (defaultEvent) {
      return defaultEvent;
    }

    //If doesn't exist, then create & return
    const createdEventId = await this.dataSource.transaction(
      async (transactionalEntityManager) => {
        return await this.createDefaultProjectEvent(
          projectId,
          transactionalEntityManager,
        );
      },
    );

    return this.repository.findOne({
      where: {
        id: createdEventId,
      },
      relations: {
        project: true,
        summary: true,
      },
    });
  }

  async findDefaultByLink(projectPublicLink: string) {
    const defaultEvent = await this.repository
      .createQueryBuilder('projectEvent')
      .leftJoinAndSelect('projectEvent.project', 'project')
      .leftJoinAndSelect('projectEvent.summary', 'projectSummary')
      .where('projectEvent.eventType = :eventType', {
        eventType: EventType.DEFAULT_QUEST,
      })
      .andWhere('project.publicLink = :projectPublicLink', {
        projectPublicLink,
      })
      .getOne();
    if (!!defaultEvent) {
      return defaultEvent;
    }
    const createdEventId = await this.dataSource.transaction(
      async (transactionalEntityManager) => {
        const project = await transactionalEntityManager.findOne(Project, {
          where: {
            publicLink: projectPublicLink,
          },
        });
        return await this.createDefaultProjectEvent(
          project.id,
          transactionalEntityManager,
        );
      },
    );
    return this.repository.findOne({
      where: {
        id: createdEventId,
      },
      relations: {
        project: true,
        summary: true,
      },
    });
  }

  findByLink(projectPublicLink: string, publicLink: string) {
    return this.repository
      .createQueryBuilder('projectEvent')
      .leftJoinAndSelect('projectEvent.project', 'project')
      .leftJoinAndSelect('projectEvent.giveawayEvents', 'giveawayEvents')
      .leftJoinAndSelect('giveawayEvents.giveaway', 'giveaway')
      .leftJoinAndSelect('projectEvent.summary', 'projectSummary')
      .where('projectEvent.publicLink = :publicLink', { publicLink })
      .andWhere('project.publicLink = :projectPublicLink', {
        projectPublicLink,
      })
      .getOne();
  }

  createProjectEvent(
    projectId: string,
    data: ProjectEventInput,
  ): Promise<InsertResult> {
    if (
      data.publicLink &&
      RESERVED_EVENT_URLS.includes(data.publicLink.toLowerCase())
    ) {
      throw new BadRequestException('Event URL is reserved');
    }
    const entity = this.repository.merge(new ProjectEvent(), {
      ...data,
      projectId,
    });
    return this.repository.insert(entity);
  }

  async createDefaultProjectEvent(
    projectId: string,
    transactionalEntityManager: EntityManager,
  ): Promise<string> {
    const entity = this.repository.merge(new ProjectEvent(), {
      title: 'Quests',
      publicLink: `default-quests`,
      startTime: new Date(),
      eventType: EventType.DEFAULT_QUEST,
      state: EventState.ONGOING,
      visibility: EventVisibility.PRIVATE,
      projectId,
    });
    const result = await transactionalEntityManager
      .createQueryBuilder()
      .insert()
      .into(ProjectEvent)
      .values({
        ...entity,
      })
      .execute();

    const projectEventId = result.identifiers[0]?.id;
    await this.addDefaultCheckinTask(
      projectId,
      projectEventId,
      transactionalEntityManager,
    );
    return projectEventId;
  }

  async addDefaultCheckinTask(
    projectId: string,
    eventId: string,
    transactionalEntityManager: EntityManager,
  ): Promise<InsertResult> {
    const entity = this.taskRepository.merge(new Task(), {
      order: 1,
      taskType: TaskType.CHECKIN_DAILY,
      appType: AppType.CHECKIN,
      points: 1,
      title: 'Daily checkin',
      verify: VerifyType.AUTO,
      description: 'checkin daily to earn XP',
      frequency: Frequency.DAY,
      projectId,
      event: { id: eventId },
    });
    const result = await transactionalEntityManager
      .createQueryBuilder()
      .insert()
      .into(Task)
      .values({
        ...entity,
      })
      .execute();
    return result;
  }

  async updateProjectEvent(
    projectId: string,
    id: string,
    data: ProjectEventUpdateInput,
  ): Promise<UpdateResult> {
    if (
      data.publicLink &&
      RESERVED_EVENT_URLS.includes(data.publicLink.toLowerCase())
    ) {
      throw new BadRequestException('Event URL is reserved');
    }
    try {
      let updateData: ProjectEventUpdateInput & { state?: EventState } = data;

      if (data.startTime && isFuture(data.startTime)) {
        const event = await this.repository.findOne({
          where: {
            id,
            projectId,
          },
        });
        if (event.state === EventState.ONGOING) {
          updateData = {
            ...updateData,
            state: EventState.SCHEDULED,
          };
        }
      }

      return await this.repository.update({ id, projectId }, updateData);
    } catch (e) {
      if (e.code == 23505)
        throw new BadRequestException('Event URL already exists');
      else
        throw new BadRequestException('Error updating project event details');
    }
  }

  deleteProjectEvent(eventId: string, projectId: string) {
    return this.repository.delete({
      id: eventId,
      state: In([EventState.DRAFT, EventState.SCHEDULED]),
      project: { id: projectId },
    });
  }

  async isEventUrlAvailable(
    projectId: string,
    publicLink: string,
    eventId?: string,
  ): Promise<boolean> {
    if (RESERVED_EVENT_URLS.includes(publicLink.toLowerCase())) {
      return false;
    }
    return !Boolean(
      await this.repository.countBy({
        publicLink: publicLink.toLowerCase(),
        project: { id: projectId },
        id: Not(eventId || '00000000-0000-0000-0000-000000000000'),
      }),
    );
  }

  async isEventEnded(eventId: string) {
    const result = await this.repository
      .createQueryBuilder()
      .select('id')
      .where('id = :eventId', { eventId })
      .andWhere('"endTime" < now()')
      .getRawOne();

    if (result) return true;
    return false;
  }

  async convertToTemplate(projectId: string, eventId: string, userId: string) {
    return this.dataSource.transaction(async (transactionManager) => {
      const projectEventRepo = transactionManager.getRepository(ProjectEvent);
      const event = await projectEventRepo.findOne({
        where: { id: eventId, project: { id: projectId } },
      });

      if (!event) {
        throw new BadRequestException('Invalid projectId or eventId');
      }

      const eventTemplateRepo = transactionManager.getRepository(EventTemplate);

      const eventTemplateEntity = eventTemplateRepo.merge(new EventTemplate(), {
        bannerUrl: event.bannerUrl,
        name: event.title,
        owner: { id: userId },
        event: { id: eventId },
      });
      await eventTemplateRepo.insert(eventTemplateEntity);

      return projectEventRepo.update(
        { id: eventId },
        { mode: MODES.USER_TEMPLATE.mode },
      );
    });
  }

  async copyEvent(oldEvent: ProjectEvent, projectId: string, titlePrefix = '') {
    const mapOldNewTaskIds: Map<string, string> = new Map();
    // 2. Duplicate project event
    const newEventEntity = this.repository.merge(new ProjectEvent(), {
      title: titlePrefix + oldEvent.title,
      description: oldEvent.description,
      bannerUrl: oldEvent.bannerUrl,
      startTime: new Date(),
      endTime: addDays(new Date(), 10),
      visibility: EventVisibility.PRIVATE,
      projectId,
      publicLink: `${oldEvent.title
        .toLowerCase()
        .trim()
        .replace(/[&\/\\#,+()$~%.'":*?<>{}]/g, '')
        .replace(/\s+/g, '-')}-${Date.now()}`,
    });

    const newEvent = await this.repository.insert(newEventEntity);
    const newEventId = newEvent.identifiers?.[0].id;

    //Now start transaction since event needs to be comitted before hand.
    return this.dataSource.transaction(async (transactionManager) => {
      // 3. duplicate tasks and task data
      const insertTaskAndData = async (task: Task, parentId?: string) => {
        const newTaskId = await this.taskService.create(
          projectId,
          newEventId,
          {
            points: task.points,
            title: task.title,
            verify: task.verify,
            description: task.description,
            frequency: task.frequency,
            hidden: task.hidden,
            parentId: parentId,
            xp: task.xp,
            data: task.data?.data,
            integrations: task.integrations?.map((integration) => ({
              integrationId: integration.integrationId,
              ...(integration?.routeId && { routeId: integration?.routeId }),
            })),
            guardConfig: undefined,
          },
          task.appType,
          task.taskType,
          transactionManager,
        );

        const childTasks = oldEvent.taskList?.filter(
          (t) => t.parentId === task.id,
        );

        for (const childTask of childTasks) {
          await insertTaskAndData(childTask, newTaskId);
        }

        const referralRepo = transactionManager.getRepository(Referral);
        const referral = await referralRepo.findOne({
          where: {
            event: { id: oldEvent.id },
          },
        });

        //In case the template has a referral task then create a new referral entity connected to new event
        if (referral && task.id === referral.taskId) {
          const newReferral = referralRepo.merge(new Referral(), {
            task: {
              id: newTaskId,
            },
            event: {
              id: newEventId,
            },
          });
          await referralRepo.insert(newReferral);
        }

        return newTaskId;
      };

      const topLevelTasks = oldEvent.taskList?.filter((task) => !task.parentId);

      for (const task of topLevelTasks) {
        const newTaskId = await insertTaskAndData(task);
        mapOldNewTaskIds.set(task.id, newTaskId);
      }
      for (const task of topLevelTasks) {
        if (task.guardConfig?.rules) {
          const newGuardRule = task?.guardConfig?.rules.map((rule) => {
            if (rule.ruleType == TaskRuleType.TASK_ID) {
              return {
                ...rule,
                stringValue: mapOldNewTaskIds.get(rule.stringValue),
              };
            }
            return rule;
          });
          // Update tasks which has guradConfig & Integrations
          await this.taskService.update(
            projectId,
            newEventId,
            mapOldNewTaskIds.get(task.id),
            {
              guardConfig: {
                condition: task.guardConfig.condition,
                rules: newGuardRule,
              },
            },
            true,
            transactionManager,
          );
        }
      }

      return newEvent;
    });
  }

  async cloneEvent(eventId: string, projectId: string) {
    const oldEvent = await this.repository
      .createQueryBuilder('event')
      .leftJoinAndSelect('event.taskList', 'taskList')
      .leftJoinAndSelect('taskList.data', 'taskData')
      .leftJoinAndSelect('taskList.integrations', 'taskIntegrations')
      .where('event.id = :eventId', { eventId })
      .andWhere(`"taskList"."taskType" != 'QUIZ_PLAY'`)
      .orderBy('"taskList"."order"', 'ASC')
      .getOne();

    return this.copyEvent(oldEvent, projectId, '(Copy) ');
  }

  async createFromTemplate(templateId: string, projectId: string) {
    // 1.1 Get event associated with the template
    const templateEvent = await this.repository
      .createQueryBuilder('event')
      .innerJoin('event.eventTemplate', 'template')
      .leftJoinAndSelect('event.taskList', 'taskList')
      .leftJoinAndSelect('taskList.data', 'taskData')
      .leftJoinAndSelect('taskList.integrations', 'taskIntegrations')
      .where('template.id = :templateId', { templateId })
      .orderBy('"taskList"."order"', 'ASC')
      .getOne();

    // 1.2 Get project urls, if any
    const urls = await this.dataSource.getRepository(ProjectUrl).find({
      where: {
        project: { id: projectId },
        urlType: In([
          ProjectUrlType.DISCORD,
          ProjectUrlType.TWITTER,
          ProjectUrlType.TELEGRAM,
        ]),
      },
    });

    const project = await this.dataSource.getRepository(Project).findOne({
      where: {
        id: projectId,
      },
    });

    //1.3 Update tasks in the template with current project data if possible
    const mapping = {
      [TaskType.TWITTER_FOLLOW]: {
        dataMapping: () => {
          const urlObject = urls.find(
            (url) => url.urlType === ProjectUrlType.TWITTER && !!url.url,
          );
          return urlObject
            ? {
                url: urlObject.url,
              }
            : null;
        },
      },
      [TaskType.TELEGRAM_JOIN]: {
        dataMapping: () => {
          const urlObject = urls.find(
            (url) =>
              url.urlType === ProjectUrlType.TELEGRAM &&
              url.data &&
              url.data.chatId &&
              url.data.username &&
              url.data.type &&
              url.data.title,
          );
          return urlObject
            ? {
                chatId: urlObject.data.chatId,
                username: urlObject.data.username,
                type: urlObject.data.type,
                title: urlObject.data.title,
              }
            : null;
        },
      },
      [TaskType.DISCORD_JOIN]: {
        dataMapping: () => {
          const urlObject = urls.find(
            (url) =>
              url.urlType === ProjectUrlType.DISCORD &&
              url.data &&
              url.data.guildName &&
              url.data.guildId,
          );
          return urlObject
            ? {
                url: urlObject.url,
                guildId: urlObject.data.guildId,
                guildName: urlObject.data.guildName,
              }
            : null;
        },
      },
      [TaskType.AIRQUEST_FOLLOW]: {
        dataMapping: (task: Task<AirquestFollowTaskData>) => {
          if (!task.data?.data?.url) return null;

          const url = new URL(task.data.data.url);
          url.pathname = project.publicLink;

          return {
            projectId,
            projectName: project.name,
            url: url.toString(),
          };
        },
      },
    };

    const newTaskList = templateEvent?.taskList?.map((task) => {
      const dataMapping = mapping[task.taskType]?.dataMapping;
      if (!dataMapping) return task;

      const data = dataMapping(task);
      if (!data) return task;

      return {
        ...task,
        data: {
          ...task.data,
          data,
        },
      };
    });

    return this.copyEvent(
      { ...templateEvent, taskList: newTaskList } as ProjectEvent,
      projectId,
    );
  }

  publishEvent(projectId: string, eventId: string, announce?: boolean) {
    try {
      let setObj: any = {
        state: () =>
          `CASE
          WHEN "startTime" < now() 
          THEN '${EventState.ONGOING}'::project_events_state_enum
          ELSE '${EventState.SCHEDULED}'::project_events_state_enum
        END`,
      };
      if (announce) {
        setObj = { ...setObj, visibility: EventVisibility.PUBLIC_EXPLORABLE };
      }
      const eventObj = this.repository
        .createQueryBuilder()
        .update()
        .set(setObj)
        .where('id = :eventId', { eventId })
        .andWhere('projectId = :projectId', { projectId })
        .execute();

      if (announce) {
        this.pubsubService.publishNewProjectEvent(projectId, eventId);
      }

      return eventObj;
    } catch (err) {
      throw new BadRequestException('Error publishing event');
    }
  }

  publishScheduledEvents() {
    return this.repository
      .createQueryBuilder()
      .update()
      .set({ state: EventState.ONGOING })
      .where('state= :state', { state: EventState.SCHEDULED })
      .andWhere('startTime < now()')
      .execute();
  }

  publishedEvents(projectId?: string) {
    const qb = this.repository
      .createQueryBuilder('projectEvent')
      .where('projectEvent.state = ANY(:state)', {
        state: [
          EventState.SCHEDULED,
          EventState.ONGOING,
          EventState.READY_TO_SETTLE,
          EventState.SETTLED,
        ],
      });

    if (projectId) {
      qb.andWhere('projectEvent.projectId = :projectId', {
        projectId,
      });
    }

    return qb.getCount();
  }

  markEventExplorable(eventId: string) {
    return this.repository
      .createQueryBuilder()
      .update()
      .set({
        visibility: EventVisibility.PUBLIC_EXPLORABLE,
      })
      .where('id = :eventId', { eventId })
      .execute();
  }
}
