import { AuthProvider } from '@models/auth';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ProjectScopedJwtPayload } from '@root/auth/auth.dto';
import { Auth } from '@root/auth/auth.entity';
import { BaseCrudService } from '@root/core/base-crud/base-crud.service';
import { UpdateResult } from '@root/core/dto/update-result.dto';
import { countryCodeToNameMapping } from '@root/utils/country-code.helper';
import { DeleteResult, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { IpApiService } from './../utils/ip-api.service';
import { CountryStat, UserInput } from './user.dto';
import { CookieConsent, Onboarding, User } from './user.entity';

@Injectable()
export class UserService extends BaseCrudService<User> {
  constructor(
    @InjectRepository(User)
    protected repository: Repository<User>,

    @InjectRepository(Auth)
    protected authRepository: Repository<Auth>,

    private readonly ipApiService: IpApiService,
  ) {
    super(repository);
  }

  setOnboarded(onboarding: Onboarding) {
    return `CASE 
    WHEN '${onboarding}' = ANY(onboarded) THEN onboarded 
    ELSE array_append(onboarded, '${onboarding}') 
   END`;
  }

  findById(id: string, extra?: ProjectScopedJwtPayload) {
    const qb = this.repository
      .createQueryBuilder('user')
      .select('user.id')
      .where('user.id = :id', { id });

    if (extra?.projectId) {
      qb.innerJoin(
        'user.authorizedProjects',
        'authorizedProjects',
        '"authorizedProjects"."projectId" = :projectId AND  "authorizedProjects"."userId" = "user"."id"',
        {
          projectId: extra.projectId,
        },
      ).andWhere('"authorizedProjects"."nonce" = :nonce', {
        nonce: extra.nonce,
      });
    }

    return qb.getOne();
  }

  async findAuth(provider: AuthProvider, providerId: string) {
    return this.authRepository.findOne({
      where: {
        provider,
        providerId,
      },
    });
  }

  async findConflictedUserProfile(
    provider: string,
    providerId: string,
    userId: string,
  ) {
    const auth = await this.authRepository
      .createQueryBuilder('auth')
      .select(['user.id', 'user.firstName', 'user.lastName', 'user.email'])
      .addSelect([
        'relatedAuth.isPrimary',
        'relatedAuth.username',
        'relatedAuth.firstName',
        'relatedAuth.lastName',
        'relatedAuth.verified',
        'relatedAuth.provider',
        'relatedAuth.providerId',
        'relatedAuth.picture',
        'auth.updatedAt',
      ])
      .leftJoin('auth.user', 'user')
      .leftJoinAndSelect(
        'user.auth',
        'relatedAuth',
        'relatedAuth.userId = user.id AND relatedAuth.isPrimary = true',
      )
      .where('auth.provider=:provider', { provider })
      .andWhere('auth.isPrimary = false')
      .andWhere('auth.providerId=:providerId', { providerId })
      .andWhere('auth.switchToUserId=:userId', { userId })
      .getOne();

    if (!auth) return;

    const user = auth.user;

    if (!user) return;

    const primaryAuth = auth.user.auth?.[0];

    const name = `${user.firstName}${user.lastName ? ' ' + user.lastName : ''}`;

    return {
      picture: primaryAuth?.picture || user.avatar,
      displayName: primaryAuth?.username || user.email || name || user.id,
      providerType: primaryAuth?.provider,
    };
  }

  async deleteUser(userId: string): Promise<DeleteResult> {
    return this.delete(userId);
  }

  async updateMe(userId: string, data: UserInput) {
    const user = await this.repository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    if (data.username) {
      const isAvailable = await this.isUsernameAvailable(data.username);
      if (!isAvailable) {
        throw new BadRequestException('Username is already taken');
      }
    }

    if (!data.email) {
      return this.update(userId, { ...data });
    }
    const existingAuth = await this.authRepository.findOne({
      where: [
        {
          username: data.email,
          provider: AuthProvider.MAGIC_LINK,
          userId,
        },
        {
          username: data.email,
          provider: AuthProvider.MAGIC_LINK,
          switchToUserId: userId,
        },
        {
          providerId: data.email,
          provider: AuthProvider.MAGIC_LINK,
          userId,
        },
        {
          providerId: data.email,
          provider: AuthProvider.MAGIC_LINK,
          switchToUserId: userId,
        },
      ],
    });

    if (!existingAuth) {
      throw new BadRequestException('Failed to update notification email');
    }

    return this.repository
      .createQueryBuilder()
      .update(User)
      .set({
        ...data,
        email: existingAuth.email,
        onboarded: () => this.setOnboarded(Onboarding.NOTIFICATION_EMAIL),
      })
      .where('id = :userId', { userId })
      .execute();
  }

  async updateUserOnboardingApp(
    onboarding: Onboarding,
    userId: string,
    ip: string,
  ): Promise<UpdateResult> {
    let updateDto: QueryDeepPartialEntity<User> = {
      onboarded: () => this.setOnboarded(onboarding),
    };

    if (onboarding === Onboarding.PARTICIPANT_TERMS) {
      try {
        const clientDetails = await this.ipApiService.getClientDetails(ip);
        updateDto = {
          ...updateDto,
          countryCode: clientDetails.countryCode,
        };
      } catch (error) {
        // ignore country update
      }
    }

    return this.repository
      .createQueryBuilder()
      .update(User)
      .set(updateDto)
      .where('id = :id', { id: userId })
      .execute();
  }

  async updateUserCookieConsent(
    userId: string,
    cookieConsent: CookieConsent,
  ): Promise<UpdateResult> {
    return this.repository
      .createQueryBuilder()
      .update(User)
      .set({ cookieConsent })
      .where('id = :id', { id: userId })
      .execute();
  }

  async getTopCountries(): Promise<CountryStat[]> {
    const qbData = await this.repository
      .createQueryBuilder()
      .select('"countryCode"')
      .addSelect('count(*)', 'count')
      .where('"countryCode" IS NOT NULL')
      .groupBy('"countryCode"')
      .orderBy('count', 'DESC')
      .limit(10)
      .getRawMany();
    const mappedData = qbData.map((i) => {
      return {
        count: i.count,
        countryCode: i.countryCode,
        countryName: countryCodeToNameMapping[i.countryCode],
      };
    });
    return mappedData;
  }

  private generateUsername(prefix = 'user'): string {
    // Generate a random 6 digit number between 100000 and 999999
    const randomNum = Math.floor(100000 + Math.random() * 900000);
    return `${prefix}${randomNum}`;
  }

  async getUniqueUsername(prefix = 'user'): Promise<string> {
    let username = this.generateUsername(prefix);
    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      const existingUser = await this.repository.findOne({
        where: { username },
      });
      if (!existingUser) {
        return username;
      }
      username = this.generateUsername(prefix);
      attempts++;
    }

    throw new Error('Could not generate unique username');
  }

  async isUsernameAvailable(username: string): Promise<boolean> {
    const user = await this.repository.findOne({
      where: { username },
    });
    return !user;
  }
}
