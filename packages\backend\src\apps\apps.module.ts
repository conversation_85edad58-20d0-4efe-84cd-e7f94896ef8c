import { Module } from '@nestjs/common';
import { CoreModule } from '@root/core/core.module';
import { TaskModule } from '@root/task/task.module';
import { AirboostAppModule } from './airboost/airboost-app.module';
import { AirquestAppModule } from './airquest/airquest-app.module';
import { AppsResolver } from './apps.resolver';
import { BlogAppModule } from './blog/blog-app.module';
import { CheckinAppModule } from './checkin/checkin-task.module';
import { DiscordAppModule } from './discord/discord-app.module';
import { EmailAppModule } from './email/email-app.module';
import { EvmAppModule } from './evm/evm-app.module';
import { FaucetAppModule } from './faucet/faucet-app.module';
import { FlashcardAppModule } from './flashcard/flashcard-app.module';
import { FormAppModule } from './form/form-app.module';
import { LinkAppModule } from './link/link-app.module';
import { QuizAppModule } from './quiz/quiz-app.module';
import { SubgraphAppModule } from './subgraph/subgraph-app.module';
import { SubsocialAppModule } from './subsocial/subsocial-app.module';
import { TelegramAppModule } from './telegram/telegram-app.module';
import { TermsAppModule } from './terms/terms-app.module';
import { TwitterAppModule } from './twitter/twitter-app.module';
import { UploadAppModule } from './upload/upload-app.module';
import { WalletAppModule } from './wallet/wallet-app.module';
import { SubstrateAppModule } from './substrate/substrate-app.module';
import { LuckydrawModule } from './luckydraw/luckydraw-app.module';
import { MobileAppModule } from './mobile-app/mobile-app.module';
import { SecretCodeModule } from './secret-code/secret-code.module';
import { ProducthuntAppModule } from './producthunt/producthunt.module';
import { KickstarterModule } from './kickstarter/kickstarter.module';

@Module({
  imports: [
    FormAppModule,
    LinkAppModule,
    DiscordAppModule,
    UploadAppModule,
    QuizAppModule,
    EvmAppModule,
    TelegramAppModule,
    TwitterAppModule,
    SubsocialAppModule,
    WalletAppModule,
    SubgraphAppModule,
    AirboostAppModule,
    EmailAppModule,
    TermsAppModule,
    CheckinAppModule,
    FaucetAppModule,
    TaskModule,
    CoreModule,
    AirquestAppModule,
    SubstrateAppModule,
    LuckydrawModule,
    MobileAppModule,
    SecretCodeModule,
    ProducthuntAppModule,
    BlogAppModule,
    KickstarterModule,
    FlashcardAppModule,
  ],
  providers: [AppsResolver],
})
export class AppsModule {}
