import { AuthProvider } from '@models/auth';
import { CoreEntity } from '@models/core';
import {
  Field,
  Int,
  ObjectType,
  OmitType,
  registerEnumType,
} from '@nestjs/graphql';
import { UserAuthorizedProject } from '@root/api-access/authorized-project.entity';
import { Auth } from '@root/auth/auth.entity';
import { Authorization } from '@root/authorization/authorization.entity';
import { EventConnection } from '@root/event-participation/event-connection.entity';
import { EventParticipant } from '@root/event-participation/event-participant.entity';
import { EventTemplate } from '@root/event-template/event-template.entity';
import { UserNotification } from '@root/notification/user-notification.entity';
import { ProjectFollowers } from '@root/project-followers/project-follower.entity';
import { Project } from '@root/project/project.entity';
import { ReferralCode } from '@root/referral/entities/referral-code.entity';
import { ReferredUser } from '@root/referral/entities/referred-user.entity';
import { Fuel } from '@root/xp/fuel.entity';
import { XP } from '@root/xp/xp.entity';
import { Column, Entity, OneToMany } from 'typeorm';

export enum Onboarding {
  AIRPOOLS = 'AIRPOOLS',
  AIRTOKENS = 'AIRTOKENS',
  CAMPAIGNS = 'CAMPAIGNS',
  NOTIFICATION_EMAIL = 'NOTIFICATION_EMAIL',
  PARTICIPANT_TERMS = 'PARTICIPANT_TERMS',
}

export enum CookieConsent {
  NECESSARY = 'NECESSARY',
  ACCEPT_ALL = 'ACCEPT_ALL',
}

registerEnumType(Onboarding, { name: 'Onboarding' });
registerEnumType(CookieConsent, { name: 'CookieConsent' });

@ObjectType()
export class ConflictedUserProfile {
  @Field({ nullable: true })
  picture: string;

  @Field()
  displayName: string;

  @Field(() => AuthProvider)
  providerType: AuthProvider;
}

@ObjectType()
@Entity('users')
export class User extends CoreEntity {
  @Column({ nullable: true, unique: true })
  @Field({ nullable: true }) // TODO: Migrate to nullable: false
  username: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  firstName?: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  lastName?: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  email?: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  avatar?: string;

  @Column({ default: false })
  banned?: boolean;

  @OneToMany(() => EventParticipant, (participant) => participant.user)
  eventParticipation?: EventParticipant[];

  @OneToMany(() => Project, (project) => project.user)
  projects?: Project[];

  @OneToMany(() => Auth, (auth) => auth.user)
  @Field((type) => [Auth])
  auth?: Auth[];

  @OneToMany(() => Auth, (auth) => auth.switchToUser)
  conflictedAuth?: Auth[];

  @OneToMany(() => XP, (xp) => xp.user)
  xp?: XP;

  @OneToMany(() => Fuel, (fuel) => fuel.user)
  fuel?: Fuel;

  @OneToMany(() => ProjectFollowers, (follows) => follows.user)
  follows?: ProjectFollowers[];

  @OneToMany(() => Authorization, (authorization) => authorization.user)
  authorization?: Authorization[];

  // Event Connections
  @OneToMany(() => EventConnection, (connection) => connection.user)
  eventConnections?: EventConnection[];

  // Referral
  @OneToMany(() => ReferralCode, (referralCode) => referralCode.referrerUserId)
  referralCodes?: ReferralCode[];

  @OneToMany(() => ReferredUser, (referralUser) => referralUser.referredUserId)
  referredUsers?: ReferredUser[];

  @OneToMany(() => EventTemplate, (et) => et.owner)
  eventTemplates?: EventTemplate[];

  @OneToMany(() => UserNotification, (notification) => notification.user)
  userNotifications?: UserNotification[];

  @OneToMany(() => UserAuthorizedProject, (a) => a.user)
  authorizedProjects?: UserAuthorizedProject[];

  @Field((type) => [Onboarding], { nullable: true })
  @Column({
    default: [],
    enum: Onboarding,
    type: 'enum',
    array: true,
    nullable: true,
  })
  onboarded?: Onboarding;

  @Field(() => CookieConsent, { nullable: true })
  @Column({
    nullable: true,
  })
  cookieConsent?: CookieConsent;

  @Column({
    nullable: true,
  })
  countryCode?: string;
}

@ObjectType()
export class PublicUser extends OmitType(User, ['email', 'auth']) {
  @Field(() => Int)
  rep: number;
}
