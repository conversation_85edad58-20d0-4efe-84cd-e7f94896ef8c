import {
  Field,
  GraphQLISODateTime,
  ID,
  InputType,
  ObjectType,
  PartialType,
  registerEnumType,
} from '@nestjs/graphql';
import { EVENT_URL_REGEX } from '@root/constants';
import { PaginatedData } from '@root/core/dto/pagination.dto';
import { SortInput } from '@root/core/dto/sort-input.dto';
import { IsUUID, Matches } from 'class-validator';
import {
  AccountEventVisibility,
  EventState,
  EventType,
  EventVisibility,
  LeaderboardType,
  ProjectEvent,
  PublicEventState,
  PublicEventVisibility,
} from './project-event.entity';
import { Ecosystem } from '@root/project/project.dto';

export enum ProjectEventSortKey {
  CREATED_AT = 'createdAt',
  TRENDING = 'trending',
}

registerEnumType(ProjectEventSortKey, {
  name: 'ProjectEventSortKey',
  description: 'Valid sort keys for the query',
});

@InputType()
export class ProjectEventInput {
  @Field()
  title: string;

  @Field(() => GraphQLISODateTime, { nullable: true })
  startTime?: Date;

  @Matches(EVENT_URL_REGEX, {
    message:
      'Incorrect URL. Special characters like * @ ! ( ) & # ? $ not allowed except - _',
  })
  @Field()
  publicLink: string;

  @Field(() => GraphQLISODateTime, { nullable: true })
  endTime?: Date;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  bannerUrl?: string;

  @Field(() => AccountEventVisibility, { nullable: true })
  visibility?: EventVisibility;

  @Field(() => LeaderboardType, { nullable: true })
  leaderboard?: LeaderboardType;

  @Field(() => ID, { nullable: true })
  seasonId?: string;

  @Field({ nullable: true })
  ipProtect?: boolean;

  @Field({ nullable: true })
  webhook?: string;

  @Field({ nullable: true })
  zapierWebhook?: string;

  eventType?: EventType;
}

@InputType()
export class ProjectEventUpdateInput extends PartialType(ProjectEventInput) {}

@InputType()
export class ProjectEventWhereInput {
  @Field(() => [EventState])
  state: EventState[];

  @Field(() => ID)
  @IsUUID()
  projectId: string;

  @Field(() => [AccountEventVisibility], { nullable: 'itemsAndList' })
  visibility?: AccountEventVisibility[];

  @Field((type) => [Ecosystem], { nullable: true })
  ecosystems?: Ecosystem[];

  @Field(() => [String], { nullable: 'itemsAndList' })
  tags?: string[];

  eventType?: EventType;
}

@InputType()
export class PublicProjectEventWhereInput {
  @Field(() => [PublicEventState])
  state: PublicEventState[];

  @Field(() => [PublicEventVisibility])
  visibility: PublicEventVisibility[];

  @Field(() => ID, { nullable: true })
  projectId?: string;

  @Field((type) => [Ecosystem], { nullable: true })
  ecosystems?: Ecosystem[];

  @Field(() => [String], { nullable: 'itemsAndList' })
  tags?: string[];

  @Field(() => EventType, { nullable: true })
  eventType?: EventType;
}

@ObjectType()
export class ProjectEventData extends PaginatedData {
  @Field((type) => [ProjectEvent], { nullable: 'itemsAndList' })
  data?: ProjectEvent[];
}

@InputType()
export class ProjectEventSortInput extends SortInput {
  @Field(() => ProjectEventSortKey)
  sortKey: ProjectEventSortKey;
}
